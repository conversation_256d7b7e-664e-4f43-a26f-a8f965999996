<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.pttl.mobile.manager</groupId>
    <artifactId>manager-console</artifactId>
    <version>${profileJarVersion}</version>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>1.5.6.RELEASE</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <spring.version>4.3.10.RELEASE</spring.version>
        <fastjson.version>1.2.83</fastjson.version>
        <skipTests>true</skipTests>
        <hikariCP.version>3.2.0</hikariCP.version>
        <jsoup.version>1.10.3</jsoup.version>
        <swagger2.version>2.7.0</swagger2.version>
        <servlet.version>3.1.0</servlet.version>
        <spring-data-redis.version>1.8.22.RELEASE</spring-data-redis.version>
        <mybatis-spring-boot-starter.version>1.3.2</mybatis-spring-boot-starter.version>
        <mybatis-spring.version>1.3.1</mybatis-spring.version>
        <commons-codec.version>1.11</commons-codec.version>
        <mysql-connector-java.version>5.1.45</mysql-connector-java.version>
        <druid.version>1.1.5</druid.version>
        <json-lib.version>2.4</json-lib.version>
        <lombok.version>1.16.18</lombok.version>
        <jpush-client.version>3.3.7</jpush-client.version>
        <spring-cloud-starter-sleuth.version>1.3.2.RELEASE</spring-cloud-starter-sleuth.version>
        <guava.version>18.0</guava.version>
        <joda-time.version>2.9.9</joda-time.version>
        <commons-lang3.version>3.4</commons-lang3.version>
        <mybatis-generator-core.version>1.3.2</mybatis-generator-core.version>
        <cassandra-all.version>0.8.1</cassandra-all.version>
        <hutool-all.version>5.8.8</hutool-all.version>
        <bcprov-ext-jdk15on.version>1.69</bcprov-ext-jdk15on.version>
        <zxing.version>3.5.0</zxing.version>
        <maven-compiler-plugin.version>3.1</maven-compiler-plugin.version>
        <maven-surefire-plugin.version>2.18.1</maven-surefire-plugin.version>
        <mybatis-generator-maven-plugin.version>1.3.2</mybatis-generator-maven-plugin.version>
        <kaptcha.version>2.3.2</kaptcha.version>
        <elasticsearch.version>7.6.1</elasticsearch.version>
        <logback.version>1.2.9</logback.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-tx</artifactId>
            <version>${spring.version}</version>
        </dependency>
        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
            <version>${mybatis-spring-boot-starter.version}</version>
        </dependency>
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis-spring</artifactId>
            <version>${mybatis-spring.version}</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/com.github.pagehelper/pagehelper-spring-boot-starter -->
        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper-spring-boot-starter</artifactId>
            <version>1.4.5</version>
        </dependency>

        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
            <version>${commons-codec.version}</version>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>${mysql-connector-java.version}</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid</artifactId>
            <version>${druid.version}</version>
        </dependency>
        <dependency>
            <groupId>net.sf.json-lib</groupId>
            <artifactId>json-lib</artifactId>
            <version>${json-lib.version}</version>
            <classifier>jdk15</classifier>
        </dependency>
        <dependency>
            <groupId>com.zaxxer</groupId>
            <artifactId>HikariCP</artifactId>
            <version>${hikariCP.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>${fastjson.version}</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>cn.jpush.api</groupId>
            <artifactId>jpush-client</artifactId>
            <version>${jpush-client.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-sleuth</artifactId>
            <version>${spring-cloud-starter-sleuth.version}</version>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>${guava.version}</version>
        </dependency>
        <dependency>
            <groupId>joda-time</groupId>
            <artifactId>joda-time</artifactId>
            <version>${joda-time.version}</version>
        </dependency>
        <dependency>
            <groupId>org.jsoup</groupId>
            <artifactId>jsoup</artifactId>
            <version>${jsoup.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>${commons-lang3.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.cassandra</groupId>
            <artifactId>cassandra-all</artifactId>
            <version>${cassandra-all.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--<dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>RELEASE</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>RELEASE</version>
        </dependency>-->

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>3.1.1</version>
        </dependency>
        <!-- 解决easy excel 对高版本ehcache的依赖问题-->
        <!-- 避免 Exception: not initialize class com.alibaba.excel.cache.Ehcache-->
        <dependency>
            <groupId>org.ehcache</groupId>
            <artifactId>ehcache</artifactId>
            <version>3.4.0</version>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>${hutool-all.version}</version>
        </dependency>
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-ext-jdk15on</artifactId>
            <version>${bcprov-ext-jdk15on.version}</version>
        </dependency>
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk15on</artifactId>
            <version>${bcprov-ext-jdk15on.version}</version>
        </dependency>
        <!-- Swagger2 -->
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
            <version>${swagger2.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger-ui</artifactId>
            <version>${swagger2.version}</version>
            <scope>compile</scope>
        </dependency>
        <!-- Servlet -->
        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>javax.servlet-api</artifactId>
            <version>${servlet.version}</version>
            <scope>compile</scope>
        </dependency>
        <!-- jedis -->
        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
            <version>${jedis.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.data</groupId>
            <artifactId>spring-data-redis</artifactId>
            <version>${spring-data-redis.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>
        <!--spring-session-->
        <dependency>
            <groupId>org.springframework.session</groupId>
            <artifactId>spring-session-data-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.session</groupId>
            <artifactId>spring-session-jdbc</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mybatis.generator</groupId>
            <artifactId>mybatis-generator-core</artifactId>
            <version>${mybatis-generator-core.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.github.penggle</groupId>
            <artifactId>kaptcha</artifactId>
            <version>${kaptcha.version}</version>
        </dependency>

        <!-- jwt -->
        <dependency>
            <groupId>com.auth0</groupId>
            <artifactId>java-jwt</artifactId>
            <version>4.0.0</version>
        </dependency>

        <!--过滤加载配置-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>

        <!--es依赖-->
        <dependency>
            <groupId>org.elasticsearch</groupId>
            <artifactId>elasticsearch</artifactId>
            <version>${elasticsearch.version}</version>
        </dependency>
        <dependency>
            <groupId>org.elasticsearch.client</groupId>
            <artifactId>elasticsearch-rest-high-level-client</artifactId>
            <version>${elasticsearch.version}</version>
        </dependency>

        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>core</artifactId>
            <version>${zxing.version}</version>
        </dependency>

        <!--升级logback版本为1.2.9 排除slf4j的漏洞-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-logging</artifactId>
            <version>1.5.6.RELEASE</version>
            <exclusions>
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
                    <artifactId>logback-classic</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
            <version>${logback.version}</version>
        </dependency>

    </dependencies>

    <build>
        <finalName>${project.artifactId}-${project.version}</finalName>

        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>mapper/**/*.xml</include>
                    <include>com/pttl/mobile/manager/mapper/**/*Mapper.xml</include>
                    <include>mapper/*.xml</include>
                    <include>static/**</include>
                    <include>templates/**</include>
                    <include>spy.properties</include>
                    <include>logback-spring.xml</include>
                    <include>cert/*.jks</include>
                    <include>template/*.xlsx</include>
                </includes>
            </resource>

            <!-- 过滤配置文件到config目录 -->
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <targetPath>config</targetPath>
                <includes>
                    <include>application.yml</include>
                </includes>
            </resource>

            <!-- 过滤ssl文件到config目录 -->
            <resource>
                <directory>src/main/resources/cert</directory>
                <filtering>true</filtering>
                <targetPath>config</targetPath>
                <includes>
                    <include>*.jks</include>
                    <include>*.p12</include>
                </includes>
            </resource>

            <!-- 过滤脚本文件到bin目录 -->
            <resource>
                <directory>./bin</directory>
                <filtering>true</filtering>
                <targetPath>bin</targetPath>
                <includes>
                    <include>*.bat</include>
                    <include>*.sh</include>
                </includes>
            </resource>

            <!-- 过滤脚本文件到template目录 -->
            <resource>
                <directory>src/main/resources/template</directory>
                <filtering>true</filtering>
                <targetPath>template</targetPath>
                <includes>
                    <include>*.xlsx</include>
                </includes>
            </resource>
        </resources>


        <plugins>
            <!--防止maven在编译的时候会对其他文件改写里面的内容-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>3.3.0</version>
                <configuration>
                    <encoding>UTF-8</encoding>
                    <!-- 过滤后缀为pem、pfx的证书文件 -->
                    <nonFilteredFileExtensions>
                        <nonFilteredFileExtension>cer</nonFilteredFileExtension>
                        <nonFilteredFileExtension>pem</nonFilteredFileExtension>
                        <nonFilteredFileExtension>pfx</nonFilteredFileExtension>
                        <nonFilteredFileExtension>jks</nonFilteredFileExtension>
                        <nonFilteredFileExtension>p12</nonFilteredFileExtension>
                        <nonFilteredFileExtension>xlsx</nonFilteredFileExtension>
                        <nonFilteredFileExtension>xls</nonFilteredFileExtension>
                    </nonFilteredFileExtensions>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <includeSystemScope>true</includeSystemScope>
                </configuration>
            </plugin>

            <!--打入tar内部-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>3.1.1</version>
                <configuration>
                    <excludes>
                        <exclude>bin/**</exclude>
                        <exclude>config/**</exclude>
                        <exclude>template/**</exclude>
                    </excludes>
                </configuration>
            </plugin>

            <!-- 打包插件 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-assembly-plugin</artifactId>
                <version>3.4.2</version>
                <executions>
                    <execution>
                        <!-- 打实机部署的tar.gz的包 -->
                        <configuration>
                            <descriptors>
                                <descriptor>./assembly/assembly.xml</descriptor>
                            </descriptors>
                        </configuration>
                        <id>raw-deploy-assembly</id>
                        <phase>package</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <!--原代码生成插件-->
            <plugin>
                <!--Mybatis-generator插件,用于自动生成Mapper和POJO-->
                <groupId>org.mybatis.generator</groupId>
                <artifactId>mybatis-generator-maven-plugin</artifactId>
                <version>${mybatis-generator-maven-plugin.version}</version>
                <configuration>
                    <!--配置文件的位置-->
                    <configurationFile>src/main/resources/generatorConfig.xml</configurationFile>
                    <verbose>true</verbose>
                    <overwrite>true</overwrite>
                </configuration>
                <dependencies>
                    <dependency>
                        <groupId>org.mybatis.generator</groupId>
                        <artifactId>mybatis-generator-core</artifactId>
                        <version>${mybatis-generator-core.version}</version>
                    </dependency>
                    <dependency>
                        <groupId>mysql</groupId>
                        <artifactId>mysql-connector-java</artifactId>
                        <version>${mysql-connector-java.version}</version>
                    </dependency>
                </dependencies>
            </plugin>
        </plugins>
    </build>

    <!--MAVEN打包选择运行环境-->
    <!-- 1:local(默认) 本地 2:dev:开发环境 3:test 4:uat 用户验收测试 5.pro:生产环境
     6.k8s:主要区别为logback配置-->
    <profiles>
        <profile>
            <id>local</id>
            <properties>
                <profileActive>local</profileActive>
                <profileJarVersion>1.0.0.SNAPSHOT</profileJarVersion>
            </properties>
            <build>
                <resources>
                    <resource>
                        <targetPath>config</targetPath>
                        <directory>src/profiles/local</directory>
                        <includes>
                            <include>*.yml</include>
                        </includes>
                    </resource>
                    <resource>
                        <directory>src/profiles/local</directory>
                        <includes>
                            <include>*.xml</include>
                        </includes>
                    </resource>
                </resources>
            </build>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>test</id>
            <properties>
                <profileActive>test</profileActive>
                <profileJarVersion>1.0.0.SNAPSHOT</profileJarVersion>
            </properties>
            <build>
                <resources>
                    <resource>
                        <targetPath>config</targetPath>
                        <directory>src/profiles/test</directory>
                        <includes>
                            <include>*.yml</include>
                        </includes>
                    </resource>
                    <resource>
                        <directory>src/profiles/test</directory>
                        <includes>
                            <include>*.xml</include>
                        </includes>
                    </resource>
                </resources>
            </build>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>test45</id>
            <properties>
                <profileActive>test45</profileActive>
                <!--
                    为了方便测试打包 这里新增jar版本号指定 因45调用的也是测试环境176的服务
                 but 176上已经存在1.0.0 故在这里做 区分
                 -->
                <profileJarVersion>1.0.1.SNAPSHOT</profileJarVersion>
            </properties>
            <build>
                <resources>
                    <resource>
                        <targetPath>config</targetPath>
                        <directory>src/profiles/test45</directory>
                        <includes>
                            <include>*.yml</include>
                        </includes>
                    </resource>
                    <resource>
                        <directory>src/profiles/test45</directory>
                        <includes>
                            <include>*.xml</include>
                        </includes>
                    </resource>
                </resources>
            </build>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>uat</id>
            <properties>
                <profileActive>uat</profileActive>
                <profileJarVersion>1.0.0.SNAPSHOT</profileJarVersion>
            </properties>
            <build>
                <resources>
                    <resource>
                        <targetPath>config</targetPath>
                        <directory>src/profiles/uat</directory>
                        <includes>
                            <include>*.yml</include>
                        </includes>
                    </resource>
                    <resource>
                        <directory>src/profiles/uat</directory>
                        <includes>
                            <include>*.xml</include>
                        </includes>
                    </resource>
                </resources>
            </build>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
        </profile>

        <!-- 为k8s配置的logback配置 方便抓取日志 -->
        <profile>
            <id>k8s</id>
            <properties>
                <profileActive>k8s</profileActive>
                <profileJarVersion>1.0.0.SNAPSHOT</profileJarVersion>
            </properties>
            <build>
                <resources>
                    <resource>
                        <targetPath>config</targetPath>
                        <directory>src/profiles/k8s</directory>
                        <includes>
                            <include>*.yml</include>
                        </includes>
                    </resource>
                    <resource>
                        <directory>src/profiles/k8s</directory>
                        <includes>
                            <include>*.xml</include>
                        </includes>
                    </resource>
                </resources>
            </build>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
        </profile>
    </profiles>
</project>