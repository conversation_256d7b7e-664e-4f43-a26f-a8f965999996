# 创建es document
# 移动端上报日志 doc

PUT mobile-record-log
{
  "mappings": {
      "properties": {
        "appVersion": {
          "type": "keyword"
        },
        "description": {
          "type": "keyword"
        },
        "loginName": {
          "type": "keyword"
        },
        "loginTime": {
          "type": "date",
          "format": "yyyy‐MM‐dd HH:mm:ss||yyyy‐MM‐dd||epoch_millis"
        },
        "model": {
          "type": "keyword"
        },
        "module": {
          "type": "keyword"
        },
        "occurTime": {
          "type": "date",
          "format": "yyyy‐MM‐dd HH:mm:ss||yyyy‐MM‐dd||epoch_millis"
        },
        "platform": {
          "type": "keyword"
        },
        "source": {
          "type": "keyword"
        },
        "subModule": {
          "type": "keyword"
        },
        "systemLoginName": {
          "type": "keyword"
        },
        "timeUsed": {
          "type": "long"
        },
        "type": {
          "type": "keyword"
        }
      }
  }
}