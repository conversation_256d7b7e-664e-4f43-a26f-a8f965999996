<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">
<generatorConfiguration>
    <properties resource="generatorMysql.properties"/>
    <context id="mysqlTables" targetRuntime="MyBatis3">
        <property name="javaFileEncoding" value="UTF-8"/>
        <plugin type="org.mybatis.generator.plugins.SerializablePlugin"></plugin>
        <!--<commentGenerator type="ManagerCommentGenerator">-->
            <!--&lt;!&ndash;  关闭自动生成的注释&ndash;&gt;-->
            <!--<property name="suppressAllComments" value="false"/>-->
        <!--</commentGenerator>-->
        <jdbcConnection driverClass="com.mysql.jdbc.Driver"
                        connectionURL="${jdbc.url}" userId="${jdbc.username}" password="${jdbc.password}">
            <!-- 针对mysql数据库 -->
            <property name="useInformationSchema" value="true"></property>
        </jdbcConnection>
        <!--&lt;!&ndash;指定生成的类型为java类型，避免数据库中number等类型字段 &ndash;&gt;-->
        <!--<javaTypeResolver type="ManagerJavaTypeResolverDefaultImpl">-->
            <!--<property name="forceBigDecimals" value="false"/>-->
        <!--</javaTypeResolver>-->

        <!--自动生成的实体的存放包路径 -->
        <javaModelGenerator targetPackage="com.pttl.mobile.manager.domain.po"
                            targetProject="src/main/java">
            <property name="enableSubPackages" value="true"/>
            <property name="trimStrings" value="true"/>
        </javaModelGenerator>
        <!--自动生成的*Mapper.xml文件存放路径 -->
        <sqlMapGenerator targetPackage="com.pttl.mobile.manager.mapper.base"
                         targetProject="src/main/resources">
            <property name="enableSubPackages" value="true"/>
        </sqlMapGenerator>
        <!--自动生成的*Mapper.java存放路径 -->
        <javaClientGenerator type="XMLMAPPER"
                             targetPackage="com.pttl.mobile.manager.dao" targetProject="src/main/java">
            <property name="enableSubPackages" value="true"/>
        </javaClientGenerator>

        <!--<table tableName="tlmall_business_delegate" domainObjectName="BusinessDelegate"
               enableCountByExample="false" enableUpdateByExample="false"
               enableDeleteByExample="false" enableSelectByExample="false"
               selectByExampleQueryId="false">
        </table>-->
        <table tableName="application" domainObjectName="Application"
               enableCountByExample="false" enableUpdateByExample="false"
               enableDeleteByExample="false" enableSelectByExample="false"
               selectByExampleQueryId="false">
        </table>

        <table tableName="application_adapter" domainObjectName="ApplicationAdapter"
               enableCountByExample="false" enableUpdateByExample="false"
               enableDeleteByExample="false" enableSelectByExample="false"
               selectByExampleQueryId="false">
        </table>

        <table tableName="application_group" domainObjectName="ApplicationGroup"
               enableCountByExample="false" enableUpdateByExample="false"
               enableDeleteByExample="false" enableSelectByExample="false"
               selectByExampleQueryId="false">
        </table>

        <table tableName="configuration" domainObjectName="Configuration"
               enableCountByExample="false" enableUpdateByExample="false"
               enableDeleteByExample="false" enableSelectByExample="false"
               selectByExampleQueryId="false">
        </table>

        <table tableName="operating_system" domainObjectName="OperatingSystem"
               enableCountByExample="false" enableUpdateByExample="false"
               enableDeleteByExample="false" enableSelectByExample="false"
               selectByExampleQueryId="false">
        </table>

        <table tableName="release" domainObjectName="Release"
               enableCountByExample="false" enableUpdateByExample="false"
               enableDeleteByExample="false" enableSelectByExample="false"
               selectByExampleQueryId="false">
        </table>

        <table tableName="release_type" domainObjectName="ReleaseType"
               enableCountByExample="false" enableUpdateByExample="false"
               enableDeleteByExample="false" enableSelectByExample="false"
               selectByExampleQueryId="false">
        </table>

        <table tableName="runtime" domainObjectName="Runtime"
               enableCountByExample="false" enableUpdateByExample="false"
               enableDeleteByExample="false" enableSelectByExample="false"
               selectByExampleQueryId="false">
        </table>

        <table tableName="swa" domainObjectName="Swa"
               enableCountByExample="false" enableUpdateByExample="false"
               enableDeleteByExample="false" enableSelectByExample="false"
               selectByExampleQueryId="false">
        </table>

        <table tableName="swa_keeper" domainObjectName="SwaKeeper"
               enableCountByExample="false" enableUpdateByExample="false"
               enableDeleteByExample="false" enableSelectByExample="false"
               selectByExampleQueryId="false">
        </table>

        <table tableName="system_platform" domainObjectName="SystemPlatform"
               enableCountByExample="false" enableUpdateByExample="false"
               enableDeleteByExample="false" enableSelectByExample="false"
               selectByExampleQueryId="false">
        </table>
    </context>
</generatorConfiguration>