spring:
  application:
    name: @artifactId@
  profiles:
    active: @profileActive@
  http:
    multipart:
      max-request-size: 100MB
      max-file-size: 10MB
      # 指定上传文件临时目录 报temporary upload location is not valid异常(上传附件报错找不到临时目录)
      location: /home/<USER>/xiBel/manager/tmp
management:
  security:
    enabled: false

mybatis:
  # 配置日志输出
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    # 开启驼峰命名
    map-underscore-to-camel-case: true
  # 配置pageHelp
  configuration-properties:
    helperDialect: mysql
    offsetAsPageNum: true
    rowBoundsWithCount: true
    reasonable: true
  # 配置xml路径
  mapper-locations:
    - classpath*:com/pttl/mobile/manager/mapper/**/*Mapper.xml
    - classpath*:mapper/*.xml

# 配置pageHelp
pagehelper:
  helperDialect: mysql
  reasonable: true
  supportMethodsArguments: true
  params: count=countSql

mobile:
  whitelist:
    - /user/login
    - /user/logout
    - /user/redirectToHome
    - /code/kaptcha
    - /client/release/lastversion
    - /client/configuration
    - /client/application
    - /client/application/manifest
    - /client/swa
    - /mobile/tp/getCookie
  clientlist:
    - /client/release/lastversion
    - /client/configuration
    - /client/application
    - /client/application/manifest
    - /client/swa

  # 为不改变原来的拦截逻辑 新增拦截列表
  mobilelist:
    - /mobile/login

# excel模板 本地路径
excelFile:
  path: /home/<USER>/xiBel/manager/@build.finalName@/userTemplate.xlsx