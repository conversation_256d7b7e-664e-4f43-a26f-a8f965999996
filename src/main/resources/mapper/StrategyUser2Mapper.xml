<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pttl.mobile.manager.dao.StrategyUser2Mapper">
  <resultMap id="BaseResultMap" type="com.pttl.mobile.manager.domain.entity.StrategyUser2DO">
    
    <!--@Table mm_strategy_user-->
    <id column="id" property="id" />
    <result column="strategy_id" property="strategyId" />
    <result column="user_id" property="userId" />
  </resultMap>
  <sql id="Base_Column_List">
    
    id, strategy_id, user_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    
    select 
    <include refid="Base_Column_List" />
    from mm_strategy_user
    where id = #{id}
  </select>
  
  <select id="countByStrategyId" resultType="java.lang.Integer">
    select
    count(id)
    from mm_strategy_user
    where strategy_id = #{strategyId}
  </select>

  <!--根据策略id 获取关联用户信息-->
  <select id="listByStrategyId" resultType="com.pttl.mobile.manager.domain.entity.UserDO">
        SELECT
            id,
            department_id as departmentId,
            `name`,
            `password`,
            email,
            mobile,
            login_name,
            position_name,
            modify_password_wrong_count,
            modify_password_wrong_time,
            avatar_path,
            employee_no,
            person_level,
            locality,
            telephone,
            `source`,
            valid_status,
            activated_status,
            activated_time,
            disabled_status,
            last_login_time,
            failed_login_times,
            device_num,
            device_switch_status,
            temp_device_num,
            temp_device_num_expire_time,
            create_time,
            update_time,
            ad_dn,
            remark,
            extension1,
            extension2,
            extension3
        FROM
            mm_user
        WHERE
            id IN (
            SELECT
                su.user_id
            FROM
                mm_strategy_user su
            WHERE
            su.strategy_id = #{strategyId}
            )
   </select>

   <select id="userStrategyByUserId" resultType="com.pttl.mobile.manager.domain.dto.UserStrategyInfoDTO" parameterType="java.lang.Long">
        SELECT
            ms.*,
            ( SELECT COUNT( id ) FROM mm_strategy_application msa WHERE ms.id = msa.strategy_id ) AS applicationCount
        FROM
            mm_strategy ms
        WHERE
            ms.id IN (
            SELECT
                strategy_id
            FROM
                mm_strategy_user
        WHERE
            user_id = #{userId})
   </select>
  
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    
    delete from mm_strategy_user
    where id = #{id}
  </delete>

  <!--根据策略id删除-->
  <delete id="deleteByStrategyId" parameterType="java.lang.Long">
        delete from mm_strategy_user
    where strategy_id = #{strategyId}
  </delete>

  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.pttl.mobile.manager.domain.entity.StrategyUser2DO" useGeneratedKeys="true">
    
    insert into mm_strategy_user (strategy_id, user_id)
    values (#{strategyId}, #{userId})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.pttl.mobile.manager.domain.entity.StrategyUser2DO" useGeneratedKeys="true">
    
    insert into mm_strategy_user
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="strategyId != null">
        strategy_id,
      </if>
      <if test="userId != null">
        user_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="strategyId != null">
        #{strategyId},
      </if>
      <if test="userId != null">
        #{userId},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.pttl.mobile.manager.domain.entity.StrategyUser2DO">
    
    update mm_strategy_user
    <set>
      <if test="strategyId != null">
        strategy_id = #{strategyId},
      </if>
      <if test="userId != null">
        user_id = #{userId},
      </if>
    </set>
    where id = #{id}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.pttl.mobile.manager.domain.entity.StrategyUser2DO">
    
    update mm_strategy_user
    set strategy_id = #{strategyId},
      user_id = #{userId}
    where id = #{id}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    
    update mm_strategy_user
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="strategy_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.strategyId}
        </foreach>
      </trim>
      <trim prefix="user_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.userId}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    
    update mm_strategy_user
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="strategy_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.strategyId != null">
            when id = #{item.id} then #{item.strategyId}
          </if>
        </foreach>
      </trim>
      <trim prefix="user_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.userId != null">
            when id = #{item.id} then #{item.userId}
          </if>
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    
    insert into mm_strategy_user
    (strategy_id, user_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.strategyId}, #{item.userId})
    </foreach>
  </insert>
</mapper>