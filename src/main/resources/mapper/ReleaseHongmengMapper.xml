<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pttl.mobile.manager.dao.ReleaseHongmengMapper">
  <resultMap id="BaseResultMap" type="com.pttl.mobile.manager.domain.po.Release">
    
    <!--@Table release_hongmeng-->
    <id column="id" jdbcType="VARCHAR" property="id" />
    <result column="version" jdbcType="VARCHAR" property="version" />
    <result column="download_url" jdbcType="VARCHAR" property="downloadUrl" />
    <result column="system_platform" jdbcType="TINYINT" property="systemPlatform" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="operating_system" jdbcType="TINYINT" property="operatingSystem" />
    <result column="is_forced" jdbcType="BOOLEAN" property="isForced" />
    <result column="release_date" jdbcType="TIMESTAMP" property="releaseDate" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="env" jdbcType="TINYINT" property="env" />
  </resultMap>

  <resultMap id="BaseResultMapExtend" extends="BaseResultMap"
             type="com.pttl.mobile.manager.domain.dto.ReleaseExtendDTO">

  </resultMap>
  <sql id="Base_Column_List">
    
    id, version, download_url, system_platform, `type`, operating_system, is_forced, 
    release_date, description, env
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    
    select 
    <include refid="Base_Column_List" />
    from release_hongmeng
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    
    delete from release_hongmeng
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.pttl.mobile.manager.domain.po.Release">
    
    insert into release_hongmeng (id, version, download_url, 
      system_platform, `type`, operating_system, 
      is_forced, release_date, description, 
      env)
    values (#{id,jdbcType=VARCHAR}, #{version,jdbcType=VARCHAR}, #{downloadUrl,jdbcType=VARCHAR}, 
      #{systemPlatform,jdbcType=TINYINT}, #{type,jdbcType=TINYINT}, #{operatingSystem,jdbcType=TINYINT}, 
      #{isForced,jdbcType=BOOLEAN}, #{releaseDate,jdbcType=TIMESTAMP}, #{description,jdbcType=VARCHAR}, 
      #{env,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="com.pttl.mobile.manager.domain.entity.ReleaseHongmeng">
    
    insert into release_hongmeng
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null and id != ''">
        id,
      </if>
      <if test="version != null and version != ''">
        version,
      </if>
      <if test="downloadUrl != null and downloadUrl != ''">
        download_url,
      </if>
      <if test="systemPlatform != null">
        system_platform,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="operatingSystem != null">
        operating_system,
      </if>
      <if test="isForced != null">
        is_forced,
      </if>
      <if test="releaseDate != null">
        release_date,
      </if>
      <if test="description != null and description != ''">
        description,
      </if>
      <if test="env != null">
        env,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null and id != ''">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="version != null and version != ''">
        #{version,jdbcType=VARCHAR},
      </if>
      <if test="downloadUrl != null and downloadUrl != ''">
        #{downloadUrl,jdbcType=VARCHAR},
      </if>
      <if test="systemPlatform != null">
        #{systemPlatform,jdbcType=TINYINT},
      </if>
      <if test="type != null">
        #{type,jdbcType=TINYINT},
      </if>
      <if test="operatingSystem != null">
        #{operatingSystem,jdbcType=TINYINT},
      </if>
      <if test="isForced != null">
        #{isForced,jdbcType=BOOLEAN},
      </if>
      <if test="releaseDate != null">
        #{releaseDate,jdbcType=TIMESTAMP},
      </if>
      <if test="description != null and description != ''">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="env != null">
        #{env,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.pttl.mobile.manager.domain.entity.ReleaseHongmeng">
    
    update release_hongmeng
    <set>
      <if test="version != null and version != ''">
        version = #{version,jdbcType=VARCHAR},
      </if>
      <if test="downloadUrl != null and downloadUrl != ''">
        download_url = #{downloadUrl,jdbcType=VARCHAR},
      </if>
      <if test="systemPlatform != null">
        system_platform = #{systemPlatform,jdbcType=TINYINT},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=TINYINT},
      </if>
      <if test="operatingSystem != null">
        operating_system = #{operatingSystem,jdbcType=TINYINT},
      </if>
      <if test="isForced != null">
        is_forced = #{isForced,jdbcType=BOOLEAN},
      </if>
      <if test="releaseDate != null">
        release_date = #{releaseDate,jdbcType=TIMESTAMP},
      </if>
      <if test="description != null and description != ''">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="env != null">
        env = #{env,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.pttl.mobile.manager.domain.entity.ReleaseHongmeng">
    
    update release_hongmeng
    set version = #{version,jdbcType=VARCHAR},
      download_url = #{downloadUrl,jdbcType=VARCHAR},
      system_platform = #{systemPlatform,jdbcType=TINYINT},
      `type` = #{type,jdbcType=TINYINT},
      operating_system = #{operatingSystem,jdbcType=TINYINT},
      is_forced = #{isForced,jdbcType=BOOLEAN},
      release_date = #{releaseDate,jdbcType=TIMESTAMP},
      description = #{description,jdbcType=VARCHAR},
      env = #{env,jdbcType=TINYINT}
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    
    update release_hongmeng
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="version = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=VARCHAR} then #{item.version,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="download_url = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=VARCHAR} then #{item.downloadUrl,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="system_platform = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=VARCHAR} then #{item.systemPlatform,jdbcType=TINYINT}
        </foreach>
      </trim>
      <trim prefix="`type` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=VARCHAR} then #{item.type,jdbcType=TINYINT}
        </foreach>
      </trim>
      <trim prefix="operating_system = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=VARCHAR} then #{item.operatingSystem,jdbcType=TINYINT}
        </foreach>
      </trim>
      <trim prefix="is_forced = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=VARCHAR} then #{item.isForced,jdbcType=BOOLEAN}
        </foreach>
      </trim>
      <trim prefix="release_date = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=VARCHAR} then #{item.releaseDate,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="description = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=VARCHAR} then #{item.description,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="env = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=VARCHAR} then #{item.env,jdbcType=TINYINT}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=VARCHAR}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    
    update release_hongmeng
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="version = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.version != null">
            when id = #{item.id,jdbcType=VARCHAR} then #{item.version,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="download_url = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.downloadUrl != null">
            when id = #{item.id,jdbcType=VARCHAR} then #{item.downloadUrl,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="system_platform = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.systemPlatform != null">
            when id = #{item.id,jdbcType=VARCHAR} then #{item.systemPlatform,jdbcType=TINYINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="`type` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.type != null">
            when id = #{item.id,jdbcType=VARCHAR} then #{item.type,jdbcType=TINYINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="operating_system = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.operatingSystem != null">
            when id = #{item.id,jdbcType=VARCHAR} then #{item.operatingSystem,jdbcType=TINYINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="is_forced = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isForced != null">
            when id = #{item.id,jdbcType=VARCHAR} then #{item.isForced,jdbcType=BOOLEAN}
          </if>
        </foreach>
      </trim>
      <trim prefix="release_date = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.releaseDate != null">
            when id = #{item.id,jdbcType=VARCHAR} then #{item.releaseDate,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="description = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.description != null">
            when id = #{item.id,jdbcType=VARCHAR} then #{item.description,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="env = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.env != null">
            when id = #{item.id,jdbcType=VARCHAR} then #{item.env,jdbcType=TINYINT}
          </if>
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=VARCHAR}
    </foreach>
  </update>
  <insert id="batchInsert" parameterType="map">
    
    insert into release_hongmeng
    (id, version, download_url, system_platform, `type`, operating_system, is_forced, 
      release_date, description, env)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id,jdbcType=VARCHAR}, #{item.version,jdbcType=VARCHAR}, #{item.downloadUrl,jdbcType=VARCHAR}, 
        #{item.systemPlatform,jdbcType=TINYINT}, #{item.type,jdbcType=TINYINT}, #{item.operatingSystem,jdbcType=TINYINT}, 
        #{item.isForced,jdbcType=BOOLEAN}, #{item.releaseDate,jdbcType=TIMESTAMP}, #{item.description,jdbcType=VARCHAR}, 
        #{item.env,jdbcType=TINYINT})
    </foreach>
  </insert>

  <select id="getReleaseHistory" resultMap="BaseResultMapExtend">
    SELECT
    re.id,
    re.version,
    re.download_url,
    re.system_platform,
    re.type,
    re.operating_system,
    re.is_forced,
    re.release_date,
    re.description,
    re.env,
    sp.name AS systemPlatformName,
    rt.name AS typeName,
    os.name AS operatingSystemName
    FROM `release_hongmeng` AS re
    LEFT JOIN system_platform AS sp
    ON sp.id=re.system_platform
    LEFT JOIN release_type AS rt
    ON rt.id=re.type
    LEFT JOIN operating_system AS os
    ON os.id=re.operating_system
    <where>
      <if test="type != null">and re.type=#{type}</if>
      <if test="true">and re.system_platform=${@com.pttl.mobile.manager.lib.ContextUtil@systemPlatform()}</if>
      <if test="true">and re.env=${@com.pttl.mobile.manager.lib.ContextUtil@currentEnv()}</if>
      <if test="version != null and version.length>0">and re.version=#{version}</if>
      <if test="keyword != null and keyword.length>0">and re.description like CONCAT('%',#{keyword},'%')</if>
      <if test="start_date != null">and re.release_date&gt;=#{start_date}</if>
      <if test="end_date != null">and re.release_date&lt;=#{end_date}</if>
    </where>
    order by re.release_date desc
  </select>
  <select id="isVersionExists" resultType="java.lang.Integer">
    select
    count(1)
    from `release_hongmeng`
    where type = #{type}
    and system_platform = ${@com.pttl.mobile.manager.lib.ContextUtil@systemPlatform()}
    and env = ${@com.pttl.mobile.manager.lib.ContextUtil@currentEnv()}
    and operating_system = #{operating_system}
    and version = #{version}
  </select>
  <select id="getReleaseLastVersion" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from `release_hongmeng`
    <where>
      system_platform=${@com.pttl.mobile.manager.lib.ContextUtil@systemPlatform()}
      and env=${@com.pttl.mobile.manager.lib.ContextUtil@currentEnv()}
      <if test="type != null">and type=#{type}</if>
      <if test="operating_system != null">and operating_system=#{operating_system}</if>
    </where>
    ORDER BY release_date DESC LIMIT 1
  </select>
</mapper>