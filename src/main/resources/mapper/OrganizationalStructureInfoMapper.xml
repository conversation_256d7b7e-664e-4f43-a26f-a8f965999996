<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pttl.mobile.manager.mobile.ws.dao.OrganizationalStructureInfoMapper">
    <resultMap id="BaseResultMap" type="com.pttl.mobile.manager.mobile.ws.entity.OrganizationalStructureInfoDO">
        <!--@Table mm_ws_organizational_structure_info-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="superior_dept_abbreviation" jdbcType="VARCHAR" property="superiorDeptAbbreviation"/>
        <result column="part_deptId_chn" jdbcType="INTEGER" property="partDeptIdChn"/>
        <result column="dept_id" jdbcType="INTEGER" property="deptId"/>
        <result column="row_number" jdbcType="INTEGER" property="rowNumber"/>
        <result column="dept_abbreviation" jdbcType="VARCHAR" property="deptAbbreviation"/>
    </resultMap>
    <sql id="Base_Column_List">
        id,
        superior_dept_abbreviation,
        part_deptId_chn,
        dept_id,
        `row_number`,
        dept_abbreviation
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mm_ws_organizational_structure_info
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from mm_ws_organizational_structure_info
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="com.pttl.mobile.manager.mobile.ws.entity.OrganizationalStructureInfoDO"
            useGeneratedKeys="true">
        insert into mm_ws_organizational_structure_info (superior_dept_abbreviation, part_deptId_chn,
                                                         dept_id, `row_number`, dept_abbreviation)
        values (#{superiorDeptAbbreviation,jdbcType=VARCHAR}, #{partDeptIdChn,jdbcType=INTEGER},
                #{deptId,jdbcType=INTEGER}, #{rowNumber,jdbcType=INTEGER}, #{deptAbbreviation,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.pttl.mobile.manager.mobile.ws.entity.OrganizationalStructureInfoDO"
            useGeneratedKeys="true">
        insert into mm_ws_organizational_structure_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="superiorDeptAbbreviation != null">
                superior_dept_abbreviation,
            </if>
            <if test="partDeptIdChn != null">
                part_deptId_chn,
            </if>
            <if test="deptId != null">
                dept_id,
            </if>
            <if test="rowNumber != null">
                `row_number`,
            </if>
            <if test="deptAbbreviation != null">
                dept_abbreviation,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="superiorDeptAbbreviation != null">
                #{superiorDeptAbbreviation,jdbcType=VARCHAR},
            </if>
            <if test="partDeptIdChn != null">
                #{partDeptIdChn,jdbcType=INTEGER},
            </if>
            <if test="deptId != null">
                #{deptId,jdbcType=INTEGER},
            </if>
            <if test="rowNumber != null">
                #{rowNumber,jdbcType=INTEGER},
            </if>
            <if test="deptAbbreviation != null">
                #{deptAbbreviation,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.pttl.mobile.manager.mobile.ws.entity.OrganizationalStructureInfoDO">
        update mm_ws_organizational_structure_info
        <set>
            <if test="superiorDeptAbbreviation != null">
                superior_dept_abbreviation = #{superiorDeptAbbreviation,jdbcType=VARCHAR},
            </if>
            <if test="partDeptIdChn != null">
                part_deptId_chn = #{partDeptIdChn,jdbcType=INTEGER},
            </if>
            <if test="deptId != null">
                dept_id = #{deptId,jdbcType=INTEGER},
            </if>
            <if test="rowNumber != null">
                `row_number` = #{rowNumber,jdbcType=INTEGER},
            </if>
            <if test="deptAbbreviation != null">
                dept_abbreviation = #{deptAbbreviation,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey"
            parameterType="com.pttl.mobile.manager.mobile.ws.entity.OrganizationalStructureInfoDO">
        update mm_ws_organizational_structure_info
        set superior_dept_abbreviation = #{superiorDeptAbbreviation,jdbcType=VARCHAR},
            part_deptId_chn            = #{partDeptIdChn,jdbcType=INTEGER},
            dept_id                    = #{deptId,jdbcType=INTEGER},
            `row_number`               = #{rowNumber,jdbcType=INTEGER},
            dept_abbreviation          = #{deptAbbreviation,jdbcType=VARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateBatch" parameterType="java.util.List">
        update mm_ws_organizational_structure_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="superior_dept_abbreviation = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.superiorDeptAbbreviation,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="part_deptId_chn = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.partDeptIdChn,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="dept_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.deptId,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="`row_number` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.rowNumber,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="dept_abbreviation = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.deptAbbreviation,jdbcType=VARCHAR}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        insert into mm_ws_organizational_structure_info
                (superior_dept_abbreviation, part_deptId_chn, dept_id, `row_number`, dept_abbreviation)
                values
        <foreach collection="list" item="item" separator=",">
            (#{item.superiorDeptAbbreviation,jdbcType=VARCHAR}, #{item.partDeptIdChn,jdbcType=INTEGER},
             #{item.deptId,jdbcType=INTEGER}, #{item.rowNumber,jdbcType=INTEGER},
             #{item.deptAbbreviation,jdbcType=VARCHAR})
        </foreach>
    </insert>

    <update id="cleanTable">
        TRUNCATE TABLE mm_ws_organizational_structure_info
    </update>

    <select id="listByDeptRootId" resultMap="BaseResultMap"
            resultType="com.pttl.mobile.manager.mobile.ws.entity.OrganizationalStructureInfoDO">
        select
        <include refid="Base_Column_List"/>
        from mm_ws_organizational_structure_info
        WHERE part_deptId_chn = #{treeRootId}
    </select>

    <select id="listByDeptNameAndDeptIdList" resultMap="BaseResultMap"
            resultType="com.pttl.mobile.manager.mobile.ws.entity.OrganizationalStructureInfoDO">
        select
        <include refid="Base_Column_List"/>
        from mm_ws_organizational_structure_info
        <where>
            <if test="nameDesc != null and nameDesc != ''">
                and dept_abbreviation like concat('%', #{nameDesc}, '%')
            </if>

            <if test="list != null and list.size() > 0">
                and part_deptId_chn in
                <foreach close=")" collection="list" item="item" open="(" separator=", ">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>
        </where>
    </select>

    <!--调用函数 递归获取当前部门及子部门id-->
    <select id="getDeptListByDeptId"
            resultType="java.lang.Integer">
        SELECT dept_id
        FROM mm_ws_organizational_structure_info
        WHERE FIND_IN_SET(dept_id, queryChildrenDeptInfo(#{deptId}))
    </select>
</mapper>