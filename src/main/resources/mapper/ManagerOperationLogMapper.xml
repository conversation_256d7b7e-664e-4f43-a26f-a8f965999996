<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pttl.mobile.manager.dao.ManagerOperationLogMapper">
    <resultMap id="BaseResultMap" type="com.pttl.mobile.manager.domain.entity.ManagerOperationLogDO">
        <!--@Table mm_manager_operation_log-->
        <id column="id" property="id"/>
        <result column="operation_module" property="operationModule"/>
        <result column="operation_status" property="operationStatus"/>
        <result column="operation_type" property="operationType"/>
        <result column="operation_description" property="operationDescription"/>
        <result column="operation_request_param" property="operationRequestParam"/>
        <result column="operation_response_param" property="operationResponseParam"/>
        <result column="exception_name" property="exceptionName"/>
        <result column="exception_message" property="exceptionMessage"/>
        <result column="operation_user_id" property="operationUserId"/>
        <result column="operation_user_name" property="operationUserName"/>
        <result column="operation_method" property="operationMethod"/>
        <result column="operation_uri" property="operationUri"/>
        <result column="operation_ip" property="operationIp"/>
        <result column="create_time" property="createTime"/>
        <result column="remark" property="remark"/>
    </resultMap>
    <sql id="Base_Column_List">
        id,
        operation_module,
        operation_status,
        operation_type,
        operation_description,
        operation_request_param,
        operation_response_param,
        exception_name,
        exception_message,
        operation_user_id,
        operation_user_name,
        operation_method,
        operation_uri,
        operation_ip,
        create_time,
        remark
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mm_manager_operation_log
        where id = #{id}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from mm_manager_operation_log
        where id = #{id}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="com.pttl.mobile.manager.domain.entity.ManagerOperationLogDO" useGeneratedKeys="true">
        insert into mm_manager_operation_log (operation_module, operation_status, operation_type, operation_description,
                                              operation_request_param, operation_response_param, exception_name,
                                              exception_message,
                                              operation_user_id, operation_user_name, operation_method, operation_uri,
                                              operation_ip, create_time, remark)
        values (#{operationModule}, #{operationStatus}, #{operationType}, #{operationDescription},
                #{operationRequestParam}, #{operationResponseParam}, #{exceptionName}, #{exceptionMessage},
                #{operationUserId}, #{operationUserName}, #{operationMethod}, #{operationUri},
                #{operationIp}, #{createTime}, #{remark})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.pttl.mobile.manager.domain.entity.ManagerOperationLogDO" useGeneratedKeys="true">
        insert into mm_manager_operation_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="operationModule != null">
                operation_module,
            </if>
            <if test="operationStatus != null">
                operation_status,
            </if>
            <if test="operationType != null">
                operation_type,
            </if>
            <if test="operationDescription != null">
                operation_description,
            </if>
            <if test="operationRequestParam != null">
                operation_request_param,
            </if>
            <if test="operationResponseParam != null">
                operation_response_param,
            </if>
            <if test="exceptionName != null">
                exception_name,
            </if>
            <if test="exceptionMessage != null">
                exception_message,
            </if>
            <if test="operationUserId != null">
                operation_user_id,
            </if>
            <if test="operationUserName != null">
                operation_user_name,
            </if>
            <if test="operationMethod != null">
                operation_method,
            </if>
            <if test="operationUri != null">
                operation_uri,
            </if>
            <if test="operationIp != null">
                operation_ip,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="remark != null">
                remark,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="operationModule != null">
                #{operationModule},
            </if>
            <if test="operationStatus != null">
                #{operationStatus},
            </if>
            <if test="operationType != null">
                #{operationType},
            </if>
            <if test="operationDescription != null">
                #{operationDescription},
            </if>
            <if test="operationRequestParam != null">
                #{operationRequestParam},
            </if>
            <if test="operationResponseParam != null">
                #{operationResponseParam},
            </if>
            <if test="exceptionName != null">
                #{exceptionName},
            </if>
            <if test="exceptionMessage != null">
                #{exceptionMessage},
            </if>
            <if test="operationUserId != null">
                #{operationUserId},
            </if>
            <if test="operationUserName != null">
                #{operationUserName},
            </if>
            <if test="operationMethod != null">
                #{operationMethod},
            </if>
            <if test="operationUri != null">
                #{operationUri},
            </if>
            <if test="operationIp != null">
                #{operationIp},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="remark != null">
                #{remark},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.pttl.mobile.manager.domain.entity.ManagerOperationLogDO">
        update mm_manager_operation_log
        <set>
            <if test="operationModule != null">
                operation_module = #{operationModule},
            </if>
            <if test="operationStatus != null">
                operation_status = #{operationStatus},
            </if>
            <if test="operationType != null">
                operation_type = #{operationType},
            </if>
            <if test="operationDescription != null">
                operation_description = #{operationDescription},
            </if>
            <if test="operationRequestParam != null">
                operation_request_param = #{operationRequestParam},
            </if>
            <if test="operationResponseParam != null">
                operation_response_param = #{operationResponseParam},
            </if>
            <if test="exceptionName != null">
                exception_name = #{exceptionName},
            </if>
            <if test="exceptionMessage != null">
                exception_message = #{exceptionMessage},
            </if>
            <if test="operationUserId != null">
                operation_user_id = #{operationUserId},
            </if>
            <if test="operationUserName != null">
                operation_user_name = #{operationUserName},
            </if>
            <if test="operationMethod != null">
                operation_method = #{operationMethod},
            </if>
            <if test="operationUri != null">
                operation_uri = #{operationUri},
            </if>
            <if test="operationIp != null">
                operation_ip = #{operationIp},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
        </set>
        where id = #{id}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.pttl.mobile.manager.domain.entity.ManagerOperationLogDO">
        update mm_manager_operation_log
        set operation_module         = #{operationModule},
            operation_status         = #{operationStatus},
            operation_type           = #{operationType},
            operation_description    = #{operationDescription},
            operation_request_param  = #{operationRequestParam},
            operation_response_param = #{operationResponseParam},
            exception_name           = #{exceptionName},
            exception_message        = #{exceptionMessage},
            operation_user_id        = #{operationUserId},
            operation_user_name      = #{operationUserName},
            operation_method         = #{operationMethod},
            operation_uri            = #{operationUri},
            operation_ip             = #{operationIp},
            create_time              = #{createTime},
            remark                   = #{remark}
        where id = #{id}
    </update>
    <select id="selectAllOrderByIdDesc" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mm_manager_operation_log
        order by id desc
    </select>

    <!--根据请求参数 获取分页数据-->
    <select id="listByPageParameter" resultMap="BaseResultMap"
            resultType="com.pttl.mobile.manager.domain.entity.ManagerOperationLogDO"
            parameterType="com.pttl.mobile.manager.domain.request.ManagerOperationLogPageRequest">
        select
        <include refid="Base_Column_List"/>
        from mm_manager_operation_log
        <where>
            <if test="operationStatus != null">
                AND operation_status = #{operationStatus}
            </if>
            <if test="operationUserName != null and operationUserName != ''">
                AND operation_user_name LIKE concat('%', #{operationUserName}, '%')
            </if>
            <if test="operationType != null and operationType != ''">
                AND operation_type LIKE concat('%', #{operationType}, '%')
            </if>
            <if test="startTime != null">
                AND create_time &gt;= DATE_FORMAT(#{startTime}, '%Y-%m-%d')
            </if>
            <if test="endTime != null">
                AND create_time &lt;= DATE_FORMAT(#{endTime}, '%Y-%m-%d')
            </if>
        </where>
        ORDER BY create_time DESC
    </select>
</mapper>