<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pttl.mobile.manager.mobile.ws.dao.DeptInfoMapper">
  <resultMap id="BaseResultMap" type="com.pttl.mobile.manager.mobile.ws.entity.DeptInfoDO">
    
    <!--@Table mm_ws_dept_info-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="dept_abbreviation_short" jdbcType="VARCHAR" property="deptAbbreviationShort" />
    <result column="manager_id" jdbcType="VARCHAR" property="managerId" />
    <result column="serv_att_abbreviation" jdbcType="VARCHAR" property="servAttAbbreviation" />
    <result column="eff_status" jdbcType="VARCHAR" property="effStatus" />
    <result column="dept_full_name" jdbcType="VARCHAR" property="deptFullName" />
    <result column="sequence" jdbcType="INTEGER" property="sequence" />
    <result column="dept_cost" jdbcType="VARCHAR" property="deptCost" />
    <result column="dept_sort_abbreviation" jdbcType="VARCHAR" property="deptSortAbbreviation" />
    <result column="dept_count" jdbcType="VARCHAR" property="deptCount" />
    <result column="row_number" jdbcType="INTEGER" property="rowNumber" />
    <result column="dept_hie_abbreviation" jdbcType="VARCHAR" property="deptHieAbbreviation" />
    <result column="dept_id" jdbcType="VARCHAR" property="deptId" />
    <result column="company_abbreviation" jdbcType="VARCHAR" property="companyAbbreviation" />
  </resultMap>
  <sql id="Base_Column_List">
    
    id, dept_abbreviation_short, manager_id, serv_att_abbreviation, eff_status, dept_full_name, 
    `sequence`, dept_cost, dept_sort_abbreviation, dept_count, `row_number`, dept_hie_abbreviation, 
    dept_id, company_abbreviation
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    
    select 
    <include refid="Base_Column_List" />
    from mm_ws_dept_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    
    delete from mm_ws_dept_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.pttl.mobile.manager.mobile.ws.entity.DeptInfoDO" useGeneratedKeys="true">
    
    insert into mm_ws_dept_info (dept_abbreviation_short, manager_id, 
      serv_att_abbreviation, eff_status, dept_full_name, 
      `sequence`, dept_cost, dept_sort_abbreviation, 
      dept_count, `row_number`, dept_hie_abbreviation, 
      dept_id, company_abbreviation)
    values (#{deptAbbreviationShort,jdbcType=VARCHAR}, #{managerId,jdbcType=VARCHAR}, 
      #{servAttAbbreviation,jdbcType=VARCHAR}, #{effStatus,jdbcType=VARCHAR}, #{deptFullName,jdbcType=VARCHAR}, 
      #{sequence,jdbcType=INTEGER}, #{deptCost,jdbcType=VARCHAR}, #{deptSortAbbreviation,jdbcType=VARCHAR}, 
      #{deptCount,jdbcType=VARCHAR}, #{rowNumber,jdbcType=INTEGER}, #{deptHieAbbreviation,jdbcType=VARCHAR}, 
      #{deptId,jdbcType=VARCHAR}, #{companyAbbreviation,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.pttl.mobile.manager.mobile.ws.entity.DeptInfoDO" useGeneratedKeys="true">
    
    insert into mm_ws_dept_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="deptAbbreviationShort != null">
        dept_abbreviation_short,
      </if>
      <if test="managerId != null">
        manager_id,
      </if>
      <if test="servAttAbbreviation != null">
        serv_att_abbreviation,
      </if>
      <if test="effStatus != null">
        eff_status,
      </if>
      <if test="deptFullName != null">
        dept_full_name,
      </if>
      <if test="sequence != null">
        `sequence`,
      </if>
      <if test="deptCost != null">
        dept_cost,
      </if>
      <if test="deptSortAbbreviation != null">
        dept_sort_abbreviation,
      </if>
      <if test="deptCount != null">
        dept_count,
      </if>
      <if test="rowNumber != null">
        `row_number`,
      </if>
      <if test="deptHieAbbreviation != null">
        dept_hie_abbreviation,
      </if>
      <if test="deptId != null">
        dept_id,
      </if>
      <if test="companyAbbreviation != null">
        company_abbreviation,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="deptAbbreviationShort != null">
        #{deptAbbreviationShort,jdbcType=VARCHAR},
      </if>
      <if test="managerId != null">
        #{managerId,jdbcType=VARCHAR},
      </if>
      <if test="servAttAbbreviation != null">
        #{servAttAbbreviation,jdbcType=VARCHAR},
      </if>
      <if test="effStatus != null">
        #{effStatus,jdbcType=VARCHAR},
      </if>
      <if test="deptFullName != null">
        #{deptFullName,jdbcType=VARCHAR},
      </if>
      <if test="sequence != null">
        #{sequence,jdbcType=INTEGER},
      </if>
      <if test="deptCost != null">
        #{deptCost,jdbcType=VARCHAR},
      </if>
      <if test="deptSortAbbreviation != null">
        #{deptSortAbbreviation,jdbcType=VARCHAR},
      </if>
      <if test="deptCount != null">
        #{deptCount,jdbcType=VARCHAR},
      </if>
      <if test="rowNumber != null">
        #{rowNumber,jdbcType=INTEGER},
      </if>
      <if test="deptHieAbbreviation != null">
        #{deptHieAbbreviation,jdbcType=VARCHAR},
      </if>
      <if test="deptId != null">
        #{deptId,jdbcType=VARCHAR},
      </if>
      <if test="companyAbbreviation != null">
        #{companyAbbreviation,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.pttl.mobile.manager.mobile.ws.entity.DeptInfoDO">
    
    update mm_ws_dept_info
    <set>
      <if test="deptAbbreviationShort != null">
        dept_abbreviation_short = #{deptAbbreviationShort,jdbcType=VARCHAR},
      </if>
      <if test="managerId != null">
        manager_id = #{managerId,jdbcType=VARCHAR},
      </if>
      <if test="servAttAbbreviation != null">
        serv_att_abbreviation = #{servAttAbbreviation,jdbcType=VARCHAR},
      </if>
      <if test="effStatus != null">
        eff_status = #{effStatus,jdbcType=VARCHAR},
      </if>
      <if test="deptFullName != null">
        dept_full_name = #{deptFullName,jdbcType=VARCHAR},
      </if>
      <if test="sequence != null">
        `sequence` = #{sequence,jdbcType=INTEGER},
      </if>
      <if test="deptCost != null">
        dept_cost = #{deptCost,jdbcType=VARCHAR},
      </if>
      <if test="deptSortAbbreviation != null">
        dept_sort_abbreviation = #{deptSortAbbreviation,jdbcType=VARCHAR},
      </if>
      <if test="deptCount != null">
        dept_count = #{deptCount,jdbcType=VARCHAR},
      </if>
      <if test="rowNumber != null">
        `row_number` = #{rowNumber,jdbcType=INTEGER},
      </if>
      <if test="deptHieAbbreviation != null">
        dept_hie_abbreviation = #{deptHieAbbreviation,jdbcType=VARCHAR},
      </if>
      <if test="deptId != null">
        dept_id = #{deptId,jdbcType=VARCHAR},
      </if>
      <if test="companyAbbreviation != null">
        company_abbreviation = #{companyAbbreviation,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.pttl.mobile.manager.mobile.ws.entity.DeptInfoDO">
    
    update mm_ws_dept_info
    set dept_abbreviation_short = #{deptAbbreviationShort,jdbcType=VARCHAR},
      manager_id = #{managerId,jdbcType=VARCHAR},
      serv_att_abbreviation = #{servAttAbbreviation,jdbcType=VARCHAR},
      eff_status = #{effStatus,jdbcType=VARCHAR},
      dept_full_name = #{deptFullName,jdbcType=VARCHAR},
      `sequence` = #{sequence,jdbcType=INTEGER},
      dept_cost = #{deptCost,jdbcType=VARCHAR},
      dept_sort_abbreviation = #{deptSortAbbreviation,jdbcType=VARCHAR},
      dept_count = #{deptCount,jdbcType=VARCHAR},
      `row_number` = #{rowNumber,jdbcType=INTEGER},
      dept_hie_abbreviation = #{deptHieAbbreviation,jdbcType=VARCHAR},
      dept_id = #{deptId,jdbcType=VARCHAR},
      company_abbreviation = #{companyAbbreviation,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    
    update mm_ws_dept_info
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="dept_abbreviation_short = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.deptAbbreviationShort,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="manager_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.managerId,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="serv_att_abbreviation = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.servAttAbbreviation,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="eff_status = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.effStatus,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="dept_full_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.deptFullName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`sequence` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.sequence,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="dept_cost = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.deptCost,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="dept_sort_abbreviation = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.deptSortAbbreviation,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="dept_count = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.deptCount,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`row_number` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.rowNumber,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="dept_hie_abbreviation = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.deptHieAbbreviation,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="dept_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.deptId,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="company_abbreviation = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.companyAbbreviation,jdbcType=VARCHAR}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    
    insert into mm_ws_dept_info
    (dept_abbreviation_short, manager_id, serv_att_abbreviation, eff_status, dept_full_name, 
      `sequence`, dept_cost, dept_sort_abbreviation, dept_count, `row_number`, dept_hie_abbreviation, 
      dept_id, company_abbreviation)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.deptAbbreviationShort,jdbcType=VARCHAR}, #{item.managerId,jdbcType=VARCHAR}, 
        #{item.servAttAbbreviation,jdbcType=VARCHAR}, #{item.effStatus,jdbcType=VARCHAR}, 
        #{item.deptFullName,jdbcType=VARCHAR}, #{item.sequence,jdbcType=INTEGER}, #{item.deptCost,jdbcType=VARCHAR}, 
        #{item.deptSortAbbreviation,jdbcType=VARCHAR}, #{item.deptCount,jdbcType=VARCHAR}, 
        #{item.rowNumber,jdbcType=INTEGER}, #{item.deptHieAbbreviation,jdbcType=VARCHAR}, 
        #{item.deptId,jdbcType=VARCHAR}, #{item.companyAbbreviation,jdbcType=VARCHAR})
    </foreach>
  </insert>

  <update id="cleanTable">
        TRUNCATE TABLE mm_ws_dept_info
    </update>
</mapper>