<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pttl.mobile.manager.dao.MobileLogsMapper">
  <resultMap id="BaseResultMap" type="com.pttl.mobile.manager.domain.entity.MobileLogs">
  
    <!--@Table mm_mobile_logs-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="api_name" jdbcType="VARCHAR" property="apiName" />
    <result column="server_name" jdbcType="VARCHAR" property="serverName" />
    <result column="app_version" jdbcType="VARCHAR" property="appVersion" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="log_date" jdbcType="BIGINT" property="logDate" />
    <result column="ihr_name" jdbcType="VARCHAR" property="ihrName" />
    <result column="device_info" jdbcType="VARCHAR" property="deviceInfo" />
    <result column="page_time" jdbcType="INTEGER" property="pageTime" />
    <result column="platform" jdbcType="VARCHAR" property="platform" />
    <result column="system_type" jdbcType="VARCHAR" property="systemType" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="system_login_name" jdbcType="VARCHAR" property="systemLoginName" />
    <result column="system_version" jdbcType="VARCHAR" property="systemVersion" />
    <result column="time_used" jdbcType="INTEGER" property="timeUsed" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
  </resultMap>
  <sql id="Base_Column_List">
  
    id, api_name, server_name, app_version, description, log_date, ihr_name, device_info, page_time,
    platform, system_type, `status`, system_login_name, system_version, time_used, remark
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
  
    select 
    <include refid="Base_Column_List" />
    from mm_mobile_logs
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
  
    delete from mm_mobile_logs
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.pttl.mobile.manager.domain.entity.MobileLogs" useGeneratedKeys="true">
  
    insert into mm_mobile_logs (api_name, server_name, app_version, description,
      log_date, ihr_name, device_info, 
      page_time, platform, system_type, 
      `status`, system_login_name, system_version, 
      time_used, remark)
    values (#{apiName,jdbcType=VARCHAR}, #{serverName,jdbcType=VARCHAR}, #{appVersion,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR},
      #{logDate,jdbcType=BIGINT}, #{ihrName,jdbcType=VARCHAR}, #{deviceInfo,jdbcType=VARCHAR}, 
      #{pageTime,jdbcType=INTEGER}, #{platform,jdbcType=VARCHAR}, #{systemType,jdbcType=VARCHAR}, 
      #{status,jdbcType=VARCHAR}, #{systemLoginName,jdbcType=VARCHAR}, #{systemVersion,jdbcType=VARCHAR}, 
      #{timeUsed,jdbcType=INTEGER}, #{remark,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.pttl.mobile.manager.domain.entity.MobileLogs" useGeneratedKeys="true">
  
    insert into mm_mobile_logs
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="apiName != null and apiName != ''">
        api_name,
      </if>
      <if test="serverName != null and serverName != ''">
        server_name,
      </if>
      <if test="appVersion != null and appVersion != ''">
        app_version,
      </if>
      <if test="description != null and description != ''">
        description,
      </if>
      <if test="logDate != null">
        log_date,
      </if>
      <if test="ihrName != null and ihrName != ''">
        ihr_name,
      </if>
      <if test="deviceInfo != null and deviceInfo != ''">
        device_info,
      </if>
      <if test="pageTime != null">
        page_time,
      </if>
      <if test="platform != null and platform != ''">
        platform,
      </if>
      <if test="systemType != null and systemType != ''">
        system_type,
      </if>
      <if test="status != null and status != ''">
        `status`,
      </if>
      <if test="systemLoginName != null and systemLoginName != ''">
        system_login_name,
      </if>
      <if test="systemVersion != null and systemVersion != ''">
        system_version,
      </if>
      <if test="timeUsed != null">
        time_used,
      </if>
      <if test="remark != null and remark != ''">
        remark,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="apiName != null and apiName != ''">
        #{apiName,jdbcType=VARCHAR},
      </if>
      <if test="serverName != null and serverName != ''">
        #{serverName,jdbcType=VARCHAR},
      </if>
      <if test="appVersion != null and appVersion != ''">
        #{appVersion,jdbcType=VARCHAR},
      </if>
      <if test="description != null and description != ''">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="logDate != null">
        #{logDate,jdbcType=BIGINT},
      </if>
      <if test="ihrName != null and ihrName != ''">
        #{ihrName,jdbcType=VARCHAR},
      </if>
      <if test="deviceInfo != null and deviceInfo != ''">
        #{deviceInfo,jdbcType=VARCHAR},
      </if>
      <if test="pageTime != null">
        #{pageTime,jdbcType=INTEGER},
      </if>
      <if test="platform != null and platform != ''">
        #{platform,jdbcType=VARCHAR},
      </if>
      <if test="systemType != null and systemType != ''">
        #{systemType,jdbcType=VARCHAR},
      </if>
      <if test="status != null and status != ''">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="systemLoginName != null and systemLoginName != ''">
        #{systemLoginName,jdbcType=VARCHAR},
      </if>
      <if test="systemVersion != null and systemVersion != ''">
        #{systemVersion,jdbcType=VARCHAR},
      </if>
      <if test="timeUsed != null">
        #{timeUsed,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null and remark != ''">
        #{remark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.pttl.mobile.manager.domain.entity.MobileLogs">
  
    update mm_mobile_logs
    <set>
      <if test="apiName != null and apiName != ''">
        api_name = #{apiName,jdbcType=VARCHAR},
      </if>
      <if test="serverName != null and serverName != ''">
        app_version = #{serverName,jdbcType=VARCHAR},
      </if>
      <if test="appVersion != null and appVersion != ''">
        app_version = #{appVersion,jdbcType=VARCHAR},
      </if>
      <if test="description != null and description != ''">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="logDate != null">
        log_date = #{logDate,jdbcType=BIGINT},
      </if>
      <if test="ihrName != null and ihrName != ''">
        ihr_name = #{ihrName,jdbcType=VARCHAR},
      </if>
      <if test="deviceInfo != null and deviceInfo != ''">
        device_info = #{deviceInfo,jdbcType=VARCHAR},
      </if>
      <if test="pageTime != null">
        page_time = #{pageTime,jdbcType=INTEGER},
      </if>
      <if test="platform != null and platform != ''">
        platform = #{platform,jdbcType=VARCHAR},
      </if>
      <if test="systemType != null and systemType != ''">
        system_type = #{systemType,jdbcType=VARCHAR},
      </if>
      <if test="status != null and status != ''">
        `status` = #{status,jdbcType=VARCHAR},
      </if>
      <if test="systemLoginName != null and systemLoginName != ''">
        system_login_name = #{systemLoginName,jdbcType=VARCHAR},
      </if>
      <if test="systemVersion != null and systemVersion != ''">
        system_version = #{systemVersion,jdbcType=VARCHAR},
      </if>
      <if test="timeUsed != null">
        time_used = #{timeUsed,jdbcType=INTEGER},
      </if>
      <if test="remark != null and remark != ''">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.pttl.mobile.manager.domain.entity.MobileLogs">
  
    update mm_mobile_logs
    set api_name = #{apiName,jdbcType=VARCHAR},
      server_name = #{serverName,jdbcType=VARCHAR},
      app_version = #{appVersion,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      log_date = #{logDate,jdbcType=BIGINT},
      ihr_name = #{ihrName,jdbcType=VARCHAR},
      device_info = #{deviceInfo,jdbcType=VARCHAR},
      page_time = #{pageTime,jdbcType=INTEGER},
      platform = #{platform,jdbcType=VARCHAR},
      system_type = #{systemType,jdbcType=VARCHAR},
      `status` = #{status,jdbcType=VARCHAR},
      system_login_name = #{systemLoginName,jdbcType=VARCHAR},
      system_version = #{systemVersion,jdbcType=VARCHAR},
      time_used = #{timeUsed,jdbcType=INTEGER},
      remark = #{remark,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
  
    update mm_mobile_logs
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="api_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.apiName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="server_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.serverName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="app_version = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.appVersion,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="description = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.description,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="log_date = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.logDate,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="ihr_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.ihrName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="device_info = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.deviceInfo,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="page_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.pageTime,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="platform = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.platform,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="system_type = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.systemType,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`status` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.status,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="system_login_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.systemLoginName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="system_version = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.systemVersion,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="time_used = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.timeUsed,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="remark = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.remark,jdbcType=VARCHAR}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
  
    update mm_mobile_logs
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="api_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.apiName != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.apiName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="server_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.serverName != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.serverName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="app_version = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.appVersion != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.appVersion,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="description = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.description != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.description,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="log_date = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.logDate != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.logDate,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="ihr_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.ihrName != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.ihrName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="device_info = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.deviceInfo != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.deviceInfo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="page_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.pageTime != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.pageTime,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="platform = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.platform != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.platform,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="system_type = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.systemType != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.systemType,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`status` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.status != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.status,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="system_login_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.systemLoginName != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.systemLoginName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="system_version = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.systemVersion != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.systemVersion,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="time_used = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.timeUsed != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.timeUsed,jdbcType=INTEGER}
          </if>
        </foreach>
      </trim>
      <trim prefix="remark = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.remark != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.remark,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
  
    insert into mm_mobile_logs
    (api_name, server_name, app_version, description, log_date, ihr_name, device_info, page_time,
      platform, system_type, `status`, system_login_name, system_version, time_used, 
      remark)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.apiName,jdbcType=VARCHAR}, #{serverName,jdbcType=VARCHAR}, #{item.appVersion,jdbcType=VARCHAR}, #{item.description,jdbcType=VARCHAR},
        #{item.logDate,jdbcType=BIGINT}, #{item.ihrName,jdbcType=VARCHAR}, #{item.deviceInfo,jdbcType=VARCHAR}, 
        #{item.pageTime,jdbcType=INTEGER}, #{item.platform,jdbcType=VARCHAR}, #{item.systemType,jdbcType=VARCHAR}, 
        #{item.status,jdbcType=VARCHAR}, #{item.systemLoginName,jdbcType=VARCHAR}, #{item.systemVersion,jdbcType=VARCHAR}, 
        #{item.timeUsed,jdbcType=INTEGER}, #{item.remark,jdbcType=VARCHAR})
    </foreach>
  </insert>

  <select id="pageInfo" resultType="com.pttl.mobile.manager.domain.dto.FrontLogsResultDTO">
    select id, log_date, `status`, server_name,ihr_name, system_type, system_login_name as userName
    from mm_mobile_logs
    <where>
      <if test="systemType != null and systemType != ''">
        and system_type = #{systemType,jdbcType=VARCHAR}
      </if>
      <if test="ihrName != null and ihrName != ''">
        and ihr_name = #{ihrName,jdbcType=VARCHAR}
      </if>
      <if test="userName != null and userName != ''">
        and system_login_name  LIKE concat('%', #{userName}, '%')
      </if>
      <if test="endLoginTimeLong != null">
        and log_date &lt;= #{endLoginTimeLong,jdbcType=BIGINT}
      </if>
      <if test="startLoginTimeLong != null">
        and log_date &gt;= #{startLoginTimeLong,jdbcType=BIGINT}
      </if>
      <if test="status != null and status != ''">
        and status = #{status,jdbcType=VARCHAR}
      </if>
    </where>
  </select>
</mapper>