<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pttl.mobile.manager.dao.MobileRecordLogMapper">
    <resultMap id="BaseResultMap" type="com.pttl.mobile.manager.domain.entity.MobileRecordLogDO">
        <!--@Table mm_mobile_record_log-->
        <id column="id" property="id"/>
        <result column="app_version" property="appVersion"/>
        <result column="description" property="description"/>
        <result column="login_name" property="loginName"/>
        <result column="login_time" property="loginTime"/>
        <result column="model" property="model"/>
        <result column="module" property="module"/>
        <result column="occur_time" property="occurTime"/>
        <result column="platform" property="platform"/>
        <result column="source" property="source"/>
        <result column="sub_module" property="subModule"/>
        <result column="system_login_name" property="systemLoginName"/>
        <result column="vpn_login_name" property="vpnLoginName"/>
        <result column="time_used" property="timeUsed"/>
        <result column="type" property="type"/>
        <result column="remark" property="remark"/>
        <result column="extension" property="extension"/>
    </resultMap>
    <sql id="Base_Column_List">
        id,
        app_version,
        description,
        login_name,
        login_time,
        model,
        `module`,
        occur_time,
        platform,
        `source`,
        sub_module,
        system_login_name,
        time_used,
        `type`,
        remark,
        extension,
        vpn_login_name
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mm_mobile_record_log_${yearMonth}
        where id = #{id}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from mm_mobile_record_log
        where id = #{id}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="com.pttl.mobile.manager.domain.entity.MobileRecordLogDO" useGeneratedKeys="true">
        insert into mm_mobile_record_log_${tableNameDateMonth} (app_version, description, login_name, login_time, model,
                                                                `module`,
                                                                occur_time, platform, `source`, sub_module,
                                                                system_login_name, time_used,
                                                                `type`, remark, extension, vpn_login_name)
        values (#{mobileRecordLogDO.appVersion}, #{mobileRecordLogDO.description}, #{mobileRecordLogDO.loginName},
                #{mobileRecordLogDO.loginTime}, #{mobileRecordLogDO.model}, #{mobileRecordLogDO.module},
                #{mobileRecordLogDO.occurTime}, #{mobileRecordLogDO.platform}, #{mobileRecordLogDO.source},
                #{mobileRecordLogDO.subModule}, #{mobileRecordLogDO.systemLoginName}, #{mobileRecordLogDO.timeUsed},
                #{mobileRecordLogDO.type}, #{mobileRecordLogDO.remark}, #{mobileRecordLogDO.extension}, #{mobileRecordLogDO.vpnLoginName})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.pttl.mobile.manager.domain.entity.MobileRecordLogDO" useGeneratedKeys="true">
        insert into mm_mobile_record_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="appVersion != null">
                app_version,
            </if>
            <if test="description != null">
                description,
            </if>
            <if test="loginName != null">
                login_name,
            </if>
            <if test="loginTime != null">
                login_time,
            </if>
            <if test="model != null">
                model,
            </if>
            <if test="module != null">
                `module`,
            </if>
            <if test="occurTime != null">
                occur_time,
            </if>
            <if test="platform != null">
                platform,
            </if>
            <if test="source != null">
                `source`,
            </if>
            <if test="subModule != null">
                sub_module,
            </if>
            <if test="systemLoginName != null">
                system_login_name,
            </if>
            <if test="timeUsed != null">
                time_used,
            </if>
            <if test="type != null">
                `type`,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="extension != null">
                extension,
            </if>
            <if test="vpnLoginName != null">
                vpn_login_name,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="appVersion != null">
                #{appVersion},
            </if>
            <if test="description != null">
                #{description},
            </if>
            <if test="loginName != null">
                #{loginName},
            </if>
            <if test="loginTime != null">
                #{loginTime},
            </if>
            <if test="model != null">
                #{model},
            </if>
            <if test="module != null">
                #{module},
            </if>
            <if test="occurTime != null">
                #{occurTime},
            </if>
            <if test="platform != null">
                #{platform},
            </if>
            <if test="source != null">
                #{source},
            </if>
            <if test="subModule != null">
                #{subModule},
            </if>
            <if test="systemLoginName != null">
                #{systemLoginName},
            </if>
            <if test="timeUsed != null">
                #{timeUsed},
            </if>
            <if test="type != null">
                #{type},
            </if>
            <if test="remark != null">
                #{remark},
            </if>
            <if test="extension != null">
                #{extension},
            </if>
            <if test="vpnLoginName != null">
                #{vpnLoginName},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.pttl.mobile.manager.domain.entity.MobileRecordLogDO">
        update mm_mobile_record_log
        <set>
            <if test="appVersion != null">
                app_version = #{appVersion},
            </if>
            <if test="description != null">
                description = #{description},
            </if>
            <if test="loginName != null">
                login_name = #{loginName},
            </if>
            <if test="loginTime != null">
                login_time = #{loginTime},
            </if>
            <if test="model != null">
                model = #{model},
            </if>
            <if test="module != null">
                `module` = #{module},
            </if>
            <if test="occurTime != null">
                occur_time = #{occurTime},
            </if>
            <if test="platform != null">
                platform = #{platform},
            </if>
            <if test="source != null">
                `source` = #{source},
            </if>
            <if test="subModule != null">
                sub_module = #{subModule},
            </if>
            <if test="systemLoginName != null">
                system_login_name = #{systemLoginName},
            </if>
            <if test="timeUsed != null">
                time_used = #{timeUsed},
            </if>
            <if test="type != null">
                `type` = #{type},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="extension != null">
                extension = #{extension},
            </if>
            <if test="vpnLoginName != null">
                vpn_login_name = #{vpnLoginName},
            </if>
        </set>
        where id = #{id}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.pttl.mobile.manager.domain.entity.MobileRecordLogDO">
        update mm_mobile_record_log
        set app_version       = #{appVersion},
            description       = #{description},
            login_name        = #{loginName},
            login_time        = #{loginTime},
            model             = #{model},
            `module`          = #{module},
            occur_time        = #{occurTime},
            platform          = #{platform},
            `source`          = #{source},
            sub_module        = #{subModule},
            system_login_name = #{systemLoginName},
            time_used         = #{timeUsed},
            `type`            = #{type},
            remark            = #{remark},
            extension         = #{extension},
            vpn_login_name    = #{vpnLoginName}
        where id = #{id}
    </update>
    <select id="selectAllOrderByIdDesc" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mm_mobile_record_log
        order by id desc
    </select>

    <select id="listByPageParameter" resultType="com.pttl.mobile.manager.domain.entity.MobileRecordLogDO"
            parameterType="com.pttl.mobile.manager.domain.request.MobileRecordLogPageRequest">
            select
        <include refid="Base_Column_List"/>
        from mm_mobile_record_log_${yearMonth}
        <where>
            <if test="module != null and module != ''">
                AND module = #{module}
            </if>
            <if test="platform != null and platform != ''">
                AND platform = #{platform}
            </if>
            <if test="employeeId != null and employeeId != ''">
                AND login_name LIKE concat('%', #{employeeId}, '%')
            </if>
            <if test="loginName != null and loginName != ''">
                AND remark LIKE concat('%', #{loginName}, '%')
            </if>
            <if test="loginName != null and loginName != ''">
                 or extension LIKE concat('%', #{loginName}, '%')
            </if>
            <if test="startTime != null">
                AND login_time &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                AND login_time &lt;= #{endTime}
            </if>
        </where>
        ORDER BY occur_time DESC
    </select>

    <select id="listLoginStatisticsByDateRange" resultType="com.pttl.mobile.manager.mobile.dto.LoginStatisticsResultDTO" parameterType="com.pttl.mobile.manager.mobile.dto.DateRange">
        SELECT
            source,
            COUNT( id ) AS count
        FROM
            mm_mobile_record_log_${yearStr}
        WHERE
            module = 'IHR'
            AND type != 'error'
            AND login_time BETWEEN #{startDate}
            AND #{endDate}
        GROUP BY
            source
    </select>

    <select id="listByPageParameterForMobile" resultType="com.pttl.mobile.manager.domain.entity.MobileRecordLogDO">
        select
        <include refid="Base_Column_List"/>
        from mm_mobile_record_log_${mouthDate}
        <where>
            <if test="queryDTO.type != null and queryDTO.type != ''">
                AND type = #{queryDTO.type}
            </if>
            <if test="queryDTO.source != null and queryDTO.source != ''">
                AND source = #{queryDTO.source}
            </if>
            <if test="queryDTO.loginName != null and queryDTO.loginName != ''">
                AND login_name LIKE concat('%', #{queryDTO.loginName}, '%')
            </if>
            <if test="queryDTO.loginName != null and queryDTO.loginName != ''">
                 or extension LIKE concat('%', #{queryDTO.loginName}, '%')
            </if>
            <if test="queryDTO.startLoginTime != null">
                AND login_time &gt;= #{queryDTO.startLoginTime}
            </if>
            <if test="queryDTO.endLoginTime != null">
                AND login_time &lt;= #{queryDTO.endLoginTime}
            </if>
        </where>
        ORDER BY login_time DESC
    </select>
</mapper>