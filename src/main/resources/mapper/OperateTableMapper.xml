<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pttl.mobile.manager.dao.OperateTableMapper">

  <select id="existTable" parameterType="java.lang.String" resultType="java.lang.Integer">
        select count(*)
        from information_schema.TABLES
        where LCASE(table_name)=#{tableName}
    </select>

    <update id="dropTable">
        DROP TABLE IF EXISTS ${tableName}
    </update>

    <update id="createTableOfMobileRecordLog" parameterType="java.lang.String">
        CREATE TABLE ${tableName} (
          `id` bigint(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
          `app_version` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'app版本',
          `description` text COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '描述',
          `login_name` varchar(128) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '登陆名称',
          `login_time` datetime DEFAULT NULL COMMENT '登陆时间',
          `model` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '系统版本',
          `module` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '模块',
          `occur_time` datetime DEFAULT NULL COMMENT '发生时间',
          `platform` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '平台 在线商城/在线办公',
          `source` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '手机系统 Android/IOS',
          `sub_module` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '子模块',
          `system_login_name` varchar(200) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '系统登陆名称',
          `vpn_login_name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT 'vpn登陆名称',
          `time_used` int(8) DEFAULT NULL COMMENT '使用次数',
          `type` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '类型 Info/Error/success',
          `remark` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '备注',
          `extension` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '扩展字段',
          PRIMARY KEY (`id`),
          KEY `index_login_name` (`login_name`) USING BTREE,
          KEY `index_login_time` (`login_time`) USING BTREE,
          KEY `index_source` (`source`) USING BTREE,
          KEY `index_type` (`type`) USING BTREE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='移动端业务操作记录日志表';
    </update>
</mapper>