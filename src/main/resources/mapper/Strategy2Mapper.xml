<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pttl.mobile.manager.dao.Strategy2Mapper">
  <resultMap id="BaseResultMap" type="com.pttl.mobile.manager.domain.entity.Strategy2DO">
    
    <!--@Table mm_strategy-->
    <id column="id" property="id" />
    <result column="name" property="name" />
    <result column="description" property="description" />
    <result column="weight" property="weight" />
    <result column="platform" property="platform" />
    <result column="current_step" property="currentStep" />
    <result column="is_deleted" property="isDeleted" />
    <result column="create_date" property="createDate" />
    <result column="last_update" property="lastUpdate" />
    <result column="remark" property="remark" />
  </resultMap>
  <sql id="Base_Column_List">
    
    id, `name`, description, weight, platform, current_step, is_deleted, create_date, 
    last_update, remark
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    
    select 
    <include refid="Base_Column_List" />
    from mm_strategy
    where id = #{id}
  </select>

  <select id="listData" resultType="com.pttl.mobile.manager.domain.entity.Strategy2DO" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from mm_strategy
    <where>
        <if test="name != null">
          and name like concat('%', #{name}, '%')
        </if>
          and is_deleted = ${@com.pttl.mobile.manager.domain.entity.Strategy2DO@DELETED_NO}
    </where>
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    
    delete from mm_strategy
    where id = #{id}
  </delete>
  <insert id="insert" parameterType="com.pttl.mobile.manager.domain.entity.Strategy2DO" useGeneratedKeys="true" keyProperty="id">
    
    insert into mm_strategy (id, `name`, description, weight, platform, current_step, is_deleted, 
      create_date, last_update, remark)
    values (#{id}, #{name}, #{description}, #{weight}, #{platform}, #{currentStep}, #{isDeleted}, 
      #{createDate}, #{lastUpdate}, #{remark})
  </insert>
  <insert id="insertSelective" parameterType="com.pttl.mobile.manager.domain.entity.Strategy2DO">
    
    insert into mm_strategy
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="name != null">
        `name`,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="weight != null">
        weight,
      </if>
      <if test="platform != null">
        platform,
      </if>
      <if test="currentStep != null">
        current_step,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="createDate != null">
        create_date,
      </if>
      <if test="lastUpdate != null">
        last_update,
      </if>
      <if test="remark != null">
        remark,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id},
      </if>
      <if test="name != null">
        #{name},
      </if>
      <if test="description != null">
        #{description},
      </if>
      <if test="weight != null">
        #{weight},
      </if>
      <if test="platform != null">
        #{platform},
      </if>
      <if test="currentStep != null">
        #{currentStep},
      </if>
      <if test="isDeleted != null">
        #{isDeleted},
      </if>
      <if test="createDate != null">
        #{createDate},
      </if>
      <if test="lastUpdate != null">
        #{lastUpdate},
      </if>
      <if test="remark != null">
        #{remark},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.pttl.mobile.manager.domain.entity.Strategy2DO">
    
    update mm_strategy
    <set>
      <if test="name != null">
        `name` = #{name},
      </if>
      <if test="description != null">
        description = #{description},
      </if>
      <if test="weight != null">
        weight = #{weight},
      </if>
      <if test="platform != null">
        platform = #{platform},
      </if>
      <if test="currentStep != null">
        current_step = #{currentStep},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted},
      </if>
      <if test="createDate != null">
        create_date = #{createDate},
      </if>
      <if test="lastUpdate != null">
        last_update = #{lastUpdate},
      </if>
      <if test="remark != null">
        remark = #{remark},
      </if>
    </set>
    where id = #{id}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.pttl.mobile.manager.domain.entity.Strategy2DO">
    
    update mm_strategy
    set `name` = #{name},
      description = #{description},
      weight = #{weight},
      platform = #{platform},
      current_step = #{currentStep},
      is_deleted = #{isDeleted},
      create_date = #{createDate},
      last_update = #{lastUpdate},
      remark = #{remark}
    where id = #{id}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    
    update mm_strategy
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="`name` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.name}
        </foreach>
      </trim>
      <trim prefix="description = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.description}
        </foreach>
      </trim>
      <trim prefix="weight = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.weight}
        </foreach>
      </trim>
      <trim prefix="platform = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.platform}
        </foreach>
      </trim>
      <trim prefix="current_step = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.currentStep}
        </foreach>
      </trim>
      <trim prefix="is_deleted = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.isDeleted}
        </foreach>
      </trim>
      <trim prefix="create_date = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.createDate}
        </foreach>
      </trim>
      <trim prefix="last_update = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.lastUpdate}
        </foreach>
      </trim>
      <trim prefix="remark = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.remark}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    
    update mm_strategy
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="`name` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.name != null">
            when id = #{item.id} then #{item.name}
          </if>
        </foreach>
      </trim>
      <trim prefix="description = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.description != null">
            when id = #{item.id} then #{item.description}
          </if>
        </foreach>
      </trim>
      <trim prefix="weight = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.weight != null">
            when id = #{item.id} then #{item.weight}
          </if>
        </foreach>
      </trim>
      <trim prefix="platform = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.platform != null">
            when id = #{item.id} then #{item.platform}
          </if>
        </foreach>
      </trim>
      <trim prefix="current_step = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.currentStep != null">
            when id = #{item.id} then #{item.currentStep}
          </if>
        </foreach>
      </trim>
      <trim prefix="is_deleted = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.isDeleted != null">
            when id = #{item.id} then #{item.isDeleted}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_date = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createDate != null">
            when id = #{item.id} then #{item.createDate}
          </if>
        </foreach>
      </trim>
      <trim prefix="last_update = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.lastUpdate != null">
            when id = #{item.id} then #{item.lastUpdate}
          </if>
        </foreach>
      </trim>
      <trim prefix="remark = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.remark != null">
            when id = #{item.id} then #{item.remark}
          </if>
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id}
    </foreach>
  </update>
  <insert id="batchInsert" parameterType="map">
    
    insert into mm_strategy
    (id, `name`, description, weight, platform, current_step, is_deleted, create_date, 
      last_update, remark)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.id}, #{item.name}, #{item.description}, #{item.weight}, #{item.platform}, 
        #{item.currentStep}, #{item.isDeleted}, #{item.createDate}, #{item.lastUpdate}, 
        #{item.remark})
    </foreach>
  </insert>

    <select id="listAllByWeight" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mm_strategy
        where weight = #{weight}
    </select>
</mapper>