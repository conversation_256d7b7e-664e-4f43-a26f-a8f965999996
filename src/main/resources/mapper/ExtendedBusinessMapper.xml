<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pttl.mobile.manager.dao.ExtendedBusinessMapper">
  <resultMap id="BaseResultMap" type="com.pttl.mobile.manager.domain.entity.ExtendedBusiness">
    <!--@mbg.generated-->
    <!--@Table mm_extended_business-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="data_key" jdbcType="VARCHAR" property="dataKey" />
    <result column="data_str" jdbcType="VARCHAR" property="dataStr" />
    <result column="data_text" jdbcType="LONGVARCHAR" property="dataText" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, data_key, data_str, data_text, create_time, update_time, remark
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from mm_extended_business
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from mm_extended_business
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.pttl.mobile.manager.domain.entity.ExtendedBusiness" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into mm_extended_business (data_key, data_str, data_text, 
      create_time, update_time, remark
      )
    values (#{dataKey,jdbcType=VARCHAR}, #{dataStr,jdbcType=VARCHAR}, #{dataText,jdbcType=LONGVARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{remark,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.pttl.mobile.manager.domain.entity.ExtendedBusiness" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into mm_extended_business
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="dataKey != null and dataKey != ''">
        data_key,
      </if>
      <if test="dataStr != null and dataStr != ''">
        data_str,
      </if>
      <if test="dataText != null and dataText != ''">
        data_text,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="remark != null and remark != ''">
        remark,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="dataKey != null and dataKey != ''">
        #{dataKey,jdbcType=VARCHAR},
      </if>
      <if test="dataStr != null and dataStr != ''">
        #{dataStr,jdbcType=VARCHAR},
      </if>
      <if test="dataText != null and dataText != ''">
        #{dataText,jdbcType=LONGVARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null and remark != ''">
        #{remark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.pttl.mobile.manager.domain.entity.ExtendedBusiness">
    <!--@mbg.generated-->
    update mm_extended_business
    <set>
      <if test="dataKey != null and dataKey != ''">
        data_key = #{dataKey,jdbcType=VARCHAR},
      </if>
      <if test="dataStr != null and dataStr != ''">
        data_str = #{dataStr,jdbcType=VARCHAR},
      </if>
      <if test="dataText != null and dataText != ''">
        data_text = #{dataText,jdbcType=LONGVARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null and remark != ''">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.pttl.mobile.manager.domain.entity.ExtendedBusiness">
    <!--@mbg.generated-->
    update mm_extended_business
    set data_key = #{dataKey,jdbcType=VARCHAR},
      data_str = #{dataStr,jdbcType=VARCHAR},
      data_text = #{dataText,jdbcType=LONGVARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      remark = #{remark,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update mm_extended_business
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="data_key = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.dataKey,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="data_str = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.dataStr,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="data_text = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.dataText,jdbcType=LONGVARCHAR}
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="remark = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.remark,jdbcType=VARCHAR}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update mm_extended_business
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="data_key = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.dataKey != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.dataKey,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="data_str = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.dataStr != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.dataStr,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="data_text = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.dataText != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.dataText,jdbcType=LONGVARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createTime != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateTime != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="remark = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.remark != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.remark,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into mm_extended_business
    (data_key, data_str, data_text, create_time, update_time, remark)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.dataKey,jdbcType=VARCHAR}, #{item.dataStr,jdbcType=VARCHAR}, #{item.dataText,jdbcType=LONGVARCHAR}, 
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}, #{item.remark,jdbcType=VARCHAR}
        )
    </foreach>
  </insert>

  <select id="getByKey" resultMap="BaseResultMap">
    select * from mm_extended_business where data_key = #{vrSwitchKey}
  </select>
</mapper>