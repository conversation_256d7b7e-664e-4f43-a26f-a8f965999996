<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pttl.mobile.manager.mobile.ws.dao.PersonSortInfoMapper">
  <resultMap id="BaseResultMap" type="com.pttl.mobile.manager.mobile.ws.entity.PersonSortInfoDO">
    
    <!--@Table mm_ws_person_sort_info-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="dept_id" jdbcType="INTEGER" property="deptId" />
    <result column="employee_id" jdbcType="VARCHAR" property="employeeId" />
    <result column="sort_id" jdbcType="INTEGER" property="sortId" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
  </resultMap>
  <sql id="Base_Column_List">
    
    id, dept_id, employee_id, sort_id, update_time, remark
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    
    select 
    <include refid="Base_Column_List" />
    from mm_ws_person_sort_info
    where id = #{id,jdbcType=INTEGER}
  </select>
  
  <select id="selectByDeptIdAndEmployeeId" resultType="com.pttl.mobile.manager.mobile.ws.entity.PersonSortInfoDO" parameterType="com.pttl.mobile.manager.domain.dto.AddressBookOfPersonnelRankingDTO">
    select
    <include refid="Base_Column_List" />
    from mm_ws_person_sort_info
    where dept_id = #{deptId} and employee_id = #{employeeId}
   </select>

   <select id="countByDeptId" resultType="java.lang.Integer" parameterType="java.lang.Integer">
        select
        count(id)
        from mm_ws_person_sort_info
        where dept_id = #{deptId}
    </select>

    <select id="listByDeptId" resultType="com.pttl.mobile.manager.mobile.ws.entity.PersonSortInfoDO" parameterType="java.lang.Integer">
        select
        <include refid="Base_Column_List" />
        from mm_ws_person_sort_info
        where dept_id = #{deptId}  ORDER BY sort_id
     </select>

     <select id="listByDeptIdBetweenSortId" resultType="com.pttl.mobile.manager.mobile.ws.entity.PersonSortInfoDO">
        SELECT
            <include refid="Base_Column_List" />
        FROM
            mm_ws_person_sort_info
        WHERE
            dept_id = #{deptId} AND
            sort_id >= #{sortId1}
            AND sort_id &lt;= #{sortId2}
        ORDER BY
            sort_id
     </select>
   
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    
    delete from mm_ws_person_sort_info
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.pttl.mobile.manager.mobile.ws.entity.PersonSortInfoDO" useGeneratedKeys="true">
    
    insert into mm_ws_person_sort_info (dept_id, employee_id, sort_id, 
      update_time, remark)
    values (#{deptId,jdbcType=INTEGER}, #{employeeId,jdbcType=VARCHAR}, #{sortId,jdbcType=INTEGER}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{remark,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.pttl.mobile.manager.mobile.ws.entity.PersonSortInfoDO" useGeneratedKeys="true">
    
    insert into mm_ws_person_sort_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="deptId != null">
        dept_id,
      </if>
      <if test="employeeId != null">
        employee_id,
      </if>
      <if test="sortId != null">
        sort_id,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="remark != null">
        remark,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="deptId != null">
        #{deptId,jdbcType=INTEGER},
      </if>
      <if test="employeeId != null">
        #{employeeId,jdbcType=VARCHAR},
      </if>
      <if test="sortId != null">
        #{sortId,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.pttl.mobile.manager.mobile.ws.entity.PersonSortInfoDO">
    
    update mm_ws_person_sort_info
    <set>
      <if test="deptId != null">
        dept_id = #{deptId,jdbcType=INTEGER},
      </if>
      <if test="employeeId != null">
        employee_id = #{employeeId,jdbcType=VARCHAR},
      </if>
      <if test="sortId != null">
        sort_id = #{sortId,jdbcType=INTEGER},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.pttl.mobile.manager.mobile.ws.entity.PersonSortInfoDO">
    
    update mm_ws_person_sort_info
    set dept_id = #{deptId,jdbcType=INTEGER},
      employee_id = #{employeeId,jdbcType=VARCHAR},
      sort_id = #{sortId,jdbcType=INTEGER},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      remark = #{remark,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    
    update mm_ws_person_sort_info
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="dept_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.deptId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="employee_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.employeeId,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="sort_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.sortId,jdbcType=INTEGER}
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.updateTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="remark = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=INTEGER} then #{item.remark,jdbcType=VARCHAR}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=INTEGER}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    
    insert into mm_ws_person_sort_info
    (dept_id, employee_id, sort_id, update_time, remark)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.deptId,jdbcType=INTEGER}, #{item.employeeId,jdbcType=VARCHAR}, #{item.sortId,jdbcType=INTEGER}, 
        #{item.updateTime,jdbcType=TIMESTAMP}, #{item.remark,jdbcType=VARCHAR})
    </foreach>
  </insert>
</mapper>