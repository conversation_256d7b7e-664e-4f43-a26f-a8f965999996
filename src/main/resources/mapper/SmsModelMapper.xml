<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pttl.mobile.manager.dao.SmsModelMapper">
  <resultMap id="BaseResultMap" type="com.pttl.mobile.manager.domain.entity.SmsModelDO">

    <!--@Table mm_sms_model-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="model_name" jdbcType="VARCHAR" property="modelName" />
    <result column="model_content" jdbcType="LONGVARCHAR" property="modelContent" />
    <result column="reserved_field1" jdbcType="VARCHAR" property="reservedField1" />
    <result column="reserved_field2" jdbcType="VARCHAR" property="reservedField2" />
  </resultMap>
  <sql id="Base_Column_List">

    id, model_name, model_content, reserved_field1, reserved_field2
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">

    select 
    <include refid="Base_Column_List" />
    from mm_sms_model
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">

    delete from mm_sms_model
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.pttl.mobile.manager.domain.entity.SmsModelDO" useGeneratedKeys="true">

    insert into mm_sms_model (model_name, model_content, reserved_field1, 
      reserved_field2)
    values (#{modelName,jdbcType=VARCHAR}, #{modelContent,jdbcType=LONGVARCHAR}, #{reservedField1,jdbcType=VARCHAR}, 
      #{reservedField2,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.pttl.mobile.manager.domain.entity.SmsModelDO" useGeneratedKeys="true">

    insert into mm_sms_model
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="modelName != null and modelName != ''">
        model_name,
      </if>
      <if test="modelContent != null and modelContent != ''">
        model_content,
      </if>
      <if test="reservedField1 != null and reservedField1 != ''">
        reserved_field1,
      </if>
      <if test="reservedField2 != null and reservedField2 != ''">
        reserved_field2,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="modelName != null and modelName != ''">
        #{modelName,jdbcType=VARCHAR},
      </if>
      <if test="modelContent != null and modelContent != ''">
        #{modelContent,jdbcType=LONGVARCHAR},
      </if>
      <if test="reservedField1 != null and reservedField1 != ''">
        #{reservedField1,jdbcType=VARCHAR},
      </if>
      <if test="reservedField2 != null and reservedField2 != ''">
        #{reservedField2,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.pttl.mobile.manager.domain.entity.SmsModelDO">

    update mm_sms_model
    <set>
      <if test="modelName != null and modelName != ''">
        model_name = #{modelName,jdbcType=VARCHAR},
      </if>
      <if test="modelContent != null and modelContent != ''">
        model_content = #{modelContent,jdbcType=LONGVARCHAR},
      </if>
      <if test="reservedField1 != null and reservedField1 != ''">
        reserved_field1 = #{reservedField1,jdbcType=VARCHAR},
      </if>
      <if test="reservedField2 != null and reservedField2 != ''">
        reserved_field2 = #{reservedField2,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.pttl.mobile.manager.domain.entity.SmsModelDO">

    update mm_sms_model
    set model_name = #{modelName,jdbcType=VARCHAR},
      model_content = #{modelContent,jdbcType=LONGVARCHAR},
      reserved_field1 = #{reservedField1,jdbcType=VARCHAR},
      reserved_field2 = #{reservedField2,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateBatch" parameterType="java.util.List">

    update mm_sms_model
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="model_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.modelName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="model_content = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.modelContent,jdbcType=LONGVARCHAR}
        </foreach>
      </trim>
      <trim prefix="reserved_field1 = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.reservedField1,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="reserved_field2 = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.reservedField2,jdbcType=VARCHAR}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">

    update mm_sms_model
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="model_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modelName != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.modelName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="model_content = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modelContent != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.modelContent,jdbcType=LONGVARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="reserved_field1 = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.reservedField1 != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.reservedField1,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="reserved_field2 = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.reservedField2 != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.reservedField2,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">

    insert into mm_sms_model
    (model_name, model_content, reserved_field1, reserved_field2)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.modelName,jdbcType=VARCHAR}, #{item.modelContent,jdbcType=LONGVARCHAR}, #{item.reservedField1,jdbcType=VARCHAR}, 
        #{item.reservedField2,jdbcType=VARCHAR})
    </foreach>
  </insert>

  <select id="list" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from mm_sms_model
  </select>
</mapper>