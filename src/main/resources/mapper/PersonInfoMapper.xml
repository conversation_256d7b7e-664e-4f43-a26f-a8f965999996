<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pttl.mobile.manager.mobile.ws.dao.PersonInfoMapper">
    <resultMap id="BaseResultMap" type="com.pttl.mobile.manager.mobile.ws.entity.PersonInfoDO">
        <!--@Table mm_ws_person_info-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="company" jdbcType="VARCHAR" property="company"/>
        <result column="email_address" jdbcType="VARCHAR" property="emailAddress"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="birthdate" jdbcType="VARCHAR" property="birthdate"/>
        <result column="dept_abbreviation" jdbcType="VARCHAR" property="deptAbbreviation"/>
        <result column="personnel_dept" jdbcType="VARCHAR" property="personnelDept"/>
        <result column="person_count" jdbcType="INTEGER" property="personCount"/>
        <result column="position_nbr" jdbcType="VARCHAR" property="positionNbr"/>
        <result column="phone1" jdbcType="VARCHAR" property="phone1"/>
        <result column="phone" jdbcType="VARCHAR" property="phone"/>
        <result column="employee_rcd" jdbcType="INTEGER" property="employeeRcd"/>
        <result column="hr_status" jdbcType="VARCHAR" property="hrStatus"/>
        <result column="job_level" jdbcType="VARCHAR" property="jobLevel"/>
        <result column="punch_type" jdbcType="VARCHAR" property="punchType"/>
        <result column="address" jdbcType="VARCHAR" property="address"/>
        <result column="sex" jdbcType="VARCHAR" property="sex"/>
        <result column="employee_id" jdbcType="VARCHAR" property="employeeId"/>
        <result column="row_number" jdbcType="INTEGER" property="rowNumber"/>
        <result column="dept_id" jdbcType="VARCHAR" property="deptId"/>
        <result column="company_description" jdbcType="VARCHAR" property="companyDescription"/>
    </resultMap>
    <sql id="Base_Column_List">
        id,
        company,
        email_address,
        `name`,
        birthdate,
        dept_abbreviation,
        personnel_dept,
        person_count,
        position_nbr,
        phone1,
        phone,
        employee_rcd,
        hr_status,
        job_level,
        punch_type,
        address,
        sex,
        employee_id,
        `row_number`,
        dept_id,
        company_description
    </sql>

    <!--领导部门人员时手机号需要脱敏处理时用, 该sql不包含phone(手机号)字段-->
    <sql id="Base_Column_List2">
        id,
        company,
        email_address,
        `name`,
        birthdate,
        dept_abbreviation,
        personnel_dept,
        person_count,
        position_nbr,
        phone1,
        employee_rcd,
        hr_status,
        job_level,
        punch_type,
        address,
        sex,
        employee_id,
        `row_number`,
        dept_id,
        company_description
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mm_ws_person_info
        where id = #{id,jdbcType=BIGINT}
    </select>

    <select id="leadershipDesensitizationByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List2"/>
        ,IF( employee_id IN ( SELECT employee_id FROM mm_ws_person_info WHERE dept_id = 0 AND hr_status = 'A' ), '***********', phone ) AS phone
        FROM mm_ws_person_info
        where id = #{id,jdbcType=BIGINT}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from mm_ws_person_info
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="com.pttl.mobile.manager.mobile.ws.entity.PersonInfoDO" useGeneratedKeys="true">
        insert into mm_ws_person_info (company, email_address, `name`,
                                       birthdate, dept_abbreviation, personnel_dept,
                                       person_count, position_nbr, phone1,
                                       phone, employee_rcd, hr_status,
                                       job_level, punch_type, address,
                                       sex, employee_id, `row_number`,
                                       dept_id, company_description)
        values (#{company,jdbcType=VARCHAR}, #{emailAddress,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR},
                #{birthdate,jdbcType=VARCHAR}, #{deptAbbreviation,jdbcType=VARCHAR}, #{personnelDept,jdbcType=VARCHAR},
                #{personCount,jdbcType=INTEGER}, #{positionNbr,jdbcType=VARCHAR}, #{phone1,jdbcType=VARCHAR},
                #{phone,jdbcType=VARCHAR}, #{employeeRcd,jdbcType=INTEGER}, #{hrStatus,jdbcType=VARCHAR},
                #{jobLevel,jdbcType=VARCHAR}, #{punchType,jdbcType=VARCHAR}, #{address,jdbcType=VARCHAR},
                #{sex,jdbcType=VARCHAR}, #{employeeId,jdbcType=VARCHAR}, #{rowNumber,jdbcType=INTEGER},
                #{deptId,jdbcType=VARCHAR}, #{companyDescription,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.pttl.mobile.manager.mobile.ws.entity.PersonInfoDO" useGeneratedKeys="true">
        insert into mm_ws_person_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="company != null">
                company,
            </if>
            <if test="emailAddress != null">
                email_address,
            </if>
            <if test="name != null">
                `name`,
            </if>
            <if test="birthdate != null">
                birthdate,
            </if>
            <if test="deptAbbreviation != null">
                dept_abbreviation,
            </if>
            <if test="personnelDept != null">
                personnel_dept,
            </if>
            <if test="personCount != null">
                person_count,
            </if>
            <if test="positionNbr != null">
                position_nbr,
            </if>
            <if test="phone1 != null">
                phone1,
            </if>
            <if test="phone != null">
                phone,
            </if>
            <if test="employeeRcd != null">
                employee_rcd,
            </if>
            <if test="hrStatus != null">
                hr_status,
            </if>
            <if test="jobLevel != null">
                job_level,
            </if>
            <if test="punchType != null">
                punch_type,
            </if>
            <if test="address != null">
                address,
            </if>
            <if test="sex != null">
                sex,
            </if>
            <if test="employeeId != null">
                employee_id,
            </if>
            <if test="rowNumber != null">
                `row_number`,
            </if>
            <if test="deptId != null">
                dept_id,
            </if>
            <if test="companyDescription != null">
                company_description,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="company != null">
                #{company,jdbcType=VARCHAR},
            </if>
            <if test="emailAddress != null">
                #{emailAddress,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="birthdate != null">
                #{birthdate,jdbcType=VARCHAR},
            </if>
            <if test="deptAbbreviation != null">
                #{deptAbbreviation,jdbcType=VARCHAR},
            </if>
            <if test="personnelDept != null">
                #{personnelDept,jdbcType=VARCHAR},
            </if>
            <if test="personCount != null">
                #{personCount,jdbcType=INTEGER},
            </if>
            <if test="positionNbr != null">
                #{positionNbr,jdbcType=VARCHAR},
            </if>
            <if test="phone1 != null">
                #{phone1,jdbcType=VARCHAR},
            </if>
            <if test="phone != null">
                #{phone,jdbcType=VARCHAR},
            </if>
            <if test="employeeRcd != null">
                #{employeeRcd,jdbcType=INTEGER},
            </if>
            <if test="hrStatus != null">
                #{hrStatus,jdbcType=VARCHAR},
            </if>
            <if test="jobLevel != null">
                #{jobLevel,jdbcType=VARCHAR},
            </if>
            <if test="punchType != null">
                #{punchType,jdbcType=VARCHAR},
            </if>
            <if test="address != null">
                #{address,jdbcType=VARCHAR},
            </if>
            <if test="sex != null">
                #{sex,jdbcType=VARCHAR},
            </if>
            <if test="employeeId != null">
                #{employeeId,jdbcType=VARCHAR},
            </if>
            <if test="rowNumber != null">
                #{rowNumber,jdbcType=INTEGER},
            </if>
            <if test="deptId != null">
                #{deptId,jdbcType=VARCHAR},
            </if>
            <if test="companyDescription != null">
                #{companyDescription,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.pttl.mobile.manager.mobile.ws.entity.PersonInfoDO">
        update mm_ws_person_info
        <set>
            <if test="company != null">
                company = #{company,jdbcType=VARCHAR},
            </if>
            <if test="emailAddress != null">
                email_address = #{emailAddress,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                `name` = #{name,jdbcType=VARCHAR},
            </if>
            <if test="birthdate != null">
                birthdate = #{birthdate,jdbcType=VARCHAR},
            </if>
            <if test="deptAbbreviation != null">
                dept_abbreviation = #{deptAbbreviation,jdbcType=VARCHAR},
            </if>
            <if test="personnelDept != null">
                personnel_dept = #{personnelDept,jdbcType=VARCHAR},
            </if>
            <if test="personCount != null">
                person_count = #{personCount,jdbcType=INTEGER},
            </if>
            <if test="positionNbr != null">
                position_nbr = #{positionNbr,jdbcType=VARCHAR},
            </if>
            <if test="phone1 != null">
                phone1 = #{phone1,jdbcType=VARCHAR},
            </if>
            <if test="phone != null">
                phone = #{phone,jdbcType=VARCHAR},
            </if>
            <if test="employeeRcd != null">
                employee_rcd = #{employeeRcd,jdbcType=INTEGER},
            </if>
            <if test="hrStatus != null">
                hr_status = #{hrStatus,jdbcType=VARCHAR},
            </if>
            <if test="jobLevel != null">
                job_level = #{jobLevel,jdbcType=VARCHAR},
            </if>
            <if test="punchType != null">
                punch_type = #{punchType,jdbcType=VARCHAR},
            </if>
            <if test="address != null">
                address = #{address,jdbcType=VARCHAR},
            </if>
            <if test="sex != null">
                sex = #{sex,jdbcType=VARCHAR},
            </if>
            <if test="employeeId != null">
                employee_id = #{employeeId,jdbcType=VARCHAR},
            </if>
            <if test="rowNumber != null">
                `row_number` = #{rowNumber,jdbcType=INTEGER},
            </if>
            <if test="deptId != null">
                dept_id = #{deptId,jdbcType=VARCHAR},
            </if>
            <if test="companyDescription != null">
                company_description = #{companyDescription,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.pttl.mobile.manager.mobile.ws.entity.PersonInfoDO">
        update mm_ws_person_info
        set company             = #{company,jdbcType=VARCHAR},
            email_address       = #{emailAddress,jdbcType=VARCHAR},
            `name`              = #{name,jdbcType=VARCHAR},
            birthdate           = #{birthdate,jdbcType=VARCHAR},
            dept_abbreviation   = #{deptAbbreviation,jdbcType=VARCHAR},
            personnel_dept      = #{personnelDept,jdbcType=VARCHAR},
            person_count        = #{personCount,jdbcType=INTEGER},
            position_nbr        = #{positionNbr,jdbcType=VARCHAR},
            phone1              = #{phone1,jdbcType=VARCHAR},
            phone               = #{phone,jdbcType=VARCHAR},
            employee_rcd        = #{employeeRcd,jdbcType=INTEGER},
            hr_status           = #{hrStatus,jdbcType=VARCHAR},
            job_level           = #{jobLevel,jdbcType=VARCHAR},
            punch_type          = #{punchType,jdbcType=VARCHAR},
            address             = #{address,jdbcType=VARCHAR},
            sex                 = #{sex,jdbcType=VARCHAR},
            employee_id         = #{employeeId,jdbcType=VARCHAR},
            `row_number`        = #{rowNumber,jdbcType=INTEGER},
            dept_id             = #{deptId,jdbcType=VARCHAR},
            company_description = #{companyDescription,jdbcType=VARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateBatch" parameterType="java.util.List">
        update mm_ws_person_info
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="company = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.company,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="email_address = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.emailAddress,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="`name` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.name,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="birthdate = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.birthdate,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="dept_abbreviation = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.deptAbbreviation,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="personnel_dept = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.personnelDept,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="person_count = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.personCount,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="position_nbr = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.positionNbr,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="phone1 = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.phone1,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="phone = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.phone,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="employee_rcd = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.employeeRcd,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="hr_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.hrStatus,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="job_level = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.jobLevel,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="punch_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.punchType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="address = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.address,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="sex = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.sex,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="employee_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.employeeId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="`row_number` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.rowNumber,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="dept_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.deptId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="company_description = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.companyDescription,jdbcType=VARCHAR}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        insert into mm_ws_person_info
                (company, email_address, `name`, birthdate, dept_abbreviation, personnel_dept, person_count,
                 position_nbr, phone1, phone, employee_rcd, hr_status, job_level, punch_type, address,
                 sex, employee_id, `row_number`, dept_id, company_description)
                values
        <foreach collection="list" item="item" separator=",">
            (#{item.company,jdbcType=VARCHAR}, #{item.emailAddress,jdbcType=VARCHAR}, #{item.name,jdbcType=VARCHAR},
             #{item.birthdate,jdbcType=VARCHAR}, #{item.deptAbbreviation,jdbcType=VARCHAR},
             #{item.personnelDept,jdbcType=VARCHAR}, #{item.personCount,jdbcType=INTEGER},
             #{item.positionNbr,jdbcType=VARCHAR},
             #{item.phone1,jdbcType=VARCHAR}, #{item.phone,jdbcType=VARCHAR}, #{item.employeeRcd,jdbcType=INTEGER},
             #{item.hrStatus,jdbcType=VARCHAR}, #{item.jobLevel,jdbcType=VARCHAR}, #{item.punchType,jdbcType=VARCHAR},
             #{item.address,jdbcType=VARCHAR}, #{item.sex,jdbcType=VARCHAR}, #{item.employeeId,jdbcType=VARCHAR},
             #{item.rowNumber,jdbcType=INTEGER}, #{item.deptId,jdbcType=VARCHAR},
             #{item.companyDescription,jdbcType=VARCHAR})
        </foreach>
    </insert>

    <update id="cleanTable">
        TRUNCATE TABLE mm_ws_person_info
    </update>

    <select id="listByNameAndDeptIdList" resultMap="BaseResultMap"
            resultType="com.pttl.mobile.manager.mobile.ws.entity.PersonInfoDO">
        select
        <include refid="Base_Column_List"/>
        FROM
	    mm_ws_person_info
        <where>
            <if test="nameDesc != null and nameDesc != ''">
              and `name` like concat('%', #{nameDesc}, '%')
            </if>

            <if test="list != null and list.size() > 0">
                 and dept_id in
                <foreach close=")" collection="list" item="item" open="(" separator=", ">
                    #{item,jdbcType=BIGINT}
                </foreach>
            </if>

            <!--离职为 I 在职为 A-->
            and hr_status = 'A'
        </where>

    </select>

    <select id="selectByEmployeeId" resultType="com.pttl.mobile.manager.mobile.ws.entity.PersonInfoDO" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from mm_ws_person_info
        where employee_id = #{employeeId}
        <!--离职为 I 在职为 A-->
            and hr_status = 'A'  LIMIT 0, 1
    </select>

    <select id="selectOneByEmployeeId" resultType="com.pttl.mobile.manager.mobile.ws.entity.PersonInfoDO" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from mm_ws_person_info
        where employee_id = #{employeeId}  LIMIT 0, 1
    </select>

    <select id="countByDeptIds" resultType="java.lang.Integer">
        select
            COUNT(id)
        FROM
	    mm_ws_person_info
        <where>
             dept_id in
            <foreach close=")" collection="deptIds" item="item" open="(" separator=", ">
                #{item}
            </foreach>

            <!--离职为 I 在职为 A-->
            and hr_status = 'A'
        </where>
    </select>

    <select id="listByNameAndDeptId" resultType="com.pttl.mobile.manager.mobile.dto.PersonInfoDTO">
        select
        <include refid="Base_Column_List"/>
        FROM
	    mm_ws_person_info
        <where>
            <if test="queryVO.nameDesc != null and queryVO.nameDesc != ''">
              and `name` like concat('%', #{queryVO.nameDesc}, '%')
            </if>
            <if test="queryVO.nameDesc != null and queryVO.nameDesc != ''">
              or `email_address` = #{queryVO.nameDesc}
            </if>
            <if test="queryVO.nameDesc != null and queryVO.nameDesc != ''">
              or `phone` = #{queryVO.nameDesc}
            </if>
            <if test="queryVO.nameDesc != null and queryVO.nameDesc != ''">
                or `employee_id` like concat('%', #{queryVO.nameDesc}, '%')
            </if>

            <if test="queryVO.deptId != null">
                and dept_id = #{queryVO.deptId}
            </if>
            <!--离职为 I 在职为 A-->
            and hr_status = 'A'
        </where>
    </select>

    <select id="listByDeptId" resultType="com.pttl.mobile.manager.mobile.ws.entity.PersonInfoDO" parameterType="java.lang.Integer">
        select
        <include refid="Base_Column_List"/>
        FROM
	    mm_ws_person_info
        <where>
            <if test="deptId != null">
                and dept_id = #{deptId}
            </if>
            <!--离职为 I 在职为 A-->
            and hr_status = 'A'
        </where>
    </select>

    <select id="selectByNamePinYin" resultType="com.pttl.mobile.manager.mobile.ws.entity.PersonInfoDO" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from mm_ws_person_info
        where email_address = #{loginName}
         LIMIT 0, 1
    </select>
</mapper>