<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pttl.mobile.manager.dao.UserMapper">
  <resultMap id="BaseResultMap" type="com.pttl.mobile.manager.domain.entity.UserDO">
    
    <!--@Table mm_user-->
    <id column="id" property="id" />
    <result column="department_id" property="departmentId" />
    <result column="name" property="name" />
    <result column="password" property="password" />
    <result column="email" property="email" />
    <result column="mobile" property="mobile" />
    <result column="login_name" property="loginName" />
    <result column="position_name" property="positionName" />
    <result column="modify_password_wrong_count" property="modifyPasswordWrongCount" />
    <result column="modify_password_wrong_time" property="modifyPasswordWrongTime" />
    <result column="avatar_path" property="avatarPath" />
    <result column="employee_no" property="employeeNo" />
    <result column="person_level" property="personLevel" />
    <result column="locality" property="locality" />
    <result column="telephone" property="telephone" />
    <result column="source" property="source" />
    <result column="valid_status" property="validStatus" />
    <result column="activated_status" property="activatedStatus" />
    <result column="activated_time" property="activatedTime" />
    <result column="disabled_status" property="disabledStatus" />
    <result column="last_login_time" property="lastLoginTime" />
    <result column="failed_login_times" property="failedLoginTimes" />
    <result column="device_num" property="deviceNum" />
    <result column="device_switch_status" property="deviceSwitchStatus" />
    <result column="temp_device_num" property="tempDeviceNum" />
    <result column="temp_device_num_expire_time" property="tempDeviceNumExpireTime" />
    <result column="create_time" property="createTime" />
    <result column="update_time" property="updateTime" />
    <result column="ad_dn" property="adDn" />
    <result column="remark" property="remark" />
    <result column="extension1" property="extension1" />
    <result column="extension2" property="extension2" />
    <result column="extension3" property="extension3" />
  </resultMap>
  <sql id="Base_Column_List">
    
    id, department_id, `name`, `password`, email, mobile, login_name, position_name, 
    modify_password_wrong_count, modify_password_wrong_time, avatar_path, employee_no, 
    person_level, locality, telephone, `source`, valid_status, activated_status, activated_time, 
    disabled_status, last_login_time, failed_login_times, device_num, device_switch_status, 
    temp_device_num, temp_device_num_expire_time, create_time, update_time, ad_dn, remark, 
    extension1, extension2, extension3
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    
    select 
    <include refid="Base_Column_List" />
    from mm_user
    where id = #{id}
  </select>

  <select id="selectByLoginName" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from mm_user
    where login_name = #{loginName}
  </select>

  <!--分页查询用户信息-->
  <select id="pageInfo" parameterType="java.lang.String"
   resultType="com.pttl.mobile.manager.domain.entity.UserDO" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from mm_user
    <where>
        <if test="name != null">
          and name like concat('%', #{name}, '%')
        </if>
        <if test="departmentId != null">
          and department_id = #{departmentId}
        </if>
          and valid_status = ${@com.pttl.mobile.manager.domain.entity.UserDO@VALID_STATUS_VALID}
    </where>
    ORDER BY update_time DESC
  </select>

  <select id="countByDepartmentId" resultType="java.lang.Integer" parameterType="java.lang.Long">
        select count(id) from mm_user where department_id = #{departmentId}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    
    delete from mm_user
    where id = #{id}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.pttl.mobile.manager.domain.entity.UserDO" useGeneratedKeys="true">
    
    insert into mm_user (department_id, `name`, `password`, email, mobile, login_name, 
      position_name, modify_password_wrong_count, modify_password_wrong_time, avatar_path, 
      employee_no, person_level, locality, telephone, `source`, valid_status, 
      activated_status, activated_time, disabled_status, last_login_time, failed_login_times, 
      device_num, device_switch_status, temp_device_num, temp_device_num_expire_time, 
      create_time, update_time, ad_dn, remark, extension1, extension2, 
      extension3)
    values (#{departmentId}, #{name}, #{password}, #{email}, #{mobile}, #{loginName}, 
      #{positionName}, #{modifyPasswordWrongCount}, #{modifyPasswordWrongTime}, #{avatarPath}, 
      #{employeeNo}, #{personLevel}, #{locality}, #{telephone}, #{source}, #{validStatus}, 
      #{activatedStatus}, #{activatedTime}, #{disabledStatus}, #{lastLoginTime}, #{failedLoginTimes}, 
      #{deviceNum}, #{deviceSwitchStatus}, #{tempDeviceNum}, #{tempDeviceNumExpireTime}, 
      #{createTime}, #{updateTime}, #{adDn}, #{remark}, #{extension1}, #{extension2}, 
      #{extension3})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.pttl.mobile.manager.domain.entity.UserDO" useGeneratedKeys="true">
    
    insert into mm_user
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="departmentId != null">
        department_id,
      </if>
      <if test="name != null">
        `name`,
      </if>
      <if test="password != null">
        `password`,
      </if>
      <if test="email != null">
        email,
      </if>
      <if test="mobile != null">
        mobile,
      </if>
      <if test="loginName != null">
        login_name,
      </if>
      <if test="positionName != null">
        position_name,
      </if>
      <if test="modifyPasswordWrongCount != null">
        modify_password_wrong_count,
      </if>
      <if test="modifyPasswordWrongTime != null">
        modify_password_wrong_time,
      </if>
      <if test="avatarPath != null">
        avatar_path,
      </if>
      <if test="employeeNo != null">
        employee_no,
      </if>
      <if test="personLevel != null">
        person_level,
      </if>
      <if test="locality != null">
        locality,
      </if>
      <if test="telephone != null">
        telephone,
      </if>
      <if test="source != null">
        `source`,
      </if>
      <if test="validStatus != null">
        valid_status,
      </if>
      <if test="activatedStatus != null">
        activated_status,
      </if>
      <if test="activatedTime != null">
        activated_time,
      </if>
      <if test="disabledStatus != null">
        disabled_status,
      </if>
      <if test="lastLoginTime != null">
        last_login_time,
      </if>
      <if test="failedLoginTimes != null">
        failed_login_times,
      </if>
      <if test="deviceNum != null">
        device_num,
      </if>
      <if test="deviceSwitchStatus != null">
        device_switch_status,
      </if>
      <if test="tempDeviceNum != null">
        temp_device_num,
      </if>
      <if test="tempDeviceNumExpireTime != null">
        temp_device_num_expire_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="adDn != null">
        ad_dn,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="extension1 != null">
        extension1,
      </if>
      <if test="extension2 != null">
        extension2,
      </if>
      <if test="extension3 != null">
        extension3,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="departmentId != null">
        #{departmentId},
      </if>
      <if test="name != null">
        #{name},
      </if>
      <if test="password != null">
        #{password},
      </if>
      <if test="email != null">
        #{email},
      </if>
      <if test="mobile != null">
        #{mobile},
      </if>
      <if test="loginName != null">
        #{loginName},
      </if>
      <if test="positionName != null">
        #{positionName},
      </if>
      <if test="modifyPasswordWrongCount != null">
        #{modifyPasswordWrongCount},
      </if>
      <if test="modifyPasswordWrongTime != null">
        #{modifyPasswordWrongTime},
      </if>
      <if test="avatarPath != null">
        #{avatarPath},
      </if>
      <if test="employeeNo != null">
        #{employeeNo},
      </if>
      <if test="personLevel != null">
        #{personLevel},
      </if>
      <if test="locality != null">
        #{locality},
      </if>
      <if test="telephone != null">
        #{telephone},
      </if>
      <if test="source != null">
        #{source},
      </if>
      <if test="validStatus != null">
        #{validStatus},
      </if>
      <if test="activatedStatus != null">
        #{activatedStatus},
      </if>
      <if test="activatedTime != null">
        #{activatedTime},
      </if>
      <if test="disabledStatus != null">
        #{disabledStatus},
      </if>
      <if test="lastLoginTime != null">
        #{lastLoginTime},
      </if>
      <if test="failedLoginTimes != null">
        #{failedLoginTimes},
      </if>
      <if test="deviceNum != null">
        #{deviceNum},
      </if>
      <if test="deviceSwitchStatus != null">
        #{deviceSwitchStatus},
      </if>
      <if test="tempDeviceNum != null">
        #{tempDeviceNum},
      </if>
      <if test="tempDeviceNumExpireTime != null">
        #{tempDeviceNumExpireTime},
      </if>
      <if test="createTime != null">
        #{createTime},
      </if>
      <if test="updateTime != null">
        #{updateTime},
      </if>
      <if test="adDn != null">
        #{adDn},
      </if>
      <if test="remark != null">
        #{remark},
      </if>
      <if test="extension1 != null">
        #{extension1},
      </if>
      <if test="extension2 != null">
        #{extension2},
      </if>
      <if test="extension3 != null">
        #{extension3},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.pttl.mobile.manager.domain.entity.UserDO">
    
    update mm_user
    <set>
      <if test="departmentId != null">
        department_id = #{departmentId},
      </if>
      <if test="name != null">
        `name` = #{name},
      </if>
      <if test="password != null">
        `password` = #{password},
      </if>
      <if test="email != null">
        email = #{email},
      </if>
      <if test="mobile != null">
        mobile = #{mobile},
      </if>
      <if test="loginName != null">
        login_name = #{loginName},
      </if>
      <if test="positionName != null">
        position_name = #{positionName},
      </if>
      <if test="modifyPasswordWrongCount != null">
        modify_password_wrong_count = #{modifyPasswordWrongCount},
      </if>
      <if test="modifyPasswordWrongTime != null">
        modify_password_wrong_time = #{modifyPasswordWrongTime},
      </if>
      <if test="avatarPath != null">
        avatar_path = #{avatarPath},
      </if>
      <if test="employeeNo != null">
        employee_no = #{employeeNo},
      </if>
      <if test="personLevel != null">
        person_level = #{personLevel},
      </if>
      <if test="locality != null">
        locality = #{locality},
      </if>
      <if test="telephone != null">
        telephone = #{telephone},
      </if>
      <if test="source != null">
        `source` = #{source},
      </if>
      <if test="validStatus != null">
        valid_status = #{validStatus},
      </if>
      <if test="activatedStatus != null">
        activated_status = #{activatedStatus},
      </if>
      <if test="activatedTime != null">
        activated_time = #{activatedTime},
      </if>
      <if test="disabledStatus != null">
        disabled_status = #{disabledStatus},
      </if>
      <if test="lastLoginTime != null">
        last_login_time = #{lastLoginTime},
      </if>
      <if test="failedLoginTimes != null">
        failed_login_times = #{failedLoginTimes},
      </if>
      <if test="deviceNum != null">
        device_num = #{deviceNum},
      </if>
      <if test="deviceSwitchStatus != null">
        device_switch_status = #{deviceSwitchStatus},
      </if>
      <if test="tempDeviceNum != null">
        temp_device_num = #{tempDeviceNum},
      </if>
      <if test="tempDeviceNumExpireTime != null">
        temp_device_num_expire_time = #{tempDeviceNumExpireTime},
      </if>
      <if test="createTime != null">
        create_time = #{createTime},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime},
      </if>
      <if test="adDn != null">
        ad_dn = #{adDn},
      </if>
      <if test="remark != null">
        remark = #{remark},
      </if>
      <if test="extension1 != null">
        extension1 = #{extension1},
      </if>
      <if test="extension2 != null">
        extension2 = #{extension2},
      </if>
      <if test="extension3 != null">
        extension3 = #{extension3},
      </if>
    </set>
    where id = #{id}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.pttl.mobile.manager.domain.entity.UserDO">
    
    update mm_user
    set department_id = #{departmentId},
      `name` = #{name},
      `password` = #{password},
      email = #{email},
      mobile = #{mobile},
      login_name = #{loginName},
      position_name = #{positionName},
      modify_password_wrong_count = #{modifyPasswordWrongCount},
      modify_password_wrong_time = #{modifyPasswordWrongTime},
      avatar_path = #{avatarPath},
      employee_no = #{employeeNo},
      person_level = #{personLevel},
      locality = #{locality},
      telephone = #{telephone},
      `source` = #{source},
      valid_status = #{validStatus},
      activated_status = #{activatedStatus},
      activated_time = #{activatedTime},
      disabled_status = #{disabledStatus},
      last_login_time = #{lastLoginTime},
      failed_login_times = #{failedLoginTimes},
      device_num = #{deviceNum},
      device_switch_status = #{deviceSwitchStatus},
      temp_device_num = #{tempDeviceNum},
      temp_device_num_expire_time = #{tempDeviceNumExpireTime},
      create_time = #{createTime},
      update_time = #{updateTime},
      ad_dn = #{adDn},
      remark = #{remark},
      extension1 = #{extension1},
      extension2 = #{extension2},
      extension3 = #{extension3}
    where id = #{id}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    
    update mm_user
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="department_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.departmentId}
        </foreach>
      </trim>
      <trim prefix="`name` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.name}
        </foreach>
      </trim>
      <trim prefix="`password` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.password}
        </foreach>
      </trim>
      <trim prefix="email = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.email}
        </foreach>
      </trim>
      <trim prefix="mobile = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.mobile}
        </foreach>
      </trim>
      <trim prefix="login_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.loginName}
        </foreach>
      </trim>
      <trim prefix="position_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.positionName}
        </foreach>
      </trim>
      <trim prefix="modify_password_wrong_count = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.modifyPasswordWrongCount}
        </foreach>
      </trim>
      <trim prefix="modify_password_wrong_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.modifyPasswordWrongTime}
        </foreach>
      </trim>
      <trim prefix="avatar_path = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.avatarPath}
        </foreach>
      </trim>
      <trim prefix="employee_no = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.employeeNo}
        </foreach>
      </trim>
      <trim prefix="person_level = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.personLevel}
        </foreach>
      </trim>
      <trim prefix="locality = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.locality}
        </foreach>
      </trim>
      <trim prefix="telephone = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.telephone}
        </foreach>
      </trim>
      <trim prefix="`source` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.source}
        </foreach>
      </trim>
      <trim prefix="valid_status = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.validStatus}
        </foreach>
      </trim>
      <trim prefix="activated_status = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.activatedStatus}
        </foreach>
      </trim>
      <trim prefix="activated_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.activatedTime}
        </foreach>
      </trim>
      <trim prefix="disabled_status = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.disabledStatus}
        </foreach>
      </trim>
      <trim prefix="last_login_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.lastLoginTime}
        </foreach>
      </trim>
      <trim prefix="failed_login_times = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.failedLoginTimes}
        </foreach>
      </trim>
      <trim prefix="device_num = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.deviceNum}
        </foreach>
      </trim>
      <trim prefix="device_switch_status = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.deviceSwitchStatus}
        </foreach>
      </trim>
      <trim prefix="temp_device_num = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.tempDeviceNum}
        </foreach>
      </trim>
      <trim prefix="temp_device_num_expire_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.tempDeviceNumExpireTime}
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.createTime}
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.updateTime}
        </foreach>
      </trim>
      <trim prefix="ad_dn = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.adDn}
        </foreach>
      </trim>
      <trim prefix="remark = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.remark}
        </foreach>
      </trim>
      <trim prefix="extension1 = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.extension1}
        </foreach>
      </trim>
      <trim prefix="extension2 = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.extension2}
        </foreach>
      </trim>
      <trim prefix="extension3 = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.extension3}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    
    update mm_user
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="department_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.departmentId != null">
            when id = #{item.id} then #{item.departmentId}
          </if>
        </foreach>
      </trim>
      <trim prefix="`name` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.name != null">
            when id = #{item.id} then #{item.name}
          </if>
        </foreach>
      </trim>
      <trim prefix="`password` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.password != null">
            when id = #{item.id} then #{item.password}
          </if>
        </foreach>
      </trim>
      <trim prefix="email = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.email != null">
            when id = #{item.id} then #{item.email}
          </if>
        </foreach>
      </trim>
      <trim prefix="mobile = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.mobile != null">
            when id = #{item.id} then #{item.mobile}
          </if>
        </foreach>
      </trim>
      <trim prefix="login_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.loginName != null">
            when id = #{item.id} then #{item.loginName}
          </if>
        </foreach>
      </trim>
      <trim prefix="position_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.positionName != null">
            when id = #{item.id} then #{item.positionName}
          </if>
        </foreach>
      </trim>
      <trim prefix="modify_password_wrong_count = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modifyPasswordWrongCount != null">
            when id = #{item.id} then #{item.modifyPasswordWrongCount}
          </if>
        </foreach>
      </trim>
      <trim prefix="modify_password_wrong_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modifyPasswordWrongTime != null">
            when id = #{item.id} then #{item.modifyPasswordWrongTime}
          </if>
        </foreach>
      </trim>
      <trim prefix="avatar_path = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.avatarPath != null">
            when id = #{item.id} then #{item.avatarPath}
          </if>
        </foreach>
      </trim>
      <trim prefix="employee_no = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.employeeNo != null">
            when id = #{item.id} then #{item.employeeNo}
          </if>
        </foreach>
      </trim>
      <trim prefix="person_level = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.personLevel != null">
            when id = #{item.id} then #{item.personLevel}
          </if>
        </foreach>
      </trim>
      <trim prefix="locality = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.locality != null">
            when id = #{item.id} then #{item.locality}
          </if>
        </foreach>
      </trim>
      <trim prefix="telephone = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.telephone != null">
            when id = #{item.id} then #{item.telephone}
          </if>
        </foreach>
      </trim>
      <trim prefix="`source` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.source != null">
            when id = #{item.id} then #{item.source}
          </if>
        </foreach>
      </trim>
      <trim prefix="valid_status = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.validStatus != null">
            when id = #{item.id} then #{item.validStatus}
          </if>
        </foreach>
      </trim>
      <trim prefix="activated_status = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.activatedStatus != null">
            when id = #{item.id} then #{item.activatedStatus}
          </if>
        </foreach>
      </trim>
      <trim prefix="activated_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.activatedTime != null">
            when id = #{item.id} then #{item.activatedTime}
          </if>
        </foreach>
      </trim>
      <trim prefix="disabled_status = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.disabledStatus != null">
            when id = #{item.id} then #{item.disabledStatus}
          </if>
        </foreach>
      </trim>
      <trim prefix="last_login_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.lastLoginTime != null">
            when id = #{item.id} then #{item.lastLoginTime}
          </if>
        </foreach>
      </trim>
      <trim prefix="failed_login_times = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.failedLoginTimes != null">
            when id = #{item.id} then #{item.failedLoginTimes}
          </if>
        </foreach>
      </trim>
      <trim prefix="device_num = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.deviceNum != null">
            when id = #{item.id} then #{item.deviceNum}
          </if>
        </foreach>
      </trim>
      <trim prefix="device_switch_status = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.deviceSwitchStatus != null">
            when id = #{item.id} then #{item.deviceSwitchStatus}
          </if>
        </foreach>
      </trim>
      <trim prefix="temp_device_num = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.tempDeviceNum != null">
            when id = #{item.id} then #{item.tempDeviceNum}
          </if>
        </foreach>
      </trim>
      <trim prefix="temp_device_num_expire_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.tempDeviceNumExpireTime != null">
            when id = #{item.id} then #{item.tempDeviceNumExpireTime}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createTime != null">
            when id = #{item.id} then #{item.createTime}
          </if>
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateTime != null">
            when id = #{item.id} then #{item.updateTime}
          </if>
        </foreach>
      </trim>
      <trim prefix="ad_dn = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.adDn != null">
            when id = #{item.id} then #{item.adDn}
          </if>
        </foreach>
      </trim>
      <trim prefix="remark = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.remark != null">
            when id = #{item.id} then #{item.remark}
          </if>
        </foreach>
      </trim>
      <trim prefix="extension1 = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.extension1 != null">
            when id = #{item.id} then #{item.extension1}
          </if>
        </foreach>
      </trim>
      <trim prefix="extension2 = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.extension2 != null">
            when id = #{item.id} then #{item.extension2}
          </if>
        </foreach>
      </trim>
      <trim prefix="extension3 = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.extension3 != null">
            when id = #{item.id} then #{item.extension3}
          </if>
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    
    insert into mm_user
    (department_id, `name`, `password`, email, mobile, login_name, position_name, modify_password_wrong_count, 
      modify_password_wrong_time, avatar_path, employee_no, person_level, locality, telephone, 
      `source`, valid_status, activated_status, activated_time, disabled_status, last_login_time, 
      failed_login_times, device_num, device_switch_status, temp_device_num, temp_device_num_expire_time, 
      create_time, update_time, ad_dn, remark, extension1, extension2, extension3)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.departmentId}, #{item.name}, #{item.password}, #{item.email}, #{item.mobile}, 
        #{item.loginName}, #{item.positionName}, #{item.modifyPasswordWrongCount}, #{item.modifyPasswordWrongTime}, 
        #{item.avatarPath}, #{item.employeeNo}, #{item.personLevel}, #{item.locality}, 
        #{item.telephone}, #{item.source}, #{item.validStatus}, #{item.activatedStatus}, 
        #{item.activatedTime}, #{item.disabledStatus}, #{item.lastLoginTime}, #{item.failedLoginTimes}, 
        #{item.deviceNum}, #{item.deviceSwitchStatus}, #{item.tempDeviceNum}, #{item.tempDeviceNumExpireTime}, 
        #{item.createTime}, #{item.updateTime}, #{item.adDn}, #{item.remark}, #{item.extension1}, 
        #{item.extension2}, #{item.extension3})
    </foreach>
  </insert>
</mapper>