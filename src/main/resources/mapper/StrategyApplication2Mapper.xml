<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pttl.mobile.manager.dao.StrategyApplication2Mapper">
  <resultMap id="BaseResultMap" type="com.pttl.mobile.manager.domain.entity.StrategyApplication2DO">
    
    <!--@Table mm_strategy_application-->
    <id column="id" property="id" />
    <result column="strategy_id" property="strategyId" />
    <result column="application_id" property="applicationId" />
  </resultMap>
  <sql id="Base_Column_List">
    
    id, strategy_id, application_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    
    select 
    <include refid="Base_Column_List" />
    from mm_strategy_application
    where id = #{id}
  </select>

  <select id="listByStrategyId" resultType="com.pttl.mobile.manager.domain.dto.ApplicationDTO" >
        SELECT
            app.id,
            app.application_group_id,
            app.NAME,
            app.description,
            app.logo_url,
            app.package_url,
            app.package_name,
            app.address,
            app.inner_address,
            app.type,
            app.is_visible,
            app.create_date,
            app.last_update,
            app.version,
            app.system_platform,
            app.env,
						appgroup.name AS applicationGroupName
        FROM
            application app LEFT JOIN application_group appgroup
						ON app.application_group_id = appgroup.id
        WHERE
            app.id IN (
            SELECT
                <!-- 由于表结构设计问题, 导致编码不一致 这里需要设置关联编码 -->
                sa.application_id  COLLATE utf8mb4_unicode_ci
            FROM
                mm_strategy_application sa
            WHERE
            sa.strategy_id =  #{strategyId}
            )
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    
    delete from mm_strategy_application
    where id = #{id}
  </delete>
  
 <!-- 根据策略id删除-->
  <delete id="deleteByStrategyId" parameterType="java.lang.Long">
    delete from mm_strategy_application
    where strategy_id = #{strategyId}
  </delete>
   
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.pttl.mobile.manager.domain.entity.StrategyApplication2DO" useGeneratedKeys="true">
    
    insert into mm_strategy_application (strategy_id, application_id)
    values (#{strategyId}, #{applicationId})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.pttl.mobile.manager.domain.entity.StrategyApplication2DO" useGeneratedKeys="true">
    
    insert into mm_strategy_application
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="strategyId != null">
        strategy_id,
      </if>
      <if test="applicationId != null">
        application_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="strategyId != null">
        #{strategyId},
      </if>
      <if test="applicationId != null">
        #{applicationId},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.pttl.mobile.manager.domain.entity.StrategyApplication2DO">
    
    update mm_strategy_application
    <set>
      <if test="strategyId != null">
        strategy_id = #{strategyId},
      </if>
      <if test="applicationId != null">
        application_id = #{applicationId},
      </if>
    </set>
    where id = #{id}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.pttl.mobile.manager.domain.entity.StrategyApplication2DO">
    
    update mm_strategy_application
    set strategy_id = #{strategyId},
      application_id = #{applicationId}
    where id = #{id}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    
    update mm_strategy_application
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="strategy_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.strategyId}
        </foreach>
      </trim>
      <trim prefix="application_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.applicationId}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    
    update mm_strategy_application
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="strategy_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.strategyId != null">
            when id = #{item.id} then #{item.strategyId}
          </if>
        </foreach>
      </trim>
      <trim prefix="application_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.applicationId != null">
            when id = #{item.id} then #{item.applicationId}
          </if>
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    
    insert into mm_strategy_application
    (strategy_id, application_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.strategyId}, #{item.applicationId})
    </foreach>
  </insert>
</mapper>