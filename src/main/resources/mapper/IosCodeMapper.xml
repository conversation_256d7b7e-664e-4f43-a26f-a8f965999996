<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pttl.mobile.manager.dao.IosCodeMapper">
    <resultMap id="BaseResultMap" type="com.pttl.mobile.manager.domain.entity.IosCodeDO">
        <!--@Table mm_ios_code-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="code_link" jdbcType="VARCHAR" property="codeLink"/>
        <result column="valid_status" jdbcType="TINYINT" property="validStatus"/>
        <result column="import_time" jdbcType="TIMESTAMP" property="importTime"/>
        <result column="failure_time" jdbcType="TIMESTAMP" property="failureTime"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="app_type" jdbcType="TINYINT" property="appType"/>
    </resultMap>
    <sql id="Base_Column_List">
        id,
        code,
        code_link,
        valid_status,
        import_time,
        failure_time,
        remark,
        app_type
    </sql>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from mm_ios_code
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.pttl.mobile.manager.domain.entity.IosCodeDO" useGeneratedKeys="true">
        insert into mm_ios_code
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="code != null">
                code,
            </if>
            <if test="codeLink != null">
                code_link,
            </if>
            <if test="validStatus != null">
                valid_status,
            </if>
            <if test="importTime != null">
                import_time,
            </if>
            <if test="failureTime != null">
                failure_time,
            </if>
            <if test="remark != null">
                remark,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="code != null">
                #{code,jdbcType=VARCHAR},
            </if>
            <if test="codeLink != null">
                #{codeLink,jdbcType=VARCHAR},
            </if>
            <if test="validStatus != null">
                #{validStatus,jdbcType=TINYINT},
            </if>
            <if test="importTime != null">
                #{importTime,jdbcType=TIMESTAMP},
            </if>
            <if test="failureTime != null">
                #{failureTime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        insert into mm_ios_code
                (code, code_link, valid_status, import_time, failure_time, remark, app_type)
                values
        <foreach collection="list" item="item" separator=",">
            (#{item.code,jdbcType=VARCHAR}, #{item.codeLink,jdbcType=VARCHAR}, #{item.validStatus,jdbcType=TINYINT},
             #{item.importTime,jdbcType=TIMESTAMP}, #{item.failureTime,jdbcType=TIMESTAMP},
             #{item.remark,jdbcType=VARCHAR}, #{item.appType,jdbcType=TINYINT})
        </foreach>
    </insert>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mm_ios_code
        where id = #{id,jdbcType=BIGINT}
    </select>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.pttl.mobile.manager.domain.entity.IosCodeDO"
            useGeneratedKeys="true">
        insert into mm_ios_code (code, code_link, valid_status,
                                 import_time, failure_time, remark)
        values (#{code,jdbcType=VARCHAR}, #{codeLink,jdbcType=VARCHAR}, #{validStatus,jdbcType=TINYINT},
                #{importTime,jdbcType=TIMESTAMP}, #{failureTime,jdbcType=TIMESTAMP}, #{remark,jdbcType=VARCHAR})
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.pttl.mobile.manager.domain.entity.IosCodeDO">
        update mm_ios_code
        <set>
            <if test="code != null">
                code = #{code,jdbcType=VARCHAR},
            </if>
            <if test="codeLink != null">
                code_link = #{codeLink,jdbcType=VARCHAR},
            </if>
            <if test="validStatus != null">
                valid_status = #{validStatus,jdbcType=TINYINT},
            </if>
            <if test="importTime != null">
                import_time = #{importTime,jdbcType=TIMESTAMP},
            </if>
            <if test="failureTime != null">
                failure_time = #{failureTime,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.pttl.mobile.manager.domain.entity.IosCodeDO">
        update mm_ios_code
        set code         = #{code,jdbcType=VARCHAR},
            code_link    = #{codeLink,jdbcType=VARCHAR},
            valid_status = #{validStatus,jdbcType=TINYINT},
            import_time  = #{importTime,jdbcType=TIMESTAMP},
            failure_time = #{failureTime,jdbcType=TIMESTAMP},
            remark       = #{remark,jdbcType=VARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateBatch" parameterType="java.util.List">
        update mm_ios_code
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.code,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="code_link = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.codeLink,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="valid_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.validStatus,jdbcType=TINYINT}
                </foreach>
            </trim>
            <trim prefix="import_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.importTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="failure_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.failureTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <!--根据请求参数 获取分页数据-->
    <select id="listByPageParameter" resultMap="BaseResultMap"
            resultType="com.pttl.mobile.manager.domain.entity.IosCodeDO"
            parameterType="com.pttl.mobile.manager.domain.request.IosCodePageRequest">
        select
        <include refid="Base_Column_List"/>
        from mm_ios_code
        <where>
            <if test="importStartTime != null">
                AND import_time &gt;= DATE_FORMAT(#{importStartTime}, '%Y-%m-%d %H:%i:%S')
            </if>
            <if test="importEndTime != null">
                AND import_time &lt;= DATE_FORMAT(#{importEndTime}, '%Y-%m-%d %H:%i:%S')
            </if>
            <if test="failureStartTime != null">
                AND failure_time &gt;= DATE_FORMAT(#{failureStartTime}, '%Y-%m-%d %H:%i:%S')
            </if>
            <if test="failureEndTime != null">
                AND failure_time &lt;= DATE_FORMAT(#{failureEndTime}, '%Y-%m-%d %H:%i:%S')
            </if>
            <if test="validStatus != null">
                AND valid_status = #{validStatus,jdbcType=TINYINT}
            </if>
        </where>
        ORDER BY import_time DESC
    </select>

    <!--获取最近导入有效的一条数据-->
    <select id="getLatestImportCode" resultType="com.pttl.mobile.manager.domain.entity.IosCodeDO"  parameterType="int">
        SELECT
        <include refid="Base_Column_List" />
        FROM mm_ios_code
        WHERE valid_status = 0 and app_type = #{appType}
        ORDER BY import_time DESC
        LIMIT 1
    </select>

    <!--统计用户使用情况-->
    <select id="getCount" resultType="com.pttl.mobile.manager.domain.dto.UserUsageStatisticsDTO">
        SELECT COUNT(1) AS count_num,
               failure_time
        FROM (SELECT DATE_FORMAT(failure_time, '%Y-%m-%d') AS failure_time FROM mm_ios_code WHERE valid_status = 1) u
        GROUP BY failure_time
        ORDER BY failure_time DESC
        LIMIT 0, 30;
    </select>

    <!--统计未使用情况-->
    <select id="unusedQuantity" resultType="com.pttl.mobile.manager.domain.dto.UserUsageStatisticsDTO">
        SELECT COUNT(1) AS count_num,
               failure_time
        FROM
            ( SELECT DATE_FORMAT( failure_time, '%Y-%m-%d' ) AS failure_time FROM mm_ios_code WHERE valid_status = 0 ) u
        GROUP BY
            failure_time;
    </select>

    <select id="getExchangeStatistics" resultType="com.pttl.mobile.manager.domain.dto.ExchangeStatisticsDTO" parameterType="int">
        SELECT (SELECT count(id) AS converted  FROM mm_ios_code WHERE valid_status = 1  AND app_type = #{appType}) AS converted,
         (SELECT count(id) AS unconverted FROM mm_ios_code WHERE valid_status = 0 AND app_type = #{appType}) AS unconverted
     </select>

     <select id="hourList" resultType="com.pttl.mobile.manager.domain.dto.ExchangeDetailsDTO">
        select COUNT(1) AS total, failure_time from (select DATE_FORMAT(failure_time,'%Y-%m-%d %H:00:00')  AS failure_time
         FROM mm_ios_code WHERE valid_status = 1 AND failure_time > #{dateStr} AND app_type = #{appType}) u  group by failure_time
      </select>

      <select id="daysList" resultType="com.pttl.mobile.manager.domain.dto.ExchangeDetailsDTO">
            select COUNT(1) AS total, failure_time from (select DATE_FORMAT(failure_time,'%Y-%m-%d')  AS failure_time
             FROM mm_ios_code WHERE valid_status = 1 AND failure_time > #{dateStr}  AND app_type = #{appType}) u  group by failure_time
       </select>

       <select id="mouthsList" resultType="com.pttl.mobile.manager.domain.dto.ExchangeDetailsDTO">
            select COUNT(1) AS total, failure_time from (select DATE_FORMAT(failure_time,'%Y-%m')  AS failure_time
            FROM mm_ios_code WHERE valid_status = 1 AND failure_time >  #{dateStr}  AND app_type = #{appType}) u  group by failure_time
        </select>

        <select id="allList" resultType="com.pttl.mobile.manager.domain.dto.ExchangeDetailsDTO" parameterType="int">
            select COUNT(1) AS total, failure_time from (select DATE_FORMAT(failure_time,'%Y')  AS failure_time
            FROM mm_ios_code WHERE valid_status = 1 AND app_type = #{appType}) u  group by failure_time
         </select>

    <select id="statisticsGroupByAppType"
            resultType="com.pttl.mobile.manager.domain.dto.StatisticsUnusedIosCodeQuantityDTO">
        SELECT COUNT(id) AS quantity, app_type FROM `mm_ios_code` WHERE valid_status = 0 AND  app_type is not null GROUP BY app_type
    </select>
</mapper>