<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pttl.mobile.manager.dao.Department2Mapper">
  <resultMap id="BaseResultMap" type="com.pttl.mobile.manager.domain.entity.Department2DO">

    <!--@Table mm_department-->
    <id column="id" property="id" />
    <result column="name" property="name" />
    <result column="description" property="description" />
    <result column="parent_department_id" property="parentDepartmentId" />
    <result column="source" property="source" />
    <result column="full_department_id" property="fullDepartmentId" />
    <result column="user_device_num" property="userDeviceNum" />
    <result column="device_switch_status" property="deviceSwitchStatus" />
    <result column="valid_status" property="validStatus" />
    <result column="create_time" property="createTime" />
    <result column="update_time" property="updateTime" />
    <result column="remark" property="remark" />
  </resultMap>
  <sql id="Base_Column_List">

    id, `name`, description, parent_department_id, `source`, full_department_id, user_device_num, 
    device_switch_status, valid_status, create_time, update_time, remark
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">

    select 
    <include refid="Base_Column_List" />
    from mm_department
    where id = #{id}
  </select>

  <select id="listAll" resultType="com.pttl.mobile.manager.domain.entity.Department2DO" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    <!--默认查有效的-->
    from mm_department where valid_status = ${@com.pttl.mobile.manager.domain.entity.Department2DO@VALID_STATUS_VALID}
  </select>

  <select id="listByParentDepartmentId" resultType="com.pttl.mobile.manager.domain.entity.Department2DO" resultMap="BaseResultMap">
      select
        <include refid="Base_Column_List" />
        <!--默认查有效的-->
      from mm_department where
      valid_status = ${@com.pttl.mobile.manager.domain.entity.Department2DO@VALID_STATUS_VALID}
       and parent_department_id = #{parentDepartmentId}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">

    delete from mm_department
    where id = #{id}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.pttl.mobile.manager.domain.entity.Department2DO" useGeneratedKeys="true">

    insert into mm_department (`name`, description, parent_department_id, `source`, full_department_id, 
      user_device_num, device_switch_status, valid_status, create_time, update_time, 
      remark)
    values (#{name}, #{description}, #{parentDepartmentId}, #{source}, #{fullDepartmentId}, 
      #{userDeviceNum}, #{deviceSwitchStatus}, #{validStatus}, #{createTime}, #{updateTime}, 
      #{remark})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.pttl.mobile.manager.domain.entity.Department2DO" useGeneratedKeys="true">

    insert into mm_department
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="name != null">
        `name`,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="parentDepartmentId != null">
        parent_department_id,
      </if>
      <if test="source != null">
        `source`,
      </if>
      <if test="fullDepartmentId != null">
        full_department_id,
      </if>
      <if test="userDeviceNum != null">
        user_device_num,
      </if>
      <if test="deviceSwitchStatus != null">
        device_switch_status,
      </if>
      <if test="validStatus != null">
        valid_status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="remark != null">
        remark,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="name != null">
        #{name},
      </if>
      <if test="description != null">
        #{description},
      </if>
      <if test="parentDepartmentId != null">
        #{parentDepartmentId},
      </if>
      <if test="source != null">
        #{source},
      </if>
      <if test="fullDepartmentId != null">
        #{fullDepartmentId},
      </if>
      <if test="userDeviceNum != null">
        #{userDeviceNum},
      </if>
      <if test="deviceSwitchStatus != null">
        #{deviceSwitchStatus},
      </if>
      <if test="validStatus != null">
        #{validStatus},
      </if>
      <if test="createTime != null">
        #{createTime},
      </if>
      <if test="updateTime != null">
        #{updateTime},
      </if>
      <if test="remark != null">
        #{remark},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.pttl.mobile.manager.domain.entity.Department2DO">

    update mm_department
    <set>
      <if test="name != null">
        `name` = #{name},
      </if>
      <if test="description != null">
        description = #{description},
      </if>
      <if test="parentDepartmentId != null">
        parent_department_id = #{parentDepartmentId},
      </if>
      <if test="source != null">
        `source` = #{source},
      </if>
      <if test="fullDepartmentId != null">
        full_department_id = #{fullDepartmentId},
      </if>
      <if test="userDeviceNum != null">
        user_device_num = #{userDeviceNum},
      </if>
      <if test="deviceSwitchStatus != null">
        device_switch_status = #{deviceSwitchStatus},
      </if>
      <if test="validStatus != null">
        valid_status = #{validStatus},
      </if>
      <if test="createTime != null">
        create_time = #{createTime},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime},
      </if>
      <if test="remark != null">
        remark = #{remark},
      </if>
    </set>
    where id = #{id}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.pttl.mobile.manager.domain.entity.Department2DO">

    update mm_department
    set `name` = #{name},
      description = #{description},
      parent_department_id = #{parentDepartmentId},
      `source` = #{source},
      full_department_id = #{fullDepartmentId},
      user_device_num = #{userDeviceNum},
      device_switch_status = #{deviceSwitchStatus},
      valid_status = #{validStatus},
      create_time = #{createTime},
      update_time = #{updateTime},
      remark = #{remark}
    where id = #{id}
  </update>
  <update id="updateBatch" parameterType="java.util.List">

    update mm_department
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="`name` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.name}
        </foreach>
      </trim>
      <trim prefix="description = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.description}
        </foreach>
      </trim>
      <trim prefix="parent_department_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.parentDepartmentId}
        </foreach>
      </trim>
      <trim prefix="`source` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.source}
        </foreach>
      </trim>
      <trim prefix="full_department_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.fullDepartmentId}
        </foreach>
      </trim>
      <trim prefix="user_device_num = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.userDeviceNum}
        </foreach>
      </trim>
      <trim prefix="device_switch_status = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.deviceSwitchStatus}
        </foreach>
      </trim>
      <trim prefix="valid_status = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.validStatus}
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.createTime}
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.updateTime}
        </foreach>
      </trim>
      <trim prefix="remark = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.remark}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">

    update mm_department
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="`name` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.name != null">
            when id = #{item.id} then #{item.name}
          </if>
        </foreach>
      </trim>
      <trim prefix="description = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.description != null">
            when id = #{item.id} then #{item.description}
          </if>
        </foreach>
      </trim>
      <trim prefix="parent_department_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.parentDepartmentId != null">
            when id = #{item.id} then #{item.parentDepartmentId}
          </if>
        </foreach>
      </trim>
      <trim prefix="`source` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.source != null">
            when id = #{item.id} then #{item.source}
          </if>
        </foreach>
      </trim>
      <trim prefix="full_department_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.fullDepartmentId != null">
            when id = #{item.id} then #{item.fullDepartmentId}
          </if>
        </foreach>
      </trim>
      <trim prefix="user_device_num = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.userDeviceNum != null">
            when id = #{item.id} then #{item.userDeviceNum}
          </if>
        </foreach>
      </trim>
      <trim prefix="device_switch_status = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.deviceSwitchStatus != null">
            when id = #{item.id} then #{item.deviceSwitchStatus}
          </if>
        </foreach>
      </trim>
      <trim prefix="valid_status = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.validStatus != null">
            when id = #{item.id} then #{item.validStatus}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createTime != null">
            when id = #{item.id} then #{item.createTime}
          </if>
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.updateTime != null">
            when id = #{item.id} then #{item.updateTime}
          </if>
        </foreach>
      </trim>
      <trim prefix="remark = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.remark != null">
            when id = #{item.id} then #{item.remark}
          </if>
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">

    insert into mm_department
    (`name`, description, parent_department_id, `source`, full_department_id, user_device_num, 
      device_switch_status, valid_status, create_time, update_time, remark)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.name}, #{item.description}, #{item.parentDepartmentId}, #{item.source}, #{item.fullDepartmentId}, 
        #{item.userDeviceNum}, #{item.deviceSwitchStatus}, #{item.validStatus}, #{item.createTime}, 
        #{item.updateTime}, #{item.remark})
    </foreach>
  </insert>

    <select id="getOneByNameAndParentDepartmentId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mm_department
        where name = #{name}
          AND parent_department_id = #{parentDepartmentId}
    </select>

    <select id="countAll" resultType="int">
        select count(id)
        from mm_department
    </select>


    <select id="listSameLevelDepartment" resultType="com.pttl.mobile.manager.domain.dto.SameLevelDepartmentDTO">
        SELECT
            id, name
        FROM
            mm_department
        WHERE
            parent_department_id = ( SELECT parent_department_id FROM mm_department WHERE id = #{departmentId} )
    </select>

</mapper>