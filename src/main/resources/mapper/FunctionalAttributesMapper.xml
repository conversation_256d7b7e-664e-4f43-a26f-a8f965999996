<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pttl.mobile.manager.dao.FunctionalAttributesMapper">
    <resultMap id="BaseResultMap" type="com.pttl.mobile.manager.domain.entity.FunctionalAttributesDO">
        <!--@Table mm_functional_attributes-->
        <id column="id" property="id"/>
        <result column="env" property="env"/>
        <result column="data_type" property="dataType"/>
        <result column="functional_attributes" property="functionalAttributes"/>
        <result column="attribute_value" property="attributeValue"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="remark" property="remark"/>
    </resultMap>
    <sql id="Base_Column_List">
        id,
        operating_system,
        env,
        data_type,
        functional_attributes,
        attribute_value,
        create_time,
        update_time,
        remark
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mm_functional_attributes
        where id = #{id}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from mm_functional_attributes
        where id = #{id}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="com.pttl.mobile.manager.domain.entity.FunctionalAttributesDO" useGeneratedKeys="true">
        insert into mm_functional_attributes (operating_system, env, data_type, functional_attributes, attribute_value,
                                              create_time,
                                              update_time, remark)
        values (#{operatingSystem}, #{env}, #{dataType}, #{functionalAttributes}, #{attributeValue}, #{createTime},
                #{updateTime}, #{remark})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.pttl.mobile.manager.domain.entity.FunctionalAttributesDO" useGeneratedKeys="true">
        insert into mm_functional_attributes
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="env != null">
                env,
            </if>
            <if test="dataType != null">
                data_type,
            </if>
            <if test="functionalAttributes != null">
                functional_attributes,
            </if>
            <if test="attributeValue != null">
                attribute_value,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="remark != null">
                remark,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="env != null">
                #{env},
            </if>
            <if test="dataType != null">
                #{dataType},
            </if>
            <if test="functionalAttributes != null">
                #{functionalAttributes},
            </if>
            <if test="attributeValue != null">
                #{attributeValue},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="updateTime != null">
                #{updateTime},
            </if>
            <if test="remark != null">
                #{remark},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.pttl.mobile.manager.domain.entity.FunctionalAttributesDO">
        update mm_functional_attributes
        <set>
            <if test="env != null">
                env = #{env},
            </if>
            <if test="dataType != null">
                data_type = #{dataType},
            </if>
            <if test="functionalAttributes != null">
                functional_attributes = #{functionalAttributes},
            </if>
            <if test="attributeValue != null">
                attribute_value = #{attributeValue},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
        </set>
        where id = #{id}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.pttl.mobile.manager.domain.entity.FunctionalAttributesDO">
        update mm_functional_attributes
        set env                   = #{env},
            data_type             = #{dataType},
            operating_system      = #{operatingSystem},
            functional_attributes = #{functionalAttributes},
            attribute_value       = #{attributeValue},
            create_time           = #{createTime},
            update_time           = #{updateTime},
            remark                = #{remark}
        where id = #{id}
    </update>
    <select id="selectAllOrderByIdDesc" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mm_functional_attributes
        order by id desc
    </select>
    <update id="updateBatch" parameterType="java.util.List">
        update mm_functional_attributes
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="env = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id} then #{item.env}
                </foreach>
            </trim>
            <trim prefix="data_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id} then #{item.dataType}
                </foreach>
            </trim>
            <trim prefix="functional_attributes = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id} then #{item.functionalAttributes}
                </foreach>
            </trim>
            <trim prefix="attribute_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id} then #{item.attributeValue}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id} then #{item.createTime}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id} then #{item.updateTime}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id} then #{item.remark}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id}
        </foreach>
    </update>
    <update id="updateBatchSelective" parameterType="java.util.List">
        update mm_functional_attributes
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="env = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.env != null">
                        when id = #{item.id} then #{item.env}
                    </if>
                </foreach>
            </trim>
            <trim prefix="data_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.dataType != null">
                        when id = #{item.id} then #{item.dataType}
                    </if>
                </foreach>
            </trim>
            <trim prefix="functional_attributes = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.functionalAttributes != null">
                        when id = #{item.id} then #{item.functionalAttributes}
                    </if>
                </foreach>
            </trim>
            <trim prefix="attribute_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.attributeValue != null">
                        when id = #{item.id} then #{item.attributeValue}
                    </if>
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.createTime != null">
                        when id = #{item.id} then #{item.createTime}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.updateTime != null">
                        when id = #{item.id} then #{item.updateTime}
                    </if>
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    <if test="item.remark != null">
                        when id = #{item.id} then #{item.remark}
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id}
        </foreach>
    </update>
    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        insert into mm_functional_attributes
                (env, data_type, functional_attributes, attribute_value, create_time, update_time,
                 remark)
                values
        <foreach collection="list" item="item" separator=",">
            (#{item.env}, #{item.dataType}, #{item.functionalAttributes}, #{item.attributeValue},
             #{item.createTime}, #{item.updateTime}, #{item.remark})
        </foreach>
    </insert>

    <select id="listByFunctionalAttributes" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mm_functional_attributes
        <where>
            <if test="functionalAttributes != null and functionalAttributes != ''">
                and functional_attributes like concat('%', #{functionalAttributes}, '%')
            </if>
            <if test="dataType != null">
                and data_type = #{dataType}
            </if>
            <if test="operatingSystem != null">
                and operating_system = #{operatingSystem}
            </if>
        </where>
        order by update_time desc
    </select>

    <select id="listByFunctionalAttributesForMobile" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mm_functional_attributes
        <where>
            <if test="functionalAttributes != null and functionalAttributes != ''">
                and functional_attributes like concat('%', #{functionalAttributes}, '%')
            </if>
            <if test="dataType != null">
                and data_type = #{dataType}
            </if>
            <if test="operatingSystem != null">
                and operating_system IN (#{operatingSystem}, 3)
            </if>
        </where>
        order by update_time desc
    </select>

    <select id="getByFunctionalAttributes" resultType="com.pttl.mobile.manager.domain.entity.FunctionalAttributesDO"
            parameterType="java.lang.String">
            select
        <include refid="Base_Column_List"/>
        from mm_functional_attributes
        where functional_attributes = #{functionalAttributes}
    </select>
</mapper>