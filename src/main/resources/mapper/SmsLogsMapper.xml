<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pttl.mobile.manager.dao.SmsLogsMapper">
  <resultMap id="BaseResultMap" type="com.pttl.mobile.manager.domain.entity.SmsLogs">
    <!--@mbg.generated-->
    <!--@Table mm_sms_logs-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="sms_type" jdbcType="VARCHAR" property="smsType" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="phone_num" jdbcType="VARCHAR" property="phoneNum" />
    <result column="model_name" jdbcType="VARCHAR" property="modelName" />
    <result column="model_content" jdbcType="LONGVARCHAR" property="modelContent" />
    <result column="sms_condition" jdbcType="VARCHAR" property="smsCondition" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="reserved_field1" jdbcType="VARCHAR" property="reservedField1" />
    <result column="reserved_field2" jdbcType="VARCHAR" property="reservedField2" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, sms_type, `name`, phone_num, model_name, model_content, sms_condition, create_time, 
    reserved_field1, reserved_field2
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from mm_sms_logs
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from mm_sms_logs
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.pttl.mobile.manager.domain.entity.SmsLogs" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into mm_sms_logs (sms_type, `name`, phone_num, 
      model_name, model_content, sms_condition, 
      create_time, reserved_field1, reserved_field2
      )
    values (#{smsType,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{phoneNum,jdbcType=VARCHAR}, 
      #{modelName,jdbcType=VARCHAR}, #{modelContent,jdbcType=LONGVARCHAR}, #{smsCondition,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{reservedField1,jdbcType=VARCHAR}, #{reservedField2,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.pttl.mobile.manager.domain.entity.SmsLogs" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into mm_sms_logs
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="smsType != null and smsType != ''">
        sms_type,
      </if>
      <if test="name != null and name != ''">
        `name`,
      </if>
      <if test="phoneNum != null and phoneNum != ''">
        phone_num,
      </if>
      <if test="modelName != null and modelName != ''">
        model_name,
      </if>
      <if test="modelContent != null and modelContent != ''">
        model_content,
      </if>
      <if test="smsCondition != null and smsCondition != ''">
        sms_condition,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="reservedField1 != null and reservedField1 != ''">
        reserved_field1,
      </if>
      <if test="reservedField2 != null and reservedField2 != ''">
        reserved_field2,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="smsType != null and smsType != ''">
        #{smsType,jdbcType=VARCHAR},
      </if>
      <if test="name != null and name != ''">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="phoneNum != null and phoneNum != ''">
        #{phoneNum,jdbcType=VARCHAR},
      </if>
      <if test="modelName != null and modelName != ''">
        #{modelName,jdbcType=VARCHAR},
      </if>
      <if test="modelContent != null and modelContent != ''">
        #{modelContent,jdbcType=LONGVARCHAR},
      </if>
      <if test="smsCondition != null and smsCondition != ''">
        #{smsCondition,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reservedField1 != null and reservedField1 != ''">
        #{reservedField1,jdbcType=VARCHAR},
      </if>
      <if test="reservedField2 != null and reservedField2 != ''">
        #{reservedField2,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.pttl.mobile.manager.domain.entity.SmsLogs">
    <!--@mbg.generated-->
    update mm_sms_logs
    <set>
      <if test="smsType != null and smsType != ''">
        sms_type = #{smsType,jdbcType=VARCHAR},
      </if>
      <if test="name != null and name != ''">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="phoneNum != null and phoneNum != ''">
        phone_num = #{phoneNum,jdbcType=VARCHAR},
      </if>
      <if test="modelName != null and modelName != ''">
        model_name = #{modelName,jdbcType=VARCHAR},
      </if>
      <if test="modelContent != null and modelContent != ''">
        model_content = #{modelContent,jdbcType=LONGVARCHAR},
      </if>
      <if test="smsCondition != null and smsCondition != ''">
        sms_condition = #{smsCondition,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="reservedField1 != null and reservedField1 != ''">
        reserved_field1 = #{reservedField1,jdbcType=VARCHAR},
      </if>
      <if test="reservedField2 != null and reservedField2 != ''">
        reserved_field2 = #{reservedField2,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.pttl.mobile.manager.domain.entity.SmsLogs">
    <!--@mbg.generated-->
    update mm_sms_logs
    set sms_type = #{smsType,jdbcType=VARCHAR},
      `name` = #{name,jdbcType=VARCHAR},
      phone_num = #{phoneNum,jdbcType=VARCHAR},
      model_name = #{modelName,jdbcType=VARCHAR},
      model_content = #{modelContent,jdbcType=LONGVARCHAR},
      sms_condition = #{smsCondition,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      reserved_field1 = #{reservedField1,jdbcType=VARCHAR},
      reserved_field2 = #{reservedField2,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    <!--@mbg.generated-->
    update mm_sms_logs
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="sms_type = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.smsType,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`name` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.name,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="phone_num = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.phoneNum,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="model_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.modelName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="model_content = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.modelContent,jdbcType=LONGVARCHAR}
        </foreach>
      </trim>
      <trim prefix="sms_condition = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.smsCondition,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="reserved_field1 = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.reservedField1,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="reserved_field2 = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.reservedField2,jdbcType=VARCHAR}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    <!--@mbg.generated-->
    update mm_sms_logs
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="sms_type = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.smsType != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.smsType,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="`name` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.name != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.name,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="phone_num = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.phoneNum != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.phoneNum,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="model_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modelName != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.modelName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="model_content = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modelContent != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.modelContent,jdbcType=LONGVARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="sms_condition = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.smsCondition != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.smsCondition,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.createTime != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
          </if>
        </foreach>
      </trim>
      <trim prefix="reserved_field1 = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.reservedField1 != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.reservedField1,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="reserved_field2 = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.reservedField2 != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.reservedField2,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into mm_sms_logs
    (sms_type, `name`, phone_num, model_name, model_content, sms_condition, create_time, 
      reserved_field1, reserved_field2)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.smsType,jdbcType=VARCHAR}, #{item.name,jdbcType=VARCHAR}, #{item.phoneNum,jdbcType=VARCHAR}, 
        #{item.modelName,jdbcType=VARCHAR}, #{item.modelContent,jdbcType=LONGVARCHAR}, 
        #{item.smsCondition,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP}, #{item.reservedField1,jdbcType=VARCHAR}, 
        #{item.reservedField2,jdbcType=VARCHAR})
    </foreach>
  </insert>
</mapper>