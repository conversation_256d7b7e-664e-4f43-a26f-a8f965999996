<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pttl.mobile.manager.dao.FrontLogsMapper">
  <resultMap id="BaseResultMap" type="com.pttl.mobile.manager.domain.entity.FrontLogs">
    
    <!--@Table mm_front_logs-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="logId" jdbcType="BIGINT" property="logId" />
    <result column="server_name" jdbcType="VARCHAR" property="serverName" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="log_date" jdbcType="BIGINT" property="logDate" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="app_version" jdbcType="VARCHAR" property="appVersion" />
    <result column="device_info" jdbcType="VARCHAR" property="deviceInfo" />
    <result column="ihr_name" jdbcType="VARCHAR" property="ihrName" />
    <result column="log_start_time" jdbcType="BIGINT" property="logStartTime" />
    <result column="system_type" jdbcType="VARCHAR" property="systemType" />
    <result column="system_version" jdbcType="VARCHAR" property="systemVersion" />
    <result column="nwe_page_load" jdbcType="LONGVARCHAR" property="nwePageLoad" />
    <result column="log_detail" jdbcType="LONGVARCHAR" property="logDetail" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
  </resultMap>
  <sql id="Base_Column_List">
    
    id, logId, `server_name`, user_name, log_date, `status`, app_version, device_info, 
    ihr_name, log_start_time, system_type, system_version, nwe_page_load, log_detail, 
    remark
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    
    select 
    <include refid="Base_Column_List" />
    from mm_front_logs
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    
    delete from mm_front_logs
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.pttl.mobile.manager.domain.entity.FrontLogs" useGeneratedKeys="true">
    insert into mm_front_logs (logId, `server_name`, user_name,
    log_date, `status`, app_version,
    device_info, ihr_name, log_start_time,
    system_type, system_version, nwe_page_load,
    log_detail, remark)
    values (#{logId,jdbcType=BIGINT}, #{serverName,jdbcType=VARCHAR}, #{userName,jdbcType=VARCHAR},
    #{logDate,jdbcType=BIGINT}, #{status,jdbcType=VARCHAR}, #{appVersion,jdbcType=VARCHAR},
    #{deviceInfo,jdbcType=VARCHAR}, #{ihrName,jdbcType=VARCHAR}, #{logStartTime,jdbcType=BIGINT},
    #{systemType,jdbcType=VARCHAR}, #{systemVersion,jdbcType=VARCHAR}, #{nwePageLoad,jdbcType=LONGVARCHAR},
    #{logDetail,jdbcType=LONGVARCHAR}, #{remark,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.pttl.mobile.manager.domain.entity.FrontLogs" useGeneratedKeys="true">
    
    insert into mm_front_logs
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="logId != null">
        logId,
      </if>
      <if test="serverName != null and serverName != ''">
        `server_name`,
      </if>
      <if test="userName != null and userName != ''">
        user_name,
      </if>
      <if test="logDate != null">
        log_date,
      </if>
      <if test="status != null and status != ''">
        `status`,
      </if>
      <if test="appVersion != null and appVersion != ''">
        app_version,
      </if>
      <if test="deviceInfo != null and deviceInfo != ''">
        device_info,
      </if>
      <if test="ihrName != null and ihrName != ''">
        ihr_name,
      </if>
      <if test="logStartTime != null">
        log_start_time,
      </if>
      <if test="systemType != null and systemType != ''">
        system_type,
      </if>
      <if test="systemVersion != null and systemVersion != ''">
        system_version,
      </if>
      <if test="nwePageLoad != null and nwePageLoad != ''">
        nwe_page_load,
      </if>
      <if test="logDetail != null and logDetail != ''">
        log_detail,
      </if>
      <if test="remark != null and remark != ''">
        remark,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="logId != null">
        #{logId,jdbcType=BIGINT},
      </if>
      <if test="serverName != null and serverName != ''">
        #{serverName,jdbcType=VARCHAR},
      </if>
      <if test="userName != null and userName != ''">
        #{userName,jdbcType=VARCHAR},
      </if>
      <if test="logDate != null">
        #{logDate,jdbcType=BIGINT},
      </if>
      <if test="status != null and status != ''">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="appVersion != null and appVersion != ''">
        #{appVersion,jdbcType=VARCHAR},
      </if>
      <if test="deviceInfo != null and deviceInfo != ''">
        #{deviceInfo,jdbcType=VARCHAR},
      </if>
      <if test="ihrName != null and ihrName != ''">
        #{ihrName,jdbcType=VARCHAR},
      </if>
      <if test="logStartTime != null">
        #{logStartTime,jdbcType=BIGINT},
      </if>
      <if test="systemType != null and systemType != ''">
        #{systemType,jdbcType=VARCHAR},
      </if>
      <if test="systemVersion != null and systemVersion != ''">
        #{systemVersion,jdbcType=VARCHAR},
      </if>
      <if test="nwePageLoad != null and nwePageLoad != ''">
        #{nwePageLoad,jdbcType=LONGVARCHAR},
      </if>
      <if test="logDetail != null and logDetail != ''">
        #{logDetail,jdbcType=LONGVARCHAR},
      </if>
      <if test="remark != null and remark != ''">
        #{remark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.pttl.mobile.manager.domain.entity.FrontLogs">
    
    update mm_front_logs
    <set>
      <if test="logId != null">
        logId = #{logId,jdbcType=BIGINT},
      </if>
      <if test="serverName != null and serverName != ''">
        `server_name` = #{serverName,jdbcType=VARCHAR},
      </if>
      <if test="userName != null and userName != ''">
        user_name = #{userName,jdbcType=VARCHAR},
      </if>
      <if test="logDate != null">
        log_date = #{logDate,jdbcType=BIGINT},
      </if>
      <if test="status != null and status != ''">
        `status` = #{status,jdbcType=VARCHAR},
      </if>
      <if test="appVersion != null and appVersion != ''">
        app_version = #{appVersion,jdbcType=VARCHAR},
      </if>
      <if test="deviceInfo != null and deviceInfo != ''">
        device_info = #{deviceInfo,jdbcType=VARCHAR},
      </if>
      <if test="ihrName != null and ihrName != ''">
        ihr_name = #{ihrName,jdbcType=VARCHAR},
      </if>
      <if test="logStartTime != null">
        log_start_time = #{logStartTime,jdbcType=BIGINT},
      </if>
      <if test="systemType != null and systemType != ''">
        system_type = #{systemType,jdbcType=VARCHAR},
      </if>
      <if test="systemVersion != null and systemVersion != ''">
        system_version = #{systemVersion,jdbcType=VARCHAR},
      </if>
      <if test="nwePageLoad != null and nwePageLoad != ''">
        nwe_page_load = #{nwePageLoad,jdbcType=LONGVARCHAR},
      </if>
      <if test="logDetail != null and logDetail != ''">
        log_detail = #{logDetail,jdbcType=LONGVARCHAR},
      </if>
      <if test="remark != null and remark != ''">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.pttl.mobile.manager.domain.entity.FrontLogs">
    update mm_front_logs
    set logId = #{logId,jdbcType=BIGINT},
    `server_name` = #{serverName,jdbcType=VARCHAR},
    user_name = #{userName,jdbcType=VARCHAR},
    log_date = #{logDate,jdbcType=BIGINT},
    `status` = #{status,jdbcType=VARCHAR},
    app_version = #{appVersion,jdbcType=VARCHAR},
    device_info = #{deviceInfo,jdbcType=VARCHAR},
    ihr_name = #{ihrName,jdbcType=VARCHAR},
    log_start_time = #{logStartTime,jdbcType=BIGINT},
    system_type = #{systemType,jdbcType=VARCHAR},
    system_version = #{systemVersion,jdbcType=VARCHAR},
    nwe_page_load = #{nwePageLoad,jdbcType=LONGVARCHAR},
    log_detail = #{logDetail,jdbcType=LONGVARCHAR},
    remark = #{remark,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    
    update mm_front_logs
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="logId = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.logId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="`server_name` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.serverName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="user_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.userName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="log_date = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.logDate,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="`status` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.status,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="app_version = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.appVersion,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="device_info = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.deviceInfo,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="ihr_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.ihrName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="log_start_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.logStartTime,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="system_type = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.systemType,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="system_version = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.systemVersion,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="nwe_page_load = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.nwePageLoad,jdbcType=LONGVARCHAR}
        </foreach>
      </trim>
      <trim prefix="log_detail = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.logDetail,jdbcType=LONGVARCHAR}
        </foreach>
      </trim>
      <trim prefix="remark = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.remark,jdbcType=VARCHAR}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    
    update mm_front_logs
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="logId = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.logId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.logId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="`server_name` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.serverName != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.serverName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="user_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.userName != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.userName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="log_date = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.logDate != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.logDate,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="`status` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.status != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.status,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="app_version = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.appVersion != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.appVersion,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="device_info = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.deviceInfo != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.deviceInfo,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="ihr_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.ihrName != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.ihrName,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="log_start_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.logStartTime != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.logStartTime,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="system_type = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.systemType != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.systemType,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="system_version = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.systemVersion != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.systemVersion,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="nwe_page_load = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.nwePageLoad != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.nwePageLoad,jdbcType=LONGVARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="log_detail = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.logDetail != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.logDetail,jdbcType=LONGVARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="remark = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.remark != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.remark,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    
    insert into mm_front_logs
    (logId, `server_name`, user_name, log_date, `status`, app_version, device_info, ihr_name, 
      log_start_time, system_type, system_version, nwe_page_load, log_detail, remark)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.logId,jdbcType=BIGINT}, #{item.serverName,jdbcType=VARCHAR}, #{item.userName,jdbcType=VARCHAR},
      #{item.logDate,jdbcType=BIGINT}, #{item.status,jdbcType=VARCHAR}, #{item.appVersion,jdbcType=VARCHAR},
      #{item.deviceInfo,jdbcType=VARCHAR}, #{item.ihrName,jdbcType=VARCHAR}, #{item.logStartTime,jdbcType=BIGINT},
      #{item.systemType,jdbcType=VARCHAR}, #{item.systemVersion,jdbcType=VARCHAR},
      #{item.nwePageLoad,jdbcType=LONGVARCHAR},
      #{item.logDetail,jdbcType=LONGVARCHAR}, #{item.remark,jdbcType=VARCHAR})
    </foreach>
  </insert>

  <select id="pageInfo" resultType="com.pttl.mobile.manager.domain.dto.FrontLogsResultDTO">
    select id, log_date, `status`, server_name,ihr_name, user_name
    from mm_front_logs
    <where>
      <if test="serverName != null and serverName != ''">
        and server_name = #{serverName,jdbcType=VARCHAR}
      </if>
      <if test="ihrName != null and ihrName != ''">
        and ihr_name  like concat('%', #{ihrName,jdbcType=VARCHAR}, '%')
      </if>
      <if test="userName != null and userName != ''">
        and user_name  like concat('%', #{userName,jdbcType=VARCHAR}, '%')
      </if>
      <if test="endLoginTimeLong != null">
        and log_date &lt;= #{endLoginTimeLong,jdbcType=BIGINT}
      </if>
      <if test="startLoginTimeLong != null">
        and log_date &gt;= #{startLoginTimeLong}
      </if>
      <if test="status != null and status != ''">
        and status = #{status,jdbcType=VARCHAR}
      </if>
    </where>
  </select>

  <select id="exportPageInfo" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from mm_front_logs
    <where>
      <if test="serverName != null and serverName != ''">
        and server_name = #{serverName,jdbcType=VARCHAR}
      </if>
      <if test="ihrName != null and ihrName != ''">
        and ihr_name  like concat('%', #{ihrName,jdbcType=VARCHAR}, '%')
      </if>
      <if test="userName != null and userName != ''">
        and user_name  like concat('%', #{userName,jdbcType=VARCHAR}, '%')
      </if>
      <if test="endLoginTimeLong != null">
        and log_date &lt;= #{endLoginTimeLong,jdbcType=BIGINT}
      </if>
      <if test="startLoginTimeLong != null">
        and log_date &gt;= #{startLoginTimeLong}
      </if>
      <if test="status != null and status != ''">
        and status = #{status,jdbcType=VARCHAR}
      </if>
    </where>
  </select>
</mapper>