<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pttl.mobile.manager.dao.SmsRelationMapper">
  <resultMap id="BaseResultMap" type="com.pttl.mobile.manager.domain.entity.SmsRelationDO">
    
    <!--@Table mm_sms_relation-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="personnel_id" jdbcType="BIGINT" property="personnelId" />
    <result column="model_id" jdbcType="BIGINT" property="modelId" />
    <result column="sms_type" jdbcType="VARCHAR" property="smsType" />
    <result column="sms_condition" jdbcType="VARCHAR" property="smsCondition" />
  </resultMap>
  <sql id="Base_Column_List">
    
    id, personnel_id, model_id, sms_type, sms_condition
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    
    select 
    <include refid="Base_Column_List" />
    from mm_sms_relation
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    
    delete from mm_sms_relation
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.pttl.mobile.manager.domain.entity.SmsRelationDO" useGeneratedKeys="true">
    
    insert into mm_sms_relation (personnel_id, model_id, sms_type, 
      sms_condition)
    values (#{personnelId,jdbcType=BIGINT}, #{modelId,jdbcType=BIGINT}, #{smsType,jdbcType=VARCHAR}, 
      #{smsCondition,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.pttl.mobile.manager.domain.entity.SmsRelationDO" useGeneratedKeys="true">
    
    insert into mm_sms_relation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="personnelId != null">
        personnel_id,
      </if>
      <if test="modelId != null">
        model_id,
      </if>
      <if test="smsType != null and smsType != ''">
        sms_type,
      </if>
      <if test="smsCondition != null and smsCondition != ''">
        sms_condition,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="personnelId != null">
        #{personnelId,jdbcType=BIGINT},
      </if>
      <if test="modelId != null">
        #{modelId,jdbcType=BIGINT},
      </if>
      <if test="smsType != null and smsType != ''">
        #{smsType,jdbcType=VARCHAR},
      </if>
      <if test="smsCondition != null and smsCondition != ''">
        #{smsCondition,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.pttl.mobile.manager.domain.entity.SmsRelationDO">
    
    update mm_sms_relation
    <set>
      <if test="personnelId != null">
        personnel_id = #{personnelId,jdbcType=BIGINT},
      </if>
      <if test="modelId != null">
        model_id = #{modelId,jdbcType=BIGINT},
      </if>
      <if test="smsType != null and smsType != ''">
        sms_type = #{smsType,jdbcType=VARCHAR},
      </if>
      <if test="smsCondition != null and smsCondition != ''">
        sms_condition = #{smsCondition,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.pttl.mobile.manager.domain.entity.SmsRelationDO">
    
    update mm_sms_relation
    set personnel_id = #{personnelId,jdbcType=BIGINT},
      model_id = #{modelId,jdbcType=BIGINT},
      sms_type = #{smsType,jdbcType=VARCHAR},
      sms_condition = #{smsCondition,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    
    update mm_sms_relation
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="personnel_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.personnelId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="model_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.modelId,jdbcType=BIGINT}
        </foreach>
      </trim>
      <trim prefix="sms_type = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.smsType,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="sms_condition = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.smsCondition,jdbcType=VARCHAR}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    
    update mm_sms_relation
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="personnel_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.personnelId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.personnelId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="model_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.modelId != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.modelId,jdbcType=BIGINT}
          </if>
        </foreach>
      </trim>
      <trim prefix="sms_type = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.smsType != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.smsType,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="sms_condition = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.smsCondition != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.smsCondition,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    
    insert into mm_sms_relation
    (personnel_id, model_id, sms_type, sms_condition)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.personnelId,jdbcType=BIGINT}, #{item.modelId,jdbcType=BIGINT}, #{item.smsType,jdbcType=VARCHAR}, 
        #{item.smsCondition,jdbcType=VARCHAR})
    </foreach>
  </insert>

  <delete id="deleteByPersonId">
    delete from mm_sms_relation where personnel_id = #{id,jdbcType=BIGINT}
  </delete>
</mapper>