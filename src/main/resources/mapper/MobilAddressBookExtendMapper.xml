<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pttl.mobile.manager.mobile.ws.dao.MobilAddressBookExtendMapper">
    <resultMap id="BaseResultMap" type="com.pttl.mobile.manager.mobile.ws.entity.MobilAddressBookExtendDO">
        <!--@Table mm_mobil_address_book_extend-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="person_info_id" jdbcType="VARCHAR" property="personInfoId"/>
        <result column="user_remark" jdbcType="VARCHAR" property="userRemark"/>
        <result column="custom_avatar" jdbcType="VARCHAR" property="customAvatar"/>
        <result column="reserved_field1" jdbcType="VARCHAR" property="reservedField1"/>
        <result column="reserved_field2" jdbcType="VARCHAR" property="reservedField2"/>
        <result column="reserved_field3" jdbcType="VARCHAR" property="reservedField3"/>
    </resultMap>
    <sql id="Base_Column_List">
        id,
        person_info_id,
        user_remark,
        custom_avatar,
        reserved_field1,
        reserved_field2,
        reserved_field3
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mm_mobil_address_book_extend
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from mm_mobil_address_book_extend
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="com.pttl.mobile.manager.mobile.ws.entity.MobilAddressBookExtendDO" useGeneratedKeys="true">
        insert into mm_mobil_address_book_extend (person_info_id, user_remark, custom_avatar,
                                                  reserved_field1, reserved_field2, reserved_field3)
        values (#{personInfoId,jdbcType=VARCHAR}, #{userRemark,jdbcType=VARCHAR}, #{customAvatar,jdbcType=VARCHAR},
                #{reservedField1,jdbcType=VARCHAR}, #{reservedField2,jdbcType=VARCHAR},
                #{reservedField3,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="com.pttl.mobile.manager.mobile.ws.entity.MobilAddressBookExtendDO" useGeneratedKeys="true">
        insert into mm_mobil_address_book_extend
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="personInfoId != null">
                person_info_id,
            </if>
            <if test="userRemark != null">
                user_remark,
            </if>
            <if test="customAvatar != null">
                custom_avatar,
            </if>
            <if test="reservedField1 != null">
                reserved_field1,
            </if>
            <if test="reservedField2 != null">
                reserved_field2,
            </if>
            <if test="reservedField3 != null">
                reserved_field3,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="personInfoId != null">
                #{personInfoId,jdbcType=VARCHAR},
            </if>
            <if test="userRemark != null">
                #{userRemark,jdbcType=VARCHAR},
            </if>
            <if test="customAvatar != null">
                #{customAvatar,jdbcType=VARCHAR},
            </if>
            <if test="reservedField1 != null">
                #{reservedField1,jdbcType=VARCHAR},
            </if>
            <if test="reservedField2 != null">
                #{reservedField2,jdbcType=VARCHAR},
            </if>
            <if test="reservedField3 != null">
                #{reservedField3,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective"
            parameterType="com.pttl.mobile.manager.mobile.ws.entity.MobilAddressBookExtendDO">
        update mm_mobil_address_book_extend
        <set>
            <if test="personInfoId != null">
                person_info_id = #{personInfoId,jdbcType=VARCHAR},
            </if>
            <if test="userRemark != null">
                user_remark = #{userRemark,jdbcType=VARCHAR},
            </if>
            <if test="customAvatar != null">
                custom_avatar = #{customAvatar,jdbcType=VARCHAR},
            </if>
            <if test="reservedField1 != null">
                reserved_field1 = #{reservedField1,jdbcType=VARCHAR},
            </if>
            <if test="reservedField2 != null">
                reserved_field2 = #{reservedField2,jdbcType=VARCHAR},
            </if>
            <if test="reservedField3 != null">
                reserved_field3 = #{reservedField3,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.pttl.mobile.manager.mobile.ws.entity.MobilAddressBookExtendDO">
        update mm_mobil_address_book_extend
        set person_info_id  = #{personInfoId,jdbcType=VARCHAR},
            user_remark     = #{userRemark,jdbcType=VARCHAR},
            custom_avatar   = #{customAvatar,jdbcType=VARCHAR},
            reserved_field1 = #{reservedField1,jdbcType=VARCHAR},
            reserved_field2 = #{reservedField2,jdbcType=VARCHAR},
            reserved_field3 = #{reservedField3,jdbcType=VARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateBatch" parameterType="java.util.List">
        update mm_mobil_address_book_extend
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="person_info_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.personInfoId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="user_remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.userRemark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="custom_avatar = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.customAvatar,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="reserved_field1 = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.reservedField1,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="reserved_field2 = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.reservedField2,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="reserved_field3 = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=BIGINT} then #{item.reservedField3,jdbcType=VARCHAR}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach close=")" collection="list" item="item" open="(" separator=", ">
            #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
        insert into mm_mobil_address_book_extend
                (person_info_id, user_remark, custom_avatar, reserved_field1, reserved_field2, reserved_field3)
                values
        <foreach collection="list" item="item" separator=",">
            (#{item.personInfoId,jdbcType=VARCHAR}, #{item.userRemark,jdbcType=VARCHAR},
             #{item.customAvatar,jdbcType=VARCHAR},
             #{item.reservedField1,jdbcType=VARCHAR}, #{item.reservedField2,jdbcType=VARCHAR},
             #{item.reservedField3,jdbcType=VARCHAR})
        </foreach>
    </insert>

    <select id="getByEmployeeId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mm_mobil_address_book_extend
        where person_info_id =  #{employeeId,jdbcType=VARCHAR}
    </select>
</mapper>