<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pttl.mobile.manager.dao.StrategyDepartment2Mapper">
  <resultMap id="BaseResultMap" type="com.pttl.mobile.manager.domain.entity.StrategyDepartment2DO">
    
    <!--@Table mm_strategy_department-->
    <id column="id" property="id" />
    <result column="strategy_id" property="strategyId" />
    <result column="department_id" property="departmentId" />
  </resultMap>
  <sql id="Base_Column_List">
    
    id, strategy_id, department_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    
    select 
    <include refid="Base_Column_List" />
    from mm_strategy_department
    where id = #{id}
  </select>

  <!--根据策略id获取关联部门信息列表-->
  <select id="listByStrategyId" resultType="com.pttl.mobile.manager.domain.entity.Department2DO">
        SELECT
            id,
            `name`,
            description,
            parent_department_id as parentDepartmentId,
            `source`,
            full_department_id,
            user_device_num,
            device_switch_status,
            valid_status,
            create_time,
            update_time,
            remark
        FROM
            mm_department
        WHERE
            id IN (
            SELECT
                sd.department_id
            FROM
                mm_strategy_department sd
            WHERE
            sd.strategy_id = #{strategyId}
            )
   </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    
    delete from mm_strategy_department
    where id = #{id}
  </delete>
  
  <!--根据策略id删除-->
  <delete id="deleteByStrategyId" parameterType="java.lang.Long">
    delete from mm_strategy_department
    where strategy_id = #{strategyId}
   </delete>
   
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.pttl.mobile.manager.domain.entity.StrategyDepartment2DO" useGeneratedKeys="true">
    
    insert into mm_strategy_department (strategy_id, department_id)
    values (#{strategyId}, #{departmentId})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.pttl.mobile.manager.domain.entity.StrategyDepartment2DO" useGeneratedKeys="true">
    
    insert into mm_strategy_department
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="strategyId != null">
        strategy_id,
      </if>
      <if test="departmentId != null">
        department_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="strategyId != null">
        #{strategyId},
      </if>
      <if test="departmentId != null">
        #{departmentId},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.pttl.mobile.manager.domain.entity.StrategyDepartment2DO">
    
    update mm_strategy_department
    <set>
      <if test="strategyId != null">
        strategy_id = #{strategyId},
      </if>
      <if test="departmentId != null">
        department_id = #{departmentId},
      </if>
    </set>
    where id = #{id}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.pttl.mobile.manager.domain.entity.StrategyDepartment2DO">
    
    update mm_strategy_department
    set strategy_id = #{strategyId},
      department_id = #{departmentId}
    where id = #{id}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    
    update mm_strategy_department
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="strategy_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.strategyId}
        </foreach>
      </trim>
      <trim prefix="department_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id} then #{item.departmentId}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    
    update mm_strategy_department
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="strategy_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.strategyId != null">
            when id = #{item.id} then #{item.strategyId}
          </if>
        </foreach>
      </trim>
      <trim prefix="department_id = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.departmentId != null">
            when id = #{item.id} then #{item.departmentId}
          </if>
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    
    insert into mm_strategy_department
    (strategy_id, department_id)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.strategyId}, #{item.departmentId})
    </foreach>
  </insert>
</mapper>