<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pttl.mobile.manager.dao.SystemUserMapper">
  <resultMap id="BaseResultMap" type="com.pttl.mobile.manager.domain.entity.SystemUserDO">
    
    <!--@Table mm_system_user-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="password" jdbcType="VARCHAR" property="password" />
    <result column="email" jdbcType="VARCHAR" property="email" />
    <result column="mobile" jdbcType="VARCHAR" property="mobile" />
    <result column="login_name" jdbcType="VARCHAR" property="loginName" />
    <result column="avatar_path" jdbcType="VARCHAR" property="avatarPath" />
    <result column="employee_no" jdbcType="VARCHAR" property="employeeNo" />
    <result column="person_level" jdbcType="VARCHAR" property="personLevel" />
    <result column="locality" jdbcType="VARCHAR" property="locality" />
    <result column="telephone" jdbcType="VARCHAR" property="telephone" />
    <result column="source" jdbcType="TINYINT" property="source" />
    <result column="valid_status" jdbcType="TINYINT" property="validStatus" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="extension1" jdbcType="VARCHAR" property="extension1" />
  </resultMap>
  <sql id="Base_Column_List">
    
    id, `name`, `password`, email, mobile, login_name, avatar_path, employee_no, person_level, 
    locality, telephone, `source`, valid_status, create_time, update_time, remark, extension1
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    
    select 
    <include refid="Base_Column_List" />
    from mm_system_user
    where id = #{id,jdbcType=BIGINT}
  </select>
  
  <select id="getByLoginName" resultType="com.pttl.mobile.manager.domain.entity.SystemUserDO" parameterType="java.lang.String">
        SELECT <include refid="Base_Column_List" />
        FROM mm_system_user WHERE login_name = #{loginName}
  </select>
  
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    
    delete from mm_system_user
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.pttl.mobile.manager.domain.entity.SystemUserDO" useGeneratedKeys="true">
    
    insert into mm_system_user (`name`, `password`, email, 
      mobile, login_name, avatar_path, 
      employee_no, person_level, locality, 
      telephone, `source`, valid_status, 
      create_time, update_time, remark, 
      extension1)
    values (#{name,jdbcType=VARCHAR}, #{password,jdbcType=VARCHAR}, #{email,jdbcType=VARCHAR}, 
      #{mobile,jdbcType=VARCHAR}, #{loginName,jdbcType=VARCHAR}, #{avatarPath,jdbcType=VARCHAR}, 
      #{employeeNo,jdbcType=VARCHAR}, #{personLevel,jdbcType=VARCHAR}, #{locality,jdbcType=VARCHAR}, 
      #{telephone,jdbcType=VARCHAR}, #{source,jdbcType=TINYINT}, #{validStatus,jdbcType=TINYINT}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{remark,jdbcType=VARCHAR}, 
      #{extension1,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.pttl.mobile.manager.domain.entity.SystemUserDO" useGeneratedKeys="true">
    
    insert into mm_system_user
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="name != null">
        `name`,
      </if>
      <if test="password != null">
        `password`,
      </if>
      <if test="email != null">
        email,
      </if>
      <if test="mobile != null">
        mobile,
      </if>
      <if test="loginName != null">
        login_name,
      </if>
      <if test="avatarPath != null">
        avatar_path,
      </if>
      <if test="employeeNo != null">
        employee_no,
      </if>
      <if test="personLevel != null">
        person_level,
      </if>
      <if test="locality != null">
        locality,
      </if>
      <if test="telephone != null">
        telephone,
      </if>
      <if test="source != null">
        `source`,
      </if>
      <if test="validStatus != null">
        valid_status,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="extension1 != null">
        extension1,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="password != null">
        #{password,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        #{email,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="loginName != null">
        #{loginName,jdbcType=VARCHAR},
      </if>
      <if test="avatarPath != null">
        #{avatarPath,jdbcType=VARCHAR},
      </if>
      <if test="employeeNo != null">
        #{employeeNo,jdbcType=VARCHAR},
      </if>
      <if test="personLevel != null">
        #{personLevel,jdbcType=VARCHAR},
      </if>
      <if test="locality != null">
        #{locality,jdbcType=VARCHAR},
      </if>
      <if test="telephone != null">
        #{telephone,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        #{source,jdbcType=TINYINT},
      </if>
      <if test="validStatus != null">
        #{validStatus,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="extension1 != null">
        #{extension1,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.pttl.mobile.manager.domain.entity.SystemUserDO">
    
    update mm_system_user
    <set>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="password != null">
        `password` = #{password,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        email = #{email,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        mobile = #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="loginName != null">
        login_name = #{loginName,jdbcType=VARCHAR},
      </if>
      <if test="avatarPath != null">
        avatar_path = #{avatarPath,jdbcType=VARCHAR},
      </if>
      <if test="employeeNo != null">
        employee_no = #{employeeNo,jdbcType=VARCHAR},
      </if>
      <if test="personLevel != null">
        person_level = #{personLevel,jdbcType=VARCHAR},
      </if>
      <if test="locality != null">
        locality = #{locality,jdbcType=VARCHAR},
      </if>
      <if test="telephone != null">
        telephone = #{telephone,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        `source` = #{source,jdbcType=TINYINT},
      </if>
      <if test="validStatus != null">
        valid_status = #{validStatus,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="extension1 != null">
        extension1 = #{extension1,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.pttl.mobile.manager.domain.entity.SystemUserDO">
    
    update mm_system_user
    set `name` = #{name,jdbcType=VARCHAR},
      `password` = #{password,jdbcType=VARCHAR},
      email = #{email,jdbcType=VARCHAR},
      mobile = #{mobile,jdbcType=VARCHAR},
      login_name = #{loginName,jdbcType=VARCHAR},
      avatar_path = #{avatarPath,jdbcType=VARCHAR},
      employee_no = #{employeeNo,jdbcType=VARCHAR},
      person_level = #{personLevel,jdbcType=VARCHAR},
      locality = #{locality,jdbcType=VARCHAR},
      telephone = #{telephone,jdbcType=VARCHAR},
      `source` = #{source,jdbcType=TINYINT},
      valid_status = #{validStatus,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      remark = #{remark,jdbcType=VARCHAR},
      extension1 = #{extension1,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    
    update mm_system_user
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="`name` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.name,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`password` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.password,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="email = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.email,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="mobile = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.mobile,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="login_name = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.loginName,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="avatar_path = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.avatarPath,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="employee_no = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.employeeNo,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="person_level = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.personLevel,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="locality = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.locality,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="telephone = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.telephone,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="`source` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.source,jdbcType=TINYINT}
        </foreach>
      </trim>
      <trim prefix="valid_status = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.validStatus,jdbcType=TINYINT}
        </foreach>
      </trim>
      <trim prefix="create_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.createTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="update_time = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.updateTime,jdbcType=TIMESTAMP}
        </foreach>
      </trim>
      <trim prefix="remark = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.remark,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="extension1 = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.extension1,jdbcType=VARCHAR}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    
    insert into mm_system_user
    (`name`, `password`, email, mobile, login_name, avatar_path, employee_no, person_level, 
      locality, telephone, `source`, valid_status, create_time, update_time, remark, 
      extension1)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.name,jdbcType=VARCHAR}, #{item.password,jdbcType=VARCHAR}, #{item.email,jdbcType=VARCHAR}, 
        #{item.mobile,jdbcType=VARCHAR}, #{item.loginName,jdbcType=VARCHAR}, #{item.avatarPath,jdbcType=VARCHAR}, 
        #{item.employeeNo,jdbcType=VARCHAR}, #{item.personLevel,jdbcType=VARCHAR}, #{item.locality,jdbcType=VARCHAR}, 
        #{item.telephone,jdbcType=VARCHAR}, #{item.source,jdbcType=TINYINT}, #{item.validStatus,jdbcType=TINYINT}, 
        #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}, #{item.remark,jdbcType=VARCHAR}, 
        #{item.extension1,jdbcType=VARCHAR})
    </foreach>
  </insert>
</mapper>