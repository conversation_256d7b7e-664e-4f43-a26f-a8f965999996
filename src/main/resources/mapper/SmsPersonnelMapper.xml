<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.pttl.mobile.manager.dao.SmsPersonnelMapper">
  <resultMap id="BaseResultMap" type="com.pttl.mobile.manager.domain.entity.SmsPersonnelDO">
    
    <!--@Table mm_sms_personnel-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="phone_num" jdbcType="VARCHAR" property="phoneNum" />
    <result column="reserved_field1" jdbcType="VARCHAR" property="reservedField1" />
    <result column="reserved_field2" jdbcType="VARCHAR" property="reservedField2" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
  </resultMap>
  <sql id="Base_Column_List">
    
    id, `name`, phone_num, reserved_field1, reserved_field2, remark
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    
    select 
    <include refid="Base_Column_List" />
    from mm_sms_personnel
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    
    delete from mm_sms_personnel
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.pttl.mobile.manager.domain.entity.SmsPersonnelDO" useGeneratedKeys="true">
    
    insert into mm_sms_personnel (`name`, phone_num, reserved_field1, 
      reserved_field2, remark)
    values (#{name,jdbcType=VARCHAR}, #{phoneNum,jdbcType=VARCHAR}, #{reservedField1,jdbcType=VARCHAR}, 
      #{reservedField2,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.pttl.mobile.manager.domain.entity.SmsPersonnelDO" useGeneratedKeys="true">
    
    insert into mm_sms_personnel
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="name != null and name != ''">
        `name`,
      </if>
      <if test="phoneNum != null and phoneNum != ''">
        phone_num,
      </if>
      <if test="reservedField1 != null and reservedField1 != ''">
        reserved_field1,
      </if>
      <if test="reservedField2 != null and reservedField2 != ''">
        reserved_field2,
      </if>
      <if test="remark != null and remark != ''">
        remark,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="name != null and name != ''">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="phoneNum != null and phoneNum != ''">
        #{phoneNum,jdbcType=VARCHAR},
      </if>
      <if test="reservedField1 != null and reservedField1 != ''">
        #{reservedField1,jdbcType=VARCHAR},
      </if>
      <if test="reservedField2 != null and reservedField2 != ''">
        #{reservedField2,jdbcType=VARCHAR},
      </if>
      <if test="remark != null and remark != ''">
        #{remark,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.pttl.mobile.manager.domain.entity.SmsPersonnelDO">
    
    update mm_sms_personnel
    <set>
      <if test="name != null and name != ''">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="phoneNum != null and phoneNum != ''">
        phone_num = #{phoneNum,jdbcType=VARCHAR},
      </if>
      <if test="reservedField1 != null and reservedField1 != ''">
        reserved_field1 = #{reservedField1,jdbcType=VARCHAR},
      </if>
      <if test="reservedField2 != null and reservedField2 != ''">
        reserved_field2 = #{reservedField2,jdbcType=VARCHAR},
      </if>
      <if test="remark != null and remark != ''">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.pttl.mobile.manager.domain.entity.SmsPersonnelDO">
    
    update mm_sms_personnel
    set `name` = #{name,jdbcType=VARCHAR},
      phone_num = #{phoneNum,jdbcType=VARCHAR},
      reserved_field1 = #{reservedField1,jdbcType=VARCHAR},
      reserved_field2 = #{reservedField2,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateBatch" parameterType="java.util.List">
    
    update mm_sms_personnel
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="`name` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.name,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="phone_num = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.phoneNum,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="reserved_field1 = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.reservedField1,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="reserved_field2 = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.reservedField2,jdbcType=VARCHAR}
        </foreach>
      </trim>
      <trim prefix="remark = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          when id = #{item.id,jdbcType=BIGINT} then #{item.remark,jdbcType=VARCHAR}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <update id="updateBatchSelective" parameterType="java.util.List">
    
    update mm_sms_personnel
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="`name` = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.name != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.name,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="phone_num = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.phoneNum != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.phoneNum,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="reserved_field1 = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.reservedField1 != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.reservedField1,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="reserved_field2 = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.reservedField2 != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.reservedField2,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
      <trim prefix="remark = case" suffix="end,">
        <foreach collection="list" index="index" item="item">
          <if test="item.remark != null">
            when id = #{item.id,jdbcType=BIGINT} then #{item.remark,jdbcType=VARCHAR}
          </if>
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="list" item="item" open="(" separator=", ">
      #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
  <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="map" useGeneratedKeys="true">
    
    insert into mm_sms_personnel
    (`name`, phone_num, reserved_field1, reserved_field2, remark)
    values
    <foreach collection="list" item="item" separator=",">
      (#{item.name,jdbcType=VARCHAR}, #{item.phoneNum,jdbcType=VARCHAR}, #{item.reservedField1,jdbcType=VARCHAR}, 
        #{item.reservedField2,jdbcType=VARCHAR}, #{item.remark,jdbcType=VARCHAR})
    </foreach>
  </insert>

  <select id="listByCondition" resultType="com.pttl.mobile.manager.domain.dto.SmsPersonnelResponseDTO">
    SELECT
      sp.id,
      sp.NAME,
      sp.phone_num,
      sr.sms_condition,
      sm.model_name,
      sm.model_content
    FROM
      mm_sms_personnel AS sp
        LEFT JOIN mm_sms_relation AS sr ON sp.id = sr.personnel_id
        LEFT JOIN mm_sms_model sm ON sm.id = sr.model_id
    <where>
      <if test="name != null and name != ''">
        sp.`name` LIKE CONCAT('%', #{name}, '%')
      </if>
      <if test="phoneNum != null and phoneNum != ''">
        AND sp.phone_num LIKE CONCAT('%', #{phoneNum}, '%')
      </if>
    </where>

  </select>

  <select id="getNeedSendWarningSMSMessagesPhoneBySmsType"
            resultType="com.pttl.mobile.manager.domain.dto.SmsPersonnelResponseDTO">
    SELECT
      sp.id,
      sp.NAME,
      sp.phone_num,
      sr.sms_condition,
      sm.model_name,
      sm.model_content
    FROM
      mm_sms_personnel AS sp
        LEFT JOIN mm_sms_relation AS sr ON sp.id = sr.personnel_id
        LEFT JOIN mm_sms_model sm ON sm.id = sr.model_id
    WHERE
      sr.sms_type = #{type}
    </select>

  <select id="detailsById" resultType="com.pttl.mobile.manager.domain.dto.SmsPersonnelDetailsResponseDTO">
    SELECT
      sp.id,
      sp.NAME,
      sp.phone_num,
      sr.sms_condition,
      sm.model_name,
      sm.model_content,
      sr.model_id
    FROM
      mm_sms_personnel AS sp
        LEFT JOIN mm_sms_relation AS sr ON sp.id = sr.personnel_id
        LEFT JOIN mm_sms_model sm ON sm.id = sr.model_id
    WHERE
      sp.id = #{id}
  </select>
</mapper>