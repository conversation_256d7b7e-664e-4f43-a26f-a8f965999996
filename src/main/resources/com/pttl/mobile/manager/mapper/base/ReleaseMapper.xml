<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pttl.mobile.manager.dao.ReleaseMapper" >
  <resultMap id="BaseResultMap" type="com.pttl.mobile.manager.domain.po.Release" >
    <id column="id" property="id" jdbcType="VARCHAR" />
    <result column="version" property="version" jdbcType="VARCHAR" />
    <result column="download_url" property="downloadUrl" jdbcType="VARCHAR" />
    <result column="system_platform" property="systemPlatform" jdbcType="TINYINT" />
    <result column="type" property="type" jdbcType="TINYINT" />
    <result column="operating_system" property="operatingSystem" jdbcType="TINYINT" />
    <result column="is_forced" property="isForced" jdbcType="TINYINT" />
    <result column="release_date" property="releaseDate" jdbcType="TIMESTAMP" />
    <result column="description" property="description" jdbcType="VARCHAR" />
    <result column="env" property="env" jdbcType="TINYINT" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, version, download_url, system_platform, type, operating_system, is_forced, release_date, 
    description, env
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from `release`
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from `release`
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.pttl.mobile.manager.domain.po.Release" >
    insert into `release` (id, version, download_url,
      system_platform, type, operating_system, 
      is_forced, release_date, description, 
      env)
    values (#{id,jdbcType=VARCHAR}, #{version,jdbcType=VARCHAR}, #{downloadUrl,jdbcType=VARCHAR}, 
      ${@com.pttl.mobile.manager.lib.ContextUtil@systemPlatform()}, #{type,jdbcType=TINYINT}, #{operatingSystem,jdbcType=TINYINT},
      #{isForced,jdbcType=TINYINT}, #{releaseDate,jdbcType=TIMESTAMP}, #{description,jdbcType=VARCHAR},
      ${@com.pttl.mobile.manager.lib.ContextUtil@currentEnv()})
  </insert>
  <insert id="insertSelective" parameterType="com.pttl.mobile.manager.domain.po.Release" >
    insert into `release`
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="version != null" >
        version,
      </if>
      <if test="downloadUrl != null" >
        download_url,
      </if>
      <if test="systemPlatform != null" >
        system_platform,
      </if>
      <if test="type != null" >
        type,
      </if>
      <if test="operatingSystem != null" >
        operating_system,
      </if>
      <if test="isForced != null" >
        is_forced,
      </if>
      <if test="releaseDate != null" >
        release_date,
      </if>
      <if test="description != null" >
        description,
      </if>
      <if test="env != null" >
        env,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="version != null" >
        #{version,jdbcType=VARCHAR},
      </if>
      <if test="downloadUrl != null" >
        #{downloadUrl,jdbcType=VARCHAR},
      </if>
      <if test="systemPlatform != null" >
        #{systemPlatform,jdbcType=TINYINT},
      </if>
      <if test="type != null" >
        #{type,jdbcType=TINYINT},
      </if>
      <if test="operatingSystem != null" >
        #{operatingSystem,jdbcType=TINYINT},
      </if>
      <if test="isForced != null" >
        #{isForced,jdbcType=TINYINT},
      </if>
      <if test="releaseDate != null" >
        #{releaseDate,jdbcType=TIMESTAMP},
      </if>
      <if test="description != null" >
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="env != null" >
        #{env,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.pttl.mobile.manager.domain.po.Release" >
    update `release`
    <set >
      <if test="version != null" >
        version = #{version,jdbcType=VARCHAR},
      </if>
      <if test="downloadUrl != null" >
        download_url = #{downloadUrl,jdbcType=VARCHAR},
      </if>
      <if test="systemPlatform != null" >
        system_platform = #{systemPlatform,jdbcType=TINYINT},
      </if>
      <if test="type != null" >
        type = #{type,jdbcType=TINYINT},
      </if>
      <if test="operatingSystem != null" >
        operating_system = #{operatingSystem,jdbcType=TINYINT},
      </if>
      <if test="isForced != null" >
        is_forced = #{isForced,jdbcType=TINYINT},
      </if>
      <if test="releaseDate != null" >
        release_date = #{releaseDate,jdbcType=TIMESTAMP},
      </if>
      <if test="description != null" >
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="env != null" >
        env = #{env,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.pttl.mobile.manager.domain.po.Release" >
    update `release`
    set version = #{version,jdbcType=VARCHAR},
      download_url = #{downloadUrl,jdbcType=VARCHAR},
      system_platform = #{systemPlatform,jdbcType=TINYINT},
      type = #{type,jdbcType=TINYINT},
      operating_system = #{operatingSystem,jdbcType=TINYINT},
      is_forced = #{isForced,jdbcType=TINYINT},
      release_date = #{releaseDate,jdbcType=TIMESTAMP},
      description = #{description,jdbcType=VARCHAR},
      env = #{env,jdbcType=TINYINT}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>