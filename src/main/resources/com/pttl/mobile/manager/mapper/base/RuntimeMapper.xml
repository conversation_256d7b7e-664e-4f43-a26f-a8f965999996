<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pttl.mobile.manager.dao.RuntimeMapper" >
  <resultMap id="BaseResultMap" type="com.pttl.mobile.manager.domain.po.Runtime" >
    <id column="id" property="id" jdbcType="VARCHAR" />
    <result column="version" property="version" jdbcType="VARCHAR" />
    <result column="description" property="description" jdbcType="VARCHAR" />
    <result column="package_name" property="packageName" jdbcType="VARCHAR" />
    <result column="package_url" property="packageUrl" jdbcType="VARCHAR" />
    <result column="create_date" property="createDate" jdbcType="TIMESTAMP" />
    <result column="system_platform" property="systemPlatform" jdbcType="TINYINT" />
    <result column="env" property="env" jdbcType="TINYINT" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, version, description, package_name, package_url, create_date, system_platform, 
    env
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from runtime
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from runtime
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.pttl.mobile.manager.domain.po.Runtime" >
    insert into runtime (id, version, description, 
      package_name, package_url, create_date, 
      system_platform, env)
    values (#{id,jdbcType=VARCHAR}, #{version,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR}, 
      #{packageName,jdbcType=VARCHAR}, #{packageUrl,jdbcType=VARCHAR}, #{createDate,jdbcType=TIMESTAMP}, 
      ${@com.pttl.mobile.manager.lib.ContextUtil@systemPlatform()},
      ${@com.pttl.mobile.manager.lib.ContextUtil@currentEnv()})
  </insert>
  <insert id="insertSelective" parameterType="com.pttl.mobile.manager.domain.po.Runtime" >
    insert into runtime
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="version != null" >
        version,
      </if>
      <if test="description != null" >
        description,
      </if>
      <if test="packageName != null" >
        package_name,
      </if>
      <if test="packageUrl != null" >
        package_url,
      </if>
      <if test="createDate != null" >
        create_date,
      </if>
      <if test="systemPlatform != null" >
        system_platform,
      </if>
      <if test="env != null" >
        env,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="version != null" >
        #{version,jdbcType=VARCHAR},
      </if>
      <if test="description != null" >
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="packageName != null" >
        #{packageName,jdbcType=VARCHAR},
      </if>
      <if test="packageUrl != null" >
        #{packageUrl,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null" >
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="systemPlatform != null" >
        #{systemPlatform,jdbcType=TINYINT},
      </if>
      <if test="env != null" >
        #{env,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.pttl.mobile.manager.domain.po.Runtime" >
    update runtime
    <set >
      <if test="version != null" >
        version = #{version,jdbcType=VARCHAR},
      </if>
      <if test="description != null" >
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="packageName != null" >
        package_name = #{packageName,jdbcType=VARCHAR},
      </if>
      <if test="packageUrl != null" >
        package_url = #{packageUrl,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null" >
        create_date = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="systemPlatform != null" >
        system_platform = #{systemPlatform,jdbcType=TINYINT},
      </if>
      <if test="env != null" >
        env = #{env,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.pttl.mobile.manager.domain.po.Runtime" >
    update runtime
    set version = #{version,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      package_name = #{packageName,jdbcType=VARCHAR},
      package_url = #{packageUrl,jdbcType=VARCHAR},
      create_date = #{createDate,jdbcType=TIMESTAMP},
      system_platform = #{systemPlatform,jdbcType=TINYINT},
      env = #{env,jdbcType=TINYINT}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>