<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pttl.mobile.manager.dao.ConfigurationMapper" >
  <resultMap id="BaseResultMap" type="com.pttl.mobile.manager.domain.po.Configuration" >
    <id column="name" property="name" jdbcType="VARCHAR" />
    <id column="system_platform" property="systemPlatform" jdbcType="TINYINT" />
    <id column="env" property="env" jdbcType="TINYINT" />
    <result column="description" property="description" jdbcType="VARCHAR" />
    <result column="value" property="value" jdbcType="CHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    name, system_platform, env, description, value
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="com.pttl.mobile.manager.domain.po.ConfigurationKey" >
    select 
    <include refid="Base_Column_List" />
    from configuration
    where name = #{name,jdbcType=VARCHAR}
      and system_platform = #{systemPlatform,jdbcType=TINYINT}
      and env = #{env,jdbcType=TINYINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="com.pttl.mobile.manager.domain.po.ConfigurationKey" >
    delete from configuration
    where name = #{name,jdbcType=VARCHAR}
      and system_platform = #{systemPlatform,jdbcType=TINYINT}
      and env = #{env,jdbcType=TINYINT}
  </delete>
  <insert id="insert" parameterType="com.pttl.mobile.manager.domain.po.Configuration" >
    insert into configuration (name, system_platform, env, 
      description, value)
    values (#{name,jdbcType=VARCHAR}, ${@com.pttl.mobile.manager.lib.ContextUtil@systemPlatform()},
      ${@com.pttl.mobile.manager.lib.ContextUtil@currentEnv()},
      #{description,jdbcType=VARCHAR}, #{value,jdbcType=CHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.pttl.mobile.manager.domain.po.Configuration" >
    insert into configuration
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="name != null" >
        name,
      </if>
      <if test="systemPlatform != null" >
        system_platform,
      </if>
      <if test="env != null" >
        env,
      </if>
      <if test="description != null" >
        description,
      </if>
      <if test="value != null" >
        value,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="name != null" >
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="systemPlatform != null" >
        #{systemPlatform,jdbcType=TINYINT},
      </if>
      <if test="env != null" >
        #{env,jdbcType=TINYINT},
      </if>
      <if test="description != null" >
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="value != null" >
        #{value,jdbcType=CHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.pttl.mobile.manager.domain.po.Configuration" >
    update configuration
    <set >
      <if test="description != null" >
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="value != null" >
        value = #{value,jdbcType=CHAR},
      </if>
    </set>
    where name = #{name,jdbcType=VARCHAR}
      and system_platform = #{systemPlatform,jdbcType=TINYINT}
      and env = #{env,jdbcType=TINYINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.pttl.mobile.manager.domain.po.Configuration" >
    update configuration
    set description = #{description,jdbcType=VARCHAR},
      value = #{value,jdbcType=CHAR}
    where name = #{name,jdbcType=VARCHAR}
      and system_platform = #{systemPlatform,jdbcType=TINYINT}
      and env = #{env,jdbcType=TINYINT}
  </update>
</mapper>