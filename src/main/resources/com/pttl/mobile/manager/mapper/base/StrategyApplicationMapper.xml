<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pttl.mobile.manager.dao.StrategyApplicationMapper" >
  <resultMap id="BaseResultMap" type="com.pttl.mobile.manager.domain.po.StrategyApplication" >
    <id column="id" property="id" jdbcType="VARCHAR" />
    <result column="strategy_id" property="strategyId" jdbcType="VARCHAR" />
    <result column="application_id" property="applicationId" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, strategy_id, application_id
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from strategy_application
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from strategy_application
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.pttl.mobile.manager.domain.po.StrategyApplication" >
    insert into strategy_application (id, strategy_id, application_id
      )
    values (#{id,jdbcType=VARCHAR}, #{strategyId,jdbcType=VARCHAR}, #{applicationId,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.pttl.mobile.manager.domain.po.StrategyApplication" >
    insert into strategy_application
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="strategyId != null" >
        strategy_id,
      </if>
      <if test="applicationId != null" >
        application_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="strategyId != null" >
        #{strategyId,jdbcType=VARCHAR},
      </if>
      <if test="applicationId != null" >
        #{applicationId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.pttl.mobile.manager.domain.po.StrategyApplication" >
    update strategy_application
    <set >
      <if test="strategyId != null" >
        strategy_id = #{strategyId,jdbcType=VARCHAR},
      </if>
      <if test="applicationId != null" >
        application_id = #{applicationId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.pttl.mobile.manager.domain.po.StrategyApplication" >
    update strategy_application
    set strategy_id = #{strategyId,jdbcType=VARCHAR},
      application_id = #{applicationId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>