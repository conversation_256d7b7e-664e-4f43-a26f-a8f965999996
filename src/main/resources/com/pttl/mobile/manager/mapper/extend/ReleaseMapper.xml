<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pttl.mobile.manager.dao.ReleaseMapper">
    <resultMap id="BaseResultMapExtend" extends="BaseResultMap"
               type="com.pttl.mobile.manager.domain.dto.ReleaseExtendDTO">

    </resultMap>
    <select id="getReleaseHistory" resultMap="BaseResultMapExtend">
        SELECT
        re.id,
        re.version,
        re.download_url,
        re.system_platform,
        re.type,
        re.operating_system,
        re.is_forced,
        re.release_date,
        re.description,
        re.env,
        sp.name AS systemPlatformName,
        rt.name AS typeName,
        os.name AS operatingSystemName
        FROM `release` AS re
        LEFT JOIN system_platform AS sp
        ON sp.id=re.system_platform
        LEFT JOIN release_type AS rt
        ON rt.id=re.type
        LEFT JOIN operating_system AS os
        ON os.id=re.operating_system
        <where>
            <if test="type != null">and re.type=#{type}</if>
            <if test="true">and re.system_platform=${@com.pttl.mobile.manager.lib.ContextUtil@systemPlatform()}</if>
            <if test="true">and re.env=${@com.pttl.mobile.manager.lib.ContextUtil@currentEnv()}</if>
            <if test="version != null and version.length>0">and re.version=#{version}</if>
            <if test="keyword != null and keyword.length>0">and re.description like CONCAT('%',#{keyword},'%')</if>
            <if test="start_date != null">and re.release_date&gt;=#{start_date}</if>
            <if test="end_date != null">and re.release_date&lt;=#{end_date}</if>
        </where>
        order by re.release_date desc
    </select>
    <select id="isVersionExists" resultType="java.lang.Integer">
        select
        count(1)
        from `release`
        where type = #{type}
        and system_platform = ${@com.pttl.mobile.manager.lib.ContextUtil@systemPlatform()}
        and env = ${@com.pttl.mobile.manager.lib.ContextUtil@currentEnv()}
        and operating_system = #{operating_system}
        and version = #{version}
    </select>
    <select id="getReleaseLastVersion" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
         from `release`
        <where>
            system_platform=${@com.pttl.mobile.manager.lib.ContextUtil@systemPlatform()}
            and env=${@com.pttl.mobile.manager.lib.ContextUtil@currentEnv()}
            <if test="type != null">and type=#{type}</if>
            <if test="operating_system != null">and operating_system=#{operating_system}</if>
        </where>
        ORDER BY release_date DESC LIMIT 1
    </select>
</mapper>