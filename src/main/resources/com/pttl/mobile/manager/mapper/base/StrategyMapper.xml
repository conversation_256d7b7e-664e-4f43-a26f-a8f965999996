<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pttl.mobile.manager.dao.StrategyMapper" >
  <resultMap id="BaseResultMap" type="com.pttl.mobile.manager.domain.po.Strategy" >
    <id column="id" property="id" jdbcType="VARCHAR" />
    <result column="name" property="name" jdbcType="VARCHAR" />
    <result column="description" property="description" jdbcType="VARCHAR" />
    <result column="weight" property="weight" jdbcType="SMALLINT" />
    <result column="create_date" property="createDate" jdbcType="TIMESTAMP" />
    <result column="last_update" property="lastUpdate" jdbcType="TIMESTAMP" />
    <result column="system_platform" property="systemPlatform" jdbcType="TINYINT" />
    <result column="env" property="env" jdbcType="TINYINT" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, name, description, weight, create_date, last_update, system_platform, env
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from strategy
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from strategy
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.pttl.mobile.manager.domain.po.Strategy" >
    insert into strategy (id, name, description, 
      weight, create_date, last_update, 
      system_platform, env)
    values (#{id,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR}, 
      #{weight,jdbcType=SMALLINT}, #{createDate,jdbcType=TIMESTAMP}, #{lastUpdate,jdbcType=TIMESTAMP}, 
      #{systemPlatform,jdbcType=TINYINT}, #{env,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="com.pttl.mobile.manager.domain.po.Strategy" >
    insert into strategy
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="name != null" >
        name,
      </if>
      <if test="description != null" >
        description,
      </if>
      <if test="weight != null" >
        weight,
      </if>
      <if test="createDate != null" >
        create_date,
      </if>
      <if test="lastUpdate != null" >
        last_update,
      </if>
      <if test="systemPlatform != null" >
        system_platform,
      </if>
      <if test="env != null" >
        env,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="name != null" >
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="description != null" >
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="weight != null" >
        #{weight,jdbcType=SMALLINT},
      </if>
      <if test="createDate != null" >
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdate != null" >
        #{lastUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="systemPlatform != null" >
        #{systemPlatform,jdbcType=TINYINT},
      </if>
      <if test="env != null" >
        #{env,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.pttl.mobile.manager.domain.po.Strategy" >
    update strategy
    <set >
      <if test="name != null" >
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="description != null" >
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="weight != null" >
        weight = #{weight,jdbcType=SMALLINT},
      </if>
      <if test="createDate != null" >
        create_date = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdate != null" >
        last_update = #{lastUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="systemPlatform != null" >
        system_platform = #{systemPlatform,jdbcType=TINYINT},
      </if>
      <if test="env != null" >
        env = #{env,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.pttl.mobile.manager.domain.po.Strategy" >
    update strategy
    set name = #{name,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      weight = #{weight,jdbcType=SMALLINT},
      create_date = #{createDate,jdbcType=TIMESTAMP},
      last_update = #{lastUpdate,jdbcType=TIMESTAMP},
      system_platform = #{systemPlatform,jdbcType=TINYINT},
      env = #{env,jdbcType=TINYINT}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>