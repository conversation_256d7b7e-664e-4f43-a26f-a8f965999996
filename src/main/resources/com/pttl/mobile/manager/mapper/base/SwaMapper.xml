<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pttl.mobile.manager.dao.SwaMapper" >
  <resultMap id="BaseResultMap" type="com.pttl.mobile.manager.domain.po.Swa" >
    <id column="id" property="id" jdbcType="VARCHAR" />
    <result column="application_id" property="applicationId" jdbcType="VARCHAR" />
    <result column="type" property="type" jdbcType="TINYINT" />
    <result column="login_type" property="loginType" jdbcType="TINYINT" />
    <result column="login_field" property="loginField" jdbcType="TINYINT" />
    <result column="url" property="url" jdbcType="VARCHAR" />
    <result column="is_customized_char_used" property="isCustomizedCharUsed" jdbcType="TINYINT" />
    <result column="prefix" property="prefix" jdbcType="VARCHAR" />
    <result column="suffix" property="suffix" jdbcType="VARCHAR" />
    <result column="create_date" property="createDate" jdbcType="TIMESTAMP" />
    <result column="last_update" property="lastUpdate" jdbcType="TIMESTAMP" />
    <result column="system_platform" property="systemPlatform" jdbcType="TINYINT" />
    <result column="env" property="env" jdbcType="TINYINT" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, application_id, type, login_type, login_field, url, is_customized_char_used, 
    prefix, suffix, create_date, last_update, system_platform, env
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from swa
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from swa
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.pttl.mobile.manager.domain.po.Swa" >
    insert into swa (id, application_id, type, 
      login_type, login_field, url, 
      is_customized_char_used, prefix, suffix, 
      create_date, last_update, system_platform, 
      env)
    values (#{id,jdbcType=VARCHAR}, #{applicationId,jdbcType=VARCHAR}, #{type,jdbcType=TINYINT}, 
      #{loginType,jdbcType=TINYINT}, #{loginField,jdbcType=TINYINT}, #{url,jdbcType=VARCHAR}, 
      #{isCustomizedCharUsed,jdbcType=TINYINT}, #{prefix,jdbcType=VARCHAR}, #{suffix,jdbcType=VARCHAR}, 
      #{createDate,jdbcType=TIMESTAMP}, #{lastUpdate,jdbcType=TIMESTAMP}, ${@com.pttl.mobile.manager.lib.ContextUtil@systemPlatform()},
      ${@com.pttl.mobile.manager.lib.ContextUtil@currentEnv()})
  </insert>
  <insert id="insertSelective" parameterType="com.pttl.mobile.manager.domain.po.Swa" >
    insert into swa
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="applicationId != null" >
        application_id,
      </if>
      <if test="type != null" >
        type,
      </if>
      <if test="loginType != null" >
        login_type,
      </if>
      <if test="loginField != null" >
        login_field,
      </if>
      <if test="url != null" >
        url,
      </if>
      <if test="isCustomizedCharUsed != null" >
        is_customized_char_used,
      </if>
      <if test="prefix != null" >
        prefix,
      </if>
      <if test="suffix != null" >
        suffix,
      </if>
      <if test="createDate != null" >
        create_date,
      </if>
      <if test="lastUpdate != null" >
        last_update,
      </if>
      <if test="systemPlatform != null" >
        system_platform,
      </if>
      <if test="env != null" >
        env,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="applicationId != null" >
        #{applicationId,jdbcType=VARCHAR},
      </if>
      <if test="type != null" >
        #{type,jdbcType=TINYINT},
      </if>
      <if test="loginType != null" >
        #{loginType,jdbcType=TINYINT},
      </if>
      <if test="loginField != null" >
        #{loginField,jdbcType=TINYINT},
      </if>
      <if test="url != null" >
        #{url,jdbcType=VARCHAR},
      </if>
      <if test="isCustomizedCharUsed != null" >
        #{isCustomizedCharUsed,jdbcType=TINYINT},
      </if>
      <if test="prefix != null" >
        #{prefix,jdbcType=VARCHAR},
      </if>
      <if test="suffix != null" >
        #{suffix,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null" >
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdate != null" >
        #{lastUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="systemPlatform != null" >
        #{systemPlatform,jdbcType=TINYINT},
      </if>
      <if test="env != null" >
        #{env,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.pttl.mobile.manager.domain.po.Swa" >
    update swa
    <set >
      <if test="applicationId != null" >
        application_id = #{applicationId,jdbcType=VARCHAR},
      </if>
      <if test="type != null" >
        type = #{type,jdbcType=TINYINT},
      </if>
      <if test="loginType != null" >
        login_type = #{loginType,jdbcType=TINYINT},
      </if>
      <if test="loginField != null" >
        login_field = #{loginField,jdbcType=TINYINT},
      </if>
      <if test="url != null" >
        url = #{url,jdbcType=VARCHAR},
      </if>
      <if test="isCustomizedCharUsed != null" >
        is_customized_char_used = #{isCustomizedCharUsed,jdbcType=TINYINT},
      </if>
      <if test="prefix != null" >
        prefix = #{prefix,jdbcType=VARCHAR},
      </if>
      <if test="suffix != null" >
        suffix = #{suffix,jdbcType=VARCHAR},
      </if>
      <if test="createDate != null" >
        create_date = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdate != null" >
        last_update = #{lastUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="systemPlatform != null" >
        system_platform = #{systemPlatform,jdbcType=TINYINT},
      </if>
      <if test="env != null" >
        env = #{env,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.pttl.mobile.manager.domain.po.Swa" >
    update swa
    set application_id = #{applicationId,jdbcType=VARCHAR},
      type = #{type,jdbcType=TINYINT},
      login_type = #{loginType,jdbcType=TINYINT},
      login_field = #{loginField,jdbcType=TINYINT},
      url = #{url,jdbcType=VARCHAR},
      is_customized_char_used = #{isCustomizedCharUsed,jdbcType=TINYINT},
      prefix = #{prefix,jdbcType=VARCHAR},
      suffix = #{suffix,jdbcType=VARCHAR},
      create_date = #{createDate,jdbcType=TIMESTAMP},
      last_update = #{lastUpdate,jdbcType=TIMESTAMP},
      system_platform = #{systemPlatform,jdbcType=TINYINT},
      env = #{env,jdbcType=TINYINT}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>