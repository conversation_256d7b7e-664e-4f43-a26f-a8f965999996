<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pttl.mobile.manager.dao.ClientMapper">
    <select id="getReleaseLastVersion" resultType="com.pttl.mobile.manager.domain.dto.ReleaseClientDTO">
        select
        version,
        download_url as downloadUrl,
        is_forced as isForced,
        release_date as releaseDate,
        description
        from `release`
        <where>
            system_platform=${@com.pttl.mobile.manager.lib.ContextUtil@systemPlatform()}
            and env=${@com.pttl.mobile.manager.lib.ContextUtil@currentEnv()}
            <if test="type != null">and type=#{type}</if>
            <if test="operating_system != null">and operating_system=#{operating_system}</if>
        </where>
        ORDER BY release_date DESC LIMIT 1
    </select>

    <select id="getReleaseMallLastVersion" resultType="com.pttl.mobile.manager.domain.dto.ReleaseClientDTO">
        select
        version,
        download_url as downloadUrl,
        is_forced as isForced,
        release_date as releaseDate,
        description
        from `release_mall`
        <where>
            system_platform=${@com.pttl.mobile.manager.lib.ContextUtil@systemPlatform()}
            and env=${@com.pttl.mobile.manager.lib.ContextUtil@currentEnv()}
            <if test="type != null">and type=#{type}</if>
            <if test="operating_system != null">and operating_system=#{operating_system}</if>
        </where>
        ORDER BY release_date DESC LIMIT 1
    </select>

    <select id="getReleaseHongmengLastVersion" resultType="com.pttl.mobile.manager.domain.dto.ReleaseClientDTO">
        select
        version,
        download_url as downloadUrl,
        is_forced as isForced,
        release_date as releaseDate,
        description
        from `release_hongmeng`
        <where>
            system_platform=${@com.pttl.mobile.manager.lib.ContextUtil@systemPlatform()}
            and env=${@com.pttl.mobile.manager.lib.ContextUtil@currentEnv()}
            <if test="type != null">and type=#{type}</if>
            <if test="operating_system != null">and operating_system=#{operating_system}</if>
        </where>
        ORDER BY release_date DESC LIMIT 1
    </select>

    <select id="getRuntime" resultType="com.pttl.mobile.manager.domain.dto.RuntimeClientDTO">
        select
        id,
        version,
        description,
        package_name as packageName,
        package_url as packageUrl,
        create_date as createDate
        from runtime
        where system_platform=${@com.pttl.mobile.manager.lib.ContextUtil@systemPlatform()}
        and env=${@com.pttl.mobile.manager.lib.ContextUtil@currentEnv()}
        ORDER BY create_date DESC
    </select>
    <select id="getConfigurationValueByName" resultType="java.lang.String">
        select value
        from configuration
        where name=#{name}
        and system_platform=${@com.pttl.mobile.manager.lib.ContextUtil@systemPlatform()}
        and env=${@com.pttl.mobile.manager.lib.ContextUtil@currentEnv()}
        LIMIT 1
    </select>
    <select id="getApplication" resultType="com.pttl.mobile.manager.domain.dto.ApplicationClientItemDTO">
        SELECT
         a.id,
         a.name,
         a.description,
         a.address,
         a.inner_address as innerAddress,
         a.logo_url as logoUrl,
         a.package_name as packageName,
         a.package_url as packageUrl,
         a.type,
         a.is_visible as isVisible,
         a.version,
         a.application_group_id as applicationGroupId,
         ag.name as applicationGroupName,
         ag.weight
        FROM application a
        LEFT JOIN application_group AS ag
        ON a.application_group_id = ag.id
        WHERE a.system_platform = ${@com.pttl.mobile.manager.lib.ContextUtil@systemPlatform()}
        and a.env = ${@com.pttl.mobile.manager.lib.ContextUtil@currentEnv()}
        ORDER BY a.last_update DESC
    </select>
    <select id="getApplicationAdapterByIds" resultType="com.pttl.mobile.manager.domain.dto.ApplicationAdapterClientDTO">
        select
        application_id as applicationId,
        manifest as manifestStr,
        runtime_primary_version as runtimePrimaryVersion,
        runtime_version as runtimeVersion,
        is_specific_runtime_used as isSpecificRuntimeUsed,
        last_update as lastUpdate
        from application_adapter
        where application_id in
        <foreach collection="ids" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        and system_platform=${@com.pttl.mobile.manager.lib.ContextUtil@systemPlatform()}
        and env=${@com.pttl.mobile.manager.lib.ContextUtil@currentEnv()}
    </select>
    <select id="getSwa" resultType="com.pttl.mobile.manager.domain.dto.SwaClientDTO">
        SELECT
        t1.application_id as applicationId,
        t3.name as applicationName,
        t2.iframe_xpath as iframeXpath,
        t1.is_customized_char_used as isCustomizedCharUsed,
        t1.login_field as loginField,
        t2.login_button_xpath as loginButtonXpath,
        t2.username_xpath as usernameXpath,
        t2.password_xpath as passwordXpath,
        t1.prefix,
        t1.suffix,
        t1.type,
        t1.login_type as loginType,
        t2.login_page_type as loginPageType,
        t1.url,
        t1.create_date as createDate,
        t1.last_update as lastUpdate
        FROM swa t1
        left join swa_keeper t2 on t1.id = t2.swa_id
        inner join application t3 on t1.application_id = t3.id
        WHERE t1.system_platform = ${@com.pttl.mobile.manager.lib.ContextUtil@systemPlatform()}
        and t1.env=${@com.pttl.mobile.manager.lib.ContextUtil@currentEnv()}
        order by t1.last_update desc
    </select>
</mapper>