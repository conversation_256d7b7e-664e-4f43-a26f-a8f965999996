<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pttl.mobile.manager.dao.SwaKeeperMapper" >
  <resultMap id="BaseResultMap" type="com.pttl.mobile.manager.domain.po.SwaKeeper" >
    <id column="swa_id" property="swaId" jdbcType="VARCHAR" />
    <result column="username_xpath" property="usernameXpath" jdbcType="VARCHAR" />
    <result column="password_xpath" property="passwordXpath" jdbcType="VARCHAR" />
    <result column="login_button_xpath" property="loginButtonXpath" jdbcType="VARCHAR" />
    <result column="login_page_type" property="loginPageType" jdbcType="TINYINT" />
    <result column="iframe_xpath" property="iframeXpath" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    swa_id, username_xpath, password_xpath, login_button_xpath, login_page_type, iframe_xpath
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from swa_keeper
    where swa_id = #{swaId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from swa_keeper
    where swa_id = #{swaId,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.pttl.mobile.manager.domain.po.SwaKeeper" >
    insert into swa_keeper (swa_id, username_xpath, password_xpath, 
      login_button_xpath, login_page_type, iframe_xpath
      )
    values (#{swaId,jdbcType=VARCHAR}, #{usernameXpath,jdbcType=VARCHAR}, #{passwordXpath,jdbcType=VARCHAR}, 
      #{loginButtonXpath,jdbcType=VARCHAR}, #{loginPageType,jdbcType=TINYINT}, #{iframeXpath,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.pttl.mobile.manager.domain.po.SwaKeeper" >
    insert into swa_keeper
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="swaId != null" >
        swa_id,
      </if>
      <if test="usernameXpath != null" >
        username_xpath,
      </if>
      <if test="passwordXpath != null" >
        password_xpath,
      </if>
      <if test="loginButtonXpath != null" >
        login_button_xpath,
      </if>
      <if test="loginPageType != null" >
        login_page_type,
      </if>
      <if test="iframeXpath != null" >
        iframe_xpath,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="swaId != null" >
        #{swaId,jdbcType=VARCHAR},
      </if>
      <if test="usernameXpath != null" >
        #{usernameXpath,jdbcType=VARCHAR},
      </if>
      <if test="passwordXpath != null" >
        #{passwordXpath,jdbcType=VARCHAR},
      </if>
      <if test="loginButtonXpath != null" >
        #{loginButtonXpath,jdbcType=VARCHAR},
      </if>
      <if test="loginPageType != null" >
        #{loginPageType,jdbcType=TINYINT},
      </if>
      <if test="iframeXpath != null" >
        #{iframeXpath,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.pttl.mobile.manager.domain.po.SwaKeeper" >
    update swa_keeper
    <set >
      <if test="usernameXpath != null" >
        username_xpath = #{usernameXpath,jdbcType=VARCHAR},
      </if>
      <if test="passwordXpath != null" >
        password_xpath = #{passwordXpath,jdbcType=VARCHAR},
      </if>
      <if test="loginButtonXpath != null" >
        login_button_xpath = #{loginButtonXpath,jdbcType=VARCHAR},
      </if>
      <if test="loginPageType != null" >
        login_page_type = #{loginPageType,jdbcType=TINYINT},
      </if>
      <if test="iframeXpath != null" >
        iframe_xpath = #{iframeXpath,jdbcType=VARCHAR},
      </if>
    </set>
    where swa_id = #{swaId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.pttl.mobile.manager.domain.po.SwaKeeper" >
    update swa_keeper
    set username_xpath = #{usernameXpath,jdbcType=VARCHAR},
      password_xpath = #{passwordXpath,jdbcType=VARCHAR},
      login_button_xpath = #{loginButtonXpath,jdbcType=VARCHAR},
      login_page_type = #{loginPageType,jdbcType=TINYINT},
      iframe_xpath = #{iframeXpath,jdbcType=VARCHAR}
    where swa_id = #{swaId,jdbcType=VARCHAR}
  </update>
</mapper>