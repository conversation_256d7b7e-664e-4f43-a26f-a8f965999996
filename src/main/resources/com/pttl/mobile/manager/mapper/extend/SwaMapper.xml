<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pttl.mobile.manager.dao.SwaMapper">
    <resultMap id="BaseResultMapExtend" extends="BaseResultMap"
               type="com.pttl.mobile.manager.domain.dto.SwaExtendDTO">

    </resultMap>
    <select id="isSwaExists" resultType="java.lang.Integer">
        SELECT count(1) FROM swa t1
        INNER JOIN swa_keeper t2
        ON t1.id = t2.swa_id
        WHERE t1.application_id = #{applicationId}
        and system_platform=${@com.pttl.mobile.manager.lib.ContextUtil@systemPlatform()}
        and env=${@com.pttl.mobile.manager.lib.ContextUtil@currentEnv()}
    </select>
    <select id="getSwa" resultMap="BaseResultMapExtend">
        SELECT t1.*,
        t2.login_page_type as loginPageType,
        t2.iframe_xpath as iframeXpath,
        t2.username_xpath as usernameXpath,
        t2.password_xpath as passwordXpath,
        t2.login_button_xpath as loginButtonXpath,
        t3.name as applicationName
        FROM swa t1
        left join swa_keeper t2 on t1.id = t2.swa_id
        inner join application t3 on t1.application_id = t3.id
        WHERE t1.system_platform = ${@com.pttl.mobile.manager.lib.ContextUtil@systemPlatform()}
        and t1.env=${@com.pttl.mobile.manager.lib.ContextUtil@currentEnv()}
        order by last_update desc
    </select>
    <update id="update">
        update `swa`
        <set>
            <if test="swa.application_id != null">
                application_id = #{swa.application_id},
            </if>
            <if test="swa.type != null">
                type = #{swa.type},
            </if>
            <if test="swa.login_type != null">
                login_type = #{swa.login_type},
            </if>
            <if test="swa.login_field != null">
                login_field = #{swa.login_field},
            </if>
            <if test="swa.url != null">
                url = #{swa.url},
            </if>
            <if test="swa.is_customized_char_used != null">
                is_customized_char_used = #{swa.is_customized_char_used},
            </if>
            <if test="swa.prefix != null">
                prefix = #{swa.prefix},
            </if>
            <if test="swa.suffix != null">
                suffix = #{swa.suffix},
            </if>
            <if test="swa.last_update != null">
                last_update = #{swa.last_update},
            </if>
        </set>
        where id = #{swa.id}
        and system_platform = ${@com.pttl.mobile.manager.lib.ContextUtil@systemPlatform()}
        and env=${@com.pttl.mobile.manager.lib.ContextUtil@currentEnv()};
        update `swa_keeper`
        <set>
            <if test="swaKeeper.username_xpath != null">
                username_xpath = #{swaKeeper.username_xpath},
            </if>
            <if test="swaKeeper.password_xpath != null">
                password_xpath = #{swaKeeper.password_xpath},
            </if>
            <if test="swaKeeper.login_button_xpath != null">
                login_button_xpath = #{swaKeeper.login_button_xpath},
            </if>
            <if test="swaKeeper.login_page_type != null">
                login_page_type = #{swaKeeper.login_page_type},
            </if>
            <if test="swaKeeper.login_page_type != null">
                iframe_xpath = #{swaKeeper.iframe_xpath},
            </if>
        </set>
        where swa_id = #{swaKeeper.swa_id};
    </update>
    <delete id="delete" parameterType="java.util.List">
        delete from `swa`
        where id in
        <foreach collection="ids" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        and system_platform=${@com.pttl.mobile.manager.lib.ContextUtil@systemPlatform()}
        and env=${@com.pttl.mobile.manager.lib.ContextUtil@currentEnv()}
    </delete>
</mapper>