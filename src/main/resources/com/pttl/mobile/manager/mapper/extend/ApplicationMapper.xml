<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pttl.mobile.manager.dao.ApplicationMapper">
    <resultMap id="BaseResultMapExtend" extends="BaseResultMap"
               type="com.pttl.mobile.manager.domain.dto.ApplicationExtendDTO">
    </resultMap>
    <select id="isApplicationExists" resultType="java.lang.Integer">
        select count(1)
        from application
        where (name = #{idOrName} or id = #{idOrName})
          and system_platform = ${@com.pttl.mobile.manager.lib.ContextUtil@systemPlatform()}
    </select>
    <select id="isApplicationExistsForUpdate" resultType="java.lang.Boolean">
        select count(*)
        from `application`
        where name = #{name}
          and id != #{id}
          and system_platform = ${@com.pttl.mobile.manager.lib.ContextUtil@systemPlatform()}
          and env = ${@com.pttl.mobile.manager.lib.ContextUtil@currentEnv()}
        LIMIT 1
    </select>
    <select id="getApplication" resultMap="BaseResultMapExtend">
        SELECT a.*, ag.name as applicationGroupName, ag.weight
        FROM application a
                     LEFT JOIN application_group AS ag
                ON a.application_group_id = ag.id
        WHERE a.system_platform = ${@com.pttl.mobile.manager.lib.ContextUtil@systemPlatform()}
          and a.env = ${@com.pttl.mobile.manager.lib.ContextUtil@currentEnv()}
        ORDER BY a.last_update DESC
    </select>

    <!--获取全部应用信息 条件可选-->
    <select id="listAllApplication" resultMap="BaseResultMapExtend">
        SELECT a.*, ag.name as applicationGroupName, ag.weight
        FROM application a
                     LEFT JOIN application_group AS ag
                ON a.application_group_id = ag.id
        <where>
            <if test="name != null">
                and a.name like concat('%', #{name}, '%')
            </if>
            <if test="applicationGroupId != null and applicationGroupId != ''">
                and a.application_group_id = #{applicationGroupId}
            </if>
            <!--默认只查询显示的 0 隐藏 1显示-->
            <!--and a.is_visible = 1-->
        </where>
        ORDER BY a.last_update DESC
    </select>

    <select id="getApplicationById" resultMap="BaseResultMapExtend">
        SELECT a.*, ag.name as applicationGroupName
        FROM application a
                     LEFT JOIN application_group AS ag
                ON a.application_group_id = ag.id
        WHERE a.id = #{id}
          and a.system_platform = ${@com.pttl.mobile.manager.lib.ContextUtil@systemPlatform()}
          and a.env = ${@com.pttl.mobile.manager.lib.ContextUtil@currentEnv()}
        LIMIT 1
    </select>
    <delete id="deleteByGroupIds" parameterType="java.util.List">
        delete
        from application
                where application_group_id in
        <foreach collection="groupIds" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        and system_platform = ${@com.pttl.mobile.manager.lib.ContextUtil@systemPlatform()}
        and env = ${@com.pttl.mobile.manager.lib.ContextUtil@currentEnv()}
    </delete>

    <select id="infoListForMobile" resultType="com.pttl.mobile.manager.mobile.dto.ApplicationInfoDTO">
        SELECT app.id,
               app.application_group_id,
               app.NAME,
               app.description,
               app.logo_url,
               app.package_url,
               app.package_name,
               app.address,
               app.inner_address,
               app.type,
               app.version,
               app.is_visible,
               app.last_update as appLastUpdate,
               app.application_weight,
               ag.`name` AS applicationGroupName,
               ag.weight,
               ag.last_update
        FROM application AS app
                     LEFT JOIN
                     application_group as ag ON app.application_group_id = ag.id
        WHERE app.id IN (
                SELECT DISTINCT application_id COLLATE utf8mb4_unicode_ci
                FROM mm_strategy_application
                WHERE strategy_id IN (
                        SELECT strategy_id
                        FROM mm_strategy_user
                        WHERE user_id = #{userId}
                        UNION
                        SELECT strategy_id
                        FROM mm_strategy_department
                        WHERE department_id = (SELECT department_id FROM mm_user WHERE id = #{userId}))
                )
          AND app.is_visible = 1 ORDER BY ag.last_update DESC
    </select>

    <select id="userApplicationByUserId" resultType="com.pttl.mobile.manager.domain.dto.ApplicationInfoForUserDTO">
        SELECT
            app.id,
            app.application_group_id,
            app.NAME,
            app.description,
            app.logo_url,
            app.package_url,
            app.package_name,
            app.address,
            app.inner_address,
            app.type,
            app.is_visible,
            app.system_platform,
            app.last_update,
            ag.`name` AS applicationGroupName,
            ag.weight
        FROM
            application AS app
            LEFT JOIN application_group AS ag ON app.application_group_id = ag.id
        WHERE
            app.id IN (
            SELECT DISTINCT
                application_id COLLATE utf8mb4_unicode_ci
            FROM
                mm_strategy_application
            WHERE
                strategy_id IN (
                SELECT
                    strategy_id
                FROM
                    mm_strategy_user
                WHERE
                    user_id = #{userId}
                UNION
                SELECT
                    strategy_id
                FROM
                    mm_strategy_department
                WHERE
                    department_id = ( SELECT department_id FROM mm_user WHERE id = #{userId}))
                    )
            AND app.is_visible = 1
    </select>

    <update id="updateApplicationWeightById">
        UPDATE application
        SET application_weight = #{applicationWeight}
        WHERE id = #{id}
    </update>
</mapper>