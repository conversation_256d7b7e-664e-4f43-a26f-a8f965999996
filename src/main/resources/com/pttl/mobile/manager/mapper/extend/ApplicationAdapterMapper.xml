<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pttl.mobile.manager.dao.ApplicationAdapterMapper">
    <select id="isApplicationAdapterExists" resultType="java.lang.Integer">
        SELECT count(1) FROM application_adapter
        WHERE scope=#{scope}
        and application_id != #{applicationId}
        and system_platform=${@com.pttl.mobile.manager.lib.ContextUtil@systemPlatform()}
        and env=${@com.pttl.mobile.manager.lib.ContextUtil@currentEnv()}
    </select>
    <select id="getByIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from application_adapter
        where application_id in
        <foreach collection="ids" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        and system_platform=${@com.pttl.mobile.manager.lib.ContextUtil@systemPlatform()}
        and env=${@com.pttl.mobile.manager.lib.ContextUtil@currentEnv()}
    </select>
</mapper>