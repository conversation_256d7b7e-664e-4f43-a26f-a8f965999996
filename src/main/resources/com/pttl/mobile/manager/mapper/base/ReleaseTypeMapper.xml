<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pttl.mobile.manager.dao.ReleaseTypeMapper" >
  <resultMap id="BaseResultMap" type="com.pttl.mobile.manager.domain.po.ReleaseType" >
    <id column="id" property="id" jdbcType="TINYINT" />
    <result column="name" property="name" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, name
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Boolean" >
    select 
    <include refid="Base_Column_List" />
    from release_type
    where id = #{id,jdbcType=TINYINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Boolean" >
    delete from release_type
    where id = #{id,jdbcType=TINYINT}
  </delete>
  <insert id="insert" parameterType="com.pttl.mobile.manager.domain.po.ReleaseType" >
    insert into release_type (id, name)
    values (#{id,jdbcType=TINYINT}, #{name,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.pttl.mobile.manager.domain.po.ReleaseType" >
    insert into release_type
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="name != null" >
        name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=TINYINT},
      </if>
      <if test="name != null" >
        #{name,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.pttl.mobile.manager.domain.po.ReleaseType" >
    update release_type
    <set >
      <if test="name != null" >
        name = #{name,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=TINYINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.pttl.mobile.manager.domain.po.ReleaseType" >
    update release_type
    set name = #{name,jdbcType=VARCHAR}
    where id = #{id,jdbcType=TINYINT}
  </update>
</mapper>