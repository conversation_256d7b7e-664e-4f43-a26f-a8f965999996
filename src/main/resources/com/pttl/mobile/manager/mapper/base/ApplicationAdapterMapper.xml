<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pttl.mobile.manager.dao.ApplicationAdapterMapper" >
  <resultMap id="BaseResultMap" type="com.pttl.mobile.manager.domain.po.ApplicationAdapter" >
    <id column="application_id" property="applicationId" jdbcType="VARCHAR" />
    <result column="scope" property="scope" jdbcType="VARCHAR" />
    <result column="manifest" property="manifest" jdbcType="CHAR" />
    <result column="runtime_primary_version" property="runtimePrimaryVersion" jdbcType="VARCHAR" />
    <result column="runtime_version" property="runtimeVersion" jdbcType="VARCHAR" />
    <result column="is_specific_runtime_used" property="isSpecificRuntimeUsed" jdbcType="TINYINT" />
    <result column="last_update" property="lastUpdate" jdbcType="TIMESTAMP" />
    <result column="system_platform" property="systemPlatform" jdbcType="TINYINT" />
    <result column="env" property="env" jdbcType="TINYINT" />
  </resultMap>
  <sql id="Base_Column_List" >
    application_id, scope, manifest, runtime_primary_version, runtime_version, is_specific_runtime_used, 
    last_update, system_platform, env
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from application_adapter
    where application_id = #{applicationId,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from application_adapter
    where application_id = #{applicationId,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.pttl.mobile.manager.domain.po.ApplicationAdapter" >
    insert into application_adapter (application_id, scope, manifest, 
      runtime_primary_version, runtime_version, 
      is_specific_runtime_used, last_update, system_platform, 
      env)
    values (#{applicationId,jdbcType=VARCHAR}, #{scope,jdbcType=VARCHAR}, #{manifest,jdbcType=CHAR}, 
      #{runtimePrimaryVersion,jdbcType=VARCHAR}, #{runtimeVersion,jdbcType=VARCHAR}, 
      #{isSpecificRuntimeUsed,jdbcType=TINYINT}, #{lastUpdate,jdbcType=TIMESTAMP}, ${@com.pttl.mobile.manager.lib.ContextUtil@systemPlatform()},
      ${@com.pttl.mobile.manager.lib.ContextUtil@currentEnv()})
  </insert>
  <insert id="insertSelective" parameterType="com.pttl.mobile.manager.domain.po.ApplicationAdapter" >
    insert into application_adapter
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="applicationId != null" >
        application_id,
      </if>
      <if test="scope != null" >
        scope,
      </if>
      <if test="manifest != null" >
        manifest,
      </if>
      <if test="runtimePrimaryVersion != null" >
        runtime_primary_version,
      </if>
      <if test="runtimeVersion != null" >
        runtime_version,
      </if>
      <if test="isSpecificRuntimeUsed != null" >
        is_specific_runtime_used,
      </if>
      <if test="lastUpdate != null" >
        last_update,
      </if>
      <if test="systemPlatform != null" >
        system_platform,
      </if>
      <if test="env != null" >
        env,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="applicationId != null" >
        #{applicationId,jdbcType=VARCHAR},
      </if>
      <if test="scope != null" >
        #{scope,jdbcType=VARCHAR},
      </if>
      <if test="manifest != null" >
        #{manifest,jdbcType=CHAR},
      </if>
      <if test="runtimePrimaryVersion != null" >
        #{runtimePrimaryVersion,jdbcType=VARCHAR},
      </if>
      <if test="runtimeVersion != null" >
        #{runtimeVersion,jdbcType=VARCHAR},
      </if>
      <if test="isSpecificRuntimeUsed != null" >
        #{isSpecificRuntimeUsed,jdbcType=TINYINT},
      </if>
      <if test="lastUpdate != null" >
        #{lastUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="systemPlatform != null" >
        #{systemPlatform,jdbcType=TINYINT},
      </if>
      <if test="env != null" >
        #{env,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.pttl.mobile.manager.domain.po.ApplicationAdapter" >
    update application_adapter
    <set >
      <if test="scope != null" >
        scope = #{scope,jdbcType=VARCHAR},
      </if>
      <if test="manifest != null" >
        manifest = #{manifest,jdbcType=CHAR},
      </if>
      <if test="runtimePrimaryVersion != null" >
        runtime_primary_version = #{runtimePrimaryVersion,jdbcType=VARCHAR},
      </if>
      <if test="runtimeVersion != null" >
        runtime_version = #{runtimeVersion,jdbcType=VARCHAR},
      </if>
      <if test="isSpecificRuntimeUsed != null" >
        is_specific_runtime_used = #{isSpecificRuntimeUsed,jdbcType=TINYINT},
      </if>
      <if test="lastUpdate != null" >
        last_update = #{lastUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="systemPlatform != null" >
        system_platform = #{systemPlatform,jdbcType=TINYINT},
      </if>
      <if test="env != null" >
        env = #{env,jdbcType=TINYINT},
      </if>
    </set>
    where application_id = #{applicationId,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.pttl.mobile.manager.domain.po.ApplicationAdapter" >
    update application_adapter
    set scope = #{scope,jdbcType=VARCHAR},
      manifest = #{manifest,jdbcType=CHAR},
      runtime_primary_version = #{runtimePrimaryVersion,jdbcType=VARCHAR},
      runtime_version = #{runtimeVersion,jdbcType=VARCHAR},
      is_specific_runtime_used = #{isSpecificRuntimeUsed,jdbcType=TINYINT},
      last_update = #{lastUpdate,jdbcType=TIMESTAMP},
      system_platform = #{systemPlatform,jdbcType=TINYINT},
      env = #{env,jdbcType=TINYINT}
    where application_id = #{applicationId,jdbcType=VARCHAR}
  </update>
</mapper>