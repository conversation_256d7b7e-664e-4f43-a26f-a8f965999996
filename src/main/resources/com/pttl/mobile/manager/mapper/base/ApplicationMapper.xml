<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pttl.mobile.manager.dao.ApplicationMapper" >
  <resultMap id="BaseResultMap" type="com.pttl.mobile.manager.domain.po.Application" >
    <id column="id" property="id" jdbcType="VARCHAR" />
    <result column="application_group_id" property="applicationGroupId" jdbcType="VARCHAR" />
    <result column="name" property="name" jdbcType="VARCHAR" />
    <result column="description" property="description" jdbcType="VARCHAR" />
    <result column="logo_url" property="logoUrl" jdbcType="VARCHAR" />
    <result column="package_url" property="packageUrl" jdbcType="VARCHAR" />
    <result column="package_name" property="packageName" jdbcType="VARCHAR" />
    <result column="address" property="address" jdbcType="VARCHAR" />
    <result column="inner_address" property="innerAddress" jdbcType="VARCHAR" />
    <result column="type" property="type" jdbcType="TINYINT" />
    <result column="application_weight" property="applicationWeight" jdbcType="TINYINT" />
    <result column="is_visible" property="isVisible" jdbcType="TINYINT" />
    <result column="create_date" property="createDate" jdbcType="TIMESTAMP" />
    <result column="last_update" property="lastUpdate" jdbcType="TIMESTAMP" />
    <result column="version" property="version" jdbcType="VARCHAR" />
    <result column="system_platform" property="systemPlatform" jdbcType="TINYINT" />
    <result column="env" property="env" jdbcType="TINYINT" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, application_group_id, name, description, logo_url, package_url, package_name, 
    address, inner_address, type, is_visible, create_date, last_update, version, system_platform,
    env
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select
    <include refid="Base_Column_List" />
    from application
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from application
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.pttl.mobile.manager.domain.po.Application" >
    insert into application (id, application_group_id, name, 
      description, logo_url, package_url, 
      package_name, address, inner_address, 
      type, is_visible, create_date,
      last_update, version, system_platform, env
      )
    values (#{id,jdbcType=VARCHAR}, #{applicationGroupId,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, 
      #{description,jdbcType=VARCHAR}, #{logoUrl,jdbcType=VARCHAR}, #{packageUrl,jdbcType=VARCHAR}, 
      #{packageName,jdbcType=VARCHAR}, #{address,jdbcType=VARCHAR}, #{innerAddress,jdbcType=VARCHAR}, 
      #{type,jdbcType=TINYINT}, #{isVisible,jdbcType=TINYINT}, #{createDate,jdbcType=TIMESTAMP}, 
      #{lastUpdate,jdbcType=TIMESTAMP}, #{version,jdbcType=VARCHAR}, ${@com.pttl.mobile.manager.lib.ContextUtil@systemPlatform()},
      ${@com.pttl.mobile.manager.lib.ContextUtil@currentEnv()}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.pttl.mobile.manager.domain.po.Application" >
    insert into application
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="applicationGroupId != null" >
        application_group_id,
      </if>
      <if test="name != null" >
        name,
      </if>
      <if test="description != null" >
        description,
      </if>
      <if test="logoUrl != null" >
        logo_url,
      </if>
      <if test="packageUrl != null" >
        package_url,
      </if>
      <if test="packageName != null" >
        package_name,
      </if>
      <if test="address != null" >
        address,
      </if>
      <if test="innerAddress != null" >
        inner_address,
      </if>
      <if test="type != null" >
        type,
      </if>
      <if test="isVisible != null" >
        is_visible,
      </if>
      <if test="createDate != null" >
        create_date,
      </if>
      <if test="lastUpdate != null" >
        last_update,
      </if>
      <if test="version != null" >
        version,
      </if>
      <if test="systemPlatform != null" >
        system_platform,
      </if>
      <if test="env != null" >
        env,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="applicationGroupId != null" >
        #{applicationGroupId,jdbcType=VARCHAR},
      </if>
      <if test="name != null" >
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="description != null" >
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="logoUrl != null" >
        #{logoUrl,jdbcType=VARCHAR},
      </if>
      <if test="packageUrl != null" >
        #{packageUrl,jdbcType=VARCHAR},
      </if>
      <if test="packageName != null" >
        #{packageName,jdbcType=VARCHAR},
      </if>
      <if test="address != null" >
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="innerAddress != null" >
        #{innerAddress,jdbcType=VARCHAR},
      </if>
      <if test="type != null" >
        #{type,jdbcType=TINYINT},
      </if>
      <if test="isVisible != null" >
        #{isVisible,jdbcType=TINYINT},
      </if>
      <if test="createDate != null" >
        #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdate != null" >
        #{lastUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="version != null" >
        #{version,jdbcType=VARCHAR},
      </if>
      <if test="systemPlatform != null" >
        #{systemPlatform,jdbcType=TINYINT},
      </if>
      <if test="env != null" >
        #{env,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.pttl.mobile.manager.domain.po.Application" >
    update application
    <set >
      <if test="applicationGroupId != null" >
        application_group_id = #{applicationGroupId,jdbcType=VARCHAR},
      </if>
      <if test="name != null" >
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="description != null" >
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="logoUrl != null" >
        logo_url = #{logoUrl,jdbcType=VARCHAR},
      </if>
      <if test="packageUrl != null" >
        package_url = #{packageUrl,jdbcType=VARCHAR},
      </if>
      <if test="packageName != null" >
        package_name = #{packageName,jdbcType=VARCHAR},
      </if>
      <if test="address != null" >
        address = #{address,jdbcType=VARCHAR},
      </if>
      <if test="innerAddress != null" >
        inner_address = #{innerAddress,jdbcType=VARCHAR},
      </if>
      <if test="type != null" >
        type = #{type,jdbcType=TINYINT},
      </if>
      <if test="isVisible != null" >
        is_visible = #{isVisible,jdbcType=TINYINT},
      </if>
      <if test="createDate != null" >
        create_date = #{createDate,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdate != null" >
        last_update = #{lastUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="version != null" >
        version = #{version,jdbcType=VARCHAR},
      </if>
      <if test="systemPlatform != null" >
        system_platform = #{systemPlatform,jdbcType=TINYINT},
      </if>
      <if test="env != null" >
        env = #{env,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.pttl.mobile.manager.domain.po.Application" >
    update application
    set application_group_id = #{applicationGroupId,jdbcType=VARCHAR},
      name = #{name,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      logo_url = #{logoUrl,jdbcType=VARCHAR},
      package_url = #{packageUrl,jdbcType=VARCHAR},
      package_name = #{packageName,jdbcType=VARCHAR},
      address = #{address,jdbcType=VARCHAR},
      inner_address = #{innerAddress,jdbcType=VARCHAR},
      type = #{type,jdbcType=TINYINT},
      is_visible = #{isVisible,jdbcType=TINYINT},
      create_date = #{createDate,jdbcType=TIMESTAMP},
      last_update = #{lastUpdate,jdbcType=TIMESTAMP},
      version = #{version,jdbcType=VARCHAR},
      system_platform = #{systemPlatform,jdbcType=TINYINT},
      env = #{env,jdbcType=TINYINT}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>