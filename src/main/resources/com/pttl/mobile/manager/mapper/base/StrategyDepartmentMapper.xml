<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pttl.mobile.manager.dao.StrategyDepartmentMapper" >
  <resultMap id="BaseResultMap" type="com.pttl.mobile.manager.domain.po.StrategyDepartment" >
    <id column="id" property="id" jdbcType="VARCHAR" />
    <result column="strategy_id" property="strategyId" jdbcType="VARCHAR" />
    <result column="department_id" property="departmentId" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, strategy_id, department_id
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from strategy_department
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from strategy_department
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.pttl.mobile.manager.domain.po.StrategyDepartment" >
    insert into strategy_department (id, strategy_id, department_id
      )
    values (#{id,jdbcType=VARCHAR}, #{strategyId,jdbcType=VARCHAR}, #{departmentId,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.pttl.mobile.manager.domain.po.StrategyDepartment" >
    insert into strategy_department
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="strategyId != null" >
        strategy_id,
      </if>
      <if test="departmentId != null" >
        department_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="strategyId != null" >
        #{strategyId,jdbcType=VARCHAR},
      </if>
      <if test="departmentId != null" >
        #{departmentId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.pttl.mobile.manager.domain.po.StrategyDepartment" >
    update strategy_department
    <set >
      <if test="strategyId != null" >
        strategy_id = #{strategyId,jdbcType=VARCHAR},
      </if>
      <if test="departmentId != null" >
        department_id = #{departmentId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.pttl.mobile.manager.domain.po.StrategyDepartment" >
    update strategy_department
    set strategy_id = #{strategyId,jdbcType=VARCHAR},
      department_id = #{departmentId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>