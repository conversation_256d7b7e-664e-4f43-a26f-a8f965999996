<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pttl.mobile.manager.dao.ApplicationGroupMapper">
    <select id="isGroupExists" resultType="java.lang.Integer">
        select count(1) from application_group
        where name=#{name}
        and system_platform=${@com.pttl.mobile.manager.lib.ContextUtil@systemPlatform()}
        and env=${@com.pttl.mobile.manager.lib.ContextUtil@currentEnv()}
    </select>
    <select id="getApplicationGroup" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" />
        from application_group
        where system_platform=${@com.pttl.mobile.manager.lib.ContextUtil@systemPlatform()}
        and env=${@com.pttl.mobile.manager.lib.ContextUtil@currentEnv()}
        ORDER BY weight, last_update DESC
    </select>
</mapper>