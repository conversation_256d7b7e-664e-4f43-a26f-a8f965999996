<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pttl.mobile.manager.dao.StrategyUserMapper" >
  <resultMap id="BaseResultMap" type="com.pttl.mobile.manager.domain.po.StrategyUser" >
    <id column="id" property="id" jdbcType="VARCHAR" />
    <result column="strategy_id" property="strategyId" jdbcType="VARCHAR" />
    <result column="user_id" property="userId" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, strategy_id, user_id
  </sql>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.String" >
    select 
    <include refid="Base_Column_List" />
    from strategy_user
    where id = #{id,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String" >
    delete from strategy_user
    where id = #{id,jdbcType=VARCHAR}
  </delete>
  <insert id="insert" parameterType="com.pttl.mobile.manager.domain.po.StrategyUser" >
    insert into strategy_user (id, strategy_id, user_id
      )
    values (#{id,jdbcType=VARCHAR}, #{strategyId,jdbcType=VARCHAR}, #{userId,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.pttl.mobile.manager.domain.po.StrategyUser" >
    insert into strategy_user
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="strategyId != null" >
        strategy_id,
      </if>
      <if test="userId != null" >
        user_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="strategyId != null" >
        #{strategyId,jdbcType=VARCHAR},
      </if>
      <if test="userId != null" >
        #{userId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.pttl.mobile.manager.domain.po.StrategyUser" >
    update strategy_user
    <set >
      <if test="strategyId != null" >
        strategy_id = #{strategyId,jdbcType=VARCHAR},
      </if>
      <if test="userId != null" >
        user_id = #{userId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.pttl.mobile.manager.domain.po.StrategyUser" >
    update strategy_user
    set strategy_id = #{strategyId,jdbcType=VARCHAR},
      user_id = #{userId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=VARCHAR}
  </update>
</mapper>