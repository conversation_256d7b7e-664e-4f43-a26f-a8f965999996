<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.pttl.mobile.manager.dao.ConfigurationMapper">
    <select id="getConfiguration" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from configuration
        where system_platform=${@com.pttl.mobile.manager.lib.ContextUtil@systemPlatform()}
        and env=${@com.pttl.mobile.manager.lib.ContextUtil@currentEnv()}
    </select>
    <select id="getConfigurationByName" resultType="com.pttl.mobile.manager.domain.po.Configuration">
        select
        <include refid="Base_Column_List" />
        from configuration
        where name=#{name}
        and system_platform=${@com.pttl.mobile.manager.lib.ContextUtil@systemPlatform()}
        and env=${@com.pttl.mobile.manager.lib.ContextUtil@currentEnv()}
        LIMIT 1
    </select>
    <insert id="insertOrUpdate" parameterType="com.pttl.mobile.manager.domain.po.Configuration">
        insert into configuration values(
        #{name},
        #{description},
        #{value},
        ${@com.pttl.mobile.manager.lib.ContextUtil@systemPlatform()},
        ${@com.pttl.mobile.manager.lib.ContextUtil@currentEnv()})
        ON DUPLICATE KEY UPDATE
        name = values(name),
        description = values(description),
        value = values(value)
      </insert>
</mapper>