package com.pttl.mobile.manager;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceBuilder;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.sql.DataSource;

/**
 * 主程序启动类类
 *
 * <AUTHOR>
 */
@EnableAsync
@EnableScheduling
@SpringBootApplication(scanBasePackages = {"com.pttl.mobile.manager"})
@EnableTransactionManagement
@EnableCaching
@MapperScan(value = {"com.pttl.mobile.manager.dao",
        "com.pttl.mobile.manager.mobile.ws.dao"})
public class MobileManagerApplication {

    public static void main(String[] args) {
        SpringApplication.run(MobileManagerApplication.class, args);
    }

    @Bean
    @ConfigurationProperties(prefix = "spring.datasource")
    public DataSource dataSource() {
        return DataSourceBuilder.create().build();
    }
}
