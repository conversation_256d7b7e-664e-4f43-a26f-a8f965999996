package com.pttl.mobile.manager.mobile.ws.dao;

import com.pttl.mobile.manager.mobile.ws.entity.OrganizationalStructureInfoDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface OrganizationalStructureInfoMapper {
    /**
     * delete by primary key
     *
     * @param id primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long id);

    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(OrganizationalStructureInfoDO record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(OrganizationalStructureInfoDO record);

    /**
     * select by primary key
     *
     * @param id primary key
     * @return object by primary key
     */
    OrganizationalStructureInfoDO selectByPrimaryKey(Long id);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(OrganizationalStructureInfoDO record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(OrganizationalStructureInfoDO record);

    int updateBatch(List<OrganizationalStructureInfoDO> list);

    int batchInsert(@Param("list") List<OrganizationalStructureInfoDO> list);

    void cleanTable();

    /**
     * 根据根id获取一级部门
     *
     * @param treeRootId
     * @return
     */
    List<OrganizationalStructureInfoDO> listByDeptRootId(@Param("treeRootId") int treeRootId);

    /**
     * 根据部门id和部门名称获取数据
     *
     * @param nameDesc   部门名称
     * @param deptIdList 部门id
     * @return
     */
    List<OrganizationalStructureInfoDO> listByDeptNameAndDeptIdList(@Param("nameDesc") String nameDesc, @Param("list") List<Integer> deptIdList);

    /**
     * 调用函数  根据部门id获取 当前部门id及子部门id
     *
     * @param deptId 部门id
     * @return 当前部门id及子部门id
     */
    List<Integer> getDeptListByDeptId(@Param("deptId") Integer deptId);
}