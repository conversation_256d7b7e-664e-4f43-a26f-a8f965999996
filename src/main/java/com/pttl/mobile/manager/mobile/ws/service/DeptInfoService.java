package com.pttl.mobile.manager.mobile.ws.service;

import com.pttl.mobile.manager.mobile.ws.entity.DeptInfoDO;

import java.util.List;

public interface DeptInfoService {


    int deleteByPrimaryKey(Long id);

    int insert(DeptInfoDO record);

    int insertSelective(DeptInfoDO record);

    DeptInfoDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(DeptInfoDO record);

    int updateByPrimaryKey(DeptInfoDO record);

    int updateBatch(List<DeptInfoDO> list);

    int batchInsert(List<DeptInfoDO> list);

    /**
     * 清空表数据
     */
    void cleanTable();

}
