package com.pttl.mobile.manager.mobile.controller;

import cn.hutool.core.text.CharSequenceUtil;
import com.pttl.mobile.manager.lib.ResponseMessage;
import com.pttl.mobile.manager.mobile.service.InTransitOrderService;
import com.pttl.mobile.manager.mobile.vo.GetLoginTokenVO;
import com.pttl.mobile.manager.mobile.vo.MobileInformBaseVO;
import com.pttl.mobile.manager.util.RSAUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 在途订单登陆相关 控制层
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/4
 */
@Slf4j
@RestController
@RequestMapping("/mobile/inTransit/order")
@Api(tags = "在途订单登陆相关")
public class InTransitOrderController {

    /**
     * 返回信息
     */
    private static final String RETURN_STRING = "{\"code\":%s,\"msg\":\"%s\",\"data\":\"\"}";

    /**
     * 在途挂单服务
     */
    @Resource
    private InTransitOrderService inTransitOrderService;


    @PostMapping("/obtainVcrmSubAccount")
    @ApiOperation(value = "获取VCRM子账号", notes = "移动端 获取VCRM子账号（需要登录VCRM）")
    public String obtainVcrmSubAccount(@RequestBody MobileInformBaseVO cookieVO) {
        if (Objects.isNull(cookieVO) || CharSequenceUtil.isEmpty(cookieVO.getCookie())) {
            return ResponseMessage.error("cookie不能为空").toString();
        }

        // 执行请求
        return inTransitOrderService.requestVcrmSubAccount(cookieVO);
    }


    @PostMapping("/obtainLoginToken")
    @ApiOperation(value = "获取登录token", notes = "移动端 获取登录token")
    public String obtainLoginToken(@RequestBody GetLoginTokenVO tokenVO) {
        // 参数校验
        ResponseMessage<Object> parameterVerification = parameterVerification(tokenVO);
        if (!parameterVerification.isOk()) {
            return String.format(RETURN_STRING, parameterVerification.getCode(), parameterVerification.getMessage());
        }

        // 获取token
        return inTransitOrderService.requestToken(tokenVO);
    }

    /**
     * 参数校验
     *
     * @param tokenVO 参数vo
     * @return 校验结果
     */
    private ResponseMessage<Object> parameterVerification(GetLoginTokenVO tokenVO) {
        if (Objects.isNull(tokenVO)) {
            return ResponseMessage.error("account/password/subAccount不能为空");
        }

        if (CharSequenceUtil.isEmpty(tokenVO.getAccount()) || CharSequenceUtil.isEmpty(tokenVO.getSubAccount())
                || CharSequenceUtil.isEmpty(tokenVO.getPassword())) {
            return ResponseMessage.error("account/password/subAccount不能为空");
        }

        // 解密密码
        RSAUtil rsaUtil = new RSAUtil();
        try {
            tokenVO.setPassword(rsaUtil.decrypt(tokenVO.getPassword()));
        } catch (Exception e) {
            log.error("decryption failure: {}", tokenVO);
            e.printStackTrace();
            return ResponseMessage.error("account decryption failure");
        }

        return ResponseMessage.ok();
    }

    public static void main(String[] args) throws Exception {
        RSAUtil rsaUtil = new RSAUtil();
        String encrypt = rsaUtil.encrypt("ng1#366977");
        System.out.println(encrypt);

    }

}
