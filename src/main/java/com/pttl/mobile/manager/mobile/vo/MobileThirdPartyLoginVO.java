package com.pttl.mobile.manager.mobile.vo;

import com.pttl.mobile.manager.constant.MobileThirdPartyLoginEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 移动端第三方登录vo
 *
 * <AUTHOR>
 * @date 2023/5/26
 **/
@Data
@ApiModel(value = "移动端用户登录")
public class MobileThirdPartyLoginVO {

    /**
     * 用户姓名
     */
    @ApiModelProperty(value = "用户登录名")
    private String userName;

    /**
     * 用户登录密码
     */
    @ApiModelProperty(value = "用户登录密码, 不需要url decoded")
    private String password;

    /**
     * 系统类型
     *
     * @see MobileThirdPartyLoginEnum#BPM
     */
    @ApiModelProperty(value = "系统类型, BPM-1 VCRM-2 通知登陆-3")
    private Integer sysType;

    @ApiModelProperty(value = "平台类型, true为鸿蒙")
    private boolean harmony = false;
}
