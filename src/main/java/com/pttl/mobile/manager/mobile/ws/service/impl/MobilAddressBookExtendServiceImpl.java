package com.pttl.mobile.manager.mobile.ws.service.impl;

import cn.hutool.core.util.StrUtil;
import com.pttl.mobile.manager.constant.ContactsLogSwitchEnum;
import com.pttl.mobile.manager.mobile.ws.dao.MobilAddressBookExtendMapper;
import com.pttl.mobile.manager.mobile.ws.dto.MobilAddressBookExtendDTO;
import com.pttl.mobile.manager.mobile.ws.entity.MobilAddressBookExtendDO;
import com.pttl.mobile.manager.mobile.ws.service.MobilAddressBookExtendService;
import com.pttl.mobile.manager.util.BeanMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Service
public class MobilAddressBookExtendServiceImpl implements MobilAddressBookExtendService {

    @Resource
    private MobilAddressBookExtendMapper mobilAddressBookExtendMapper;

    @Override
    public int deleteByPrimaryKey(Long id) {
        return mobilAddressBookExtendMapper.deleteByPrimaryKey(id);
    }

    @Override
    public int insert(MobilAddressBookExtendDO record) {
        return mobilAddressBookExtendMapper.insert(record);
    }

    @Override
    public int insertSelective(MobilAddressBookExtendDO record) {
        return mobilAddressBookExtendMapper.insertSelective(record);
    }

    @Override
    public MobilAddressBookExtendDO selectByPrimaryKey(Long id) {
        return mobilAddressBookExtendMapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(MobilAddressBookExtendDO record) {
        return mobilAddressBookExtendMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(MobilAddressBookExtendDO record) {
        return mobilAddressBookExtendMapper.updateByPrimaryKey(record);
    }

    @Override
    public int updateBatch(List<MobilAddressBookExtendDO> list) {
        return mobilAddressBookExtendMapper.updateBatch(list);
    }

    @Override
    public int batchInsert(List<MobilAddressBookExtendDO> list) {
        return mobilAddressBookExtendMapper.batchInsert(list);
    }

    @Override
    public MobilAddressBookExtendDO getByEmployeeId(String employeeId) {
        return mobilAddressBookExtendMapper.getByEmployeeId(employeeId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateAddressBookExtend(MobilAddressBookExtendDTO queryVO) {
        MobilAddressBookExtendDO addressBookExtend = getByEmployeeId(queryVO.getPersonInfoId());

        // 不存在则动态新增
        if (Objects.isNull(addressBookExtend)) {
            MobilAddressBookExtendDO addressBookExtendDO = BeanMapper.map(queryVO, MobilAddressBookExtendDO.class);
            insertSelective(addressBookExtendDO);
            return;
        }

        // 存在则动态修改
        if (StrUtil.isNotEmpty(queryVO.getCustomAvatar())) {
            addressBookExtend.setCustomAvatar(queryVO.getCustomAvatar());
        }
        if (StrUtil.isNotEmpty(queryVO.getUserRemark())) {
            addressBookExtend.setUserRemark(queryVO.getUserRemark());
        }
        updateByPrimaryKeySelective(addressBookExtend);
    }

    @Override
    public void updateContactsSwitch(String employeeId) {
        MobilAddressBookExtendDO addressBookExtend = getByEmployeeId(employeeId);

        // 不存在则动态新增
        if (Objects.isNull(addressBookExtend)) {
            MobilAddressBookExtendDO addressBookExtendDO = new MobilAddressBookExtendDO();
            addressBookExtendDO.setPersonInfoId(employeeId);
            addressBookExtendDO.setReservedField1(ContactsLogSwitchEnum.YES.getFlag());
            insertSelective(addressBookExtendDO);
        } else {
            if (ContactsLogSwitchEnum.YES.getFlag().equals(addressBookExtend.getReservedField1())) {
                addressBookExtend.setReservedField1(ContactsLogSwitchEnum.NO.getFlag());
            } else {
                addressBookExtend.setReservedField1(ContactsLogSwitchEnum.YES.getFlag());
            }
            this.updateByPrimaryKeySelective(addressBookExtend);
        }
    }

    @Override
    public boolean getContactsSwitchStatus(String employeeId) {
        MobilAddressBookExtendDO byEmployeeId = this.getByEmployeeId(employeeId);
        if (Objects.isNull(byEmployeeId)) {
            return false;
        }

        return ContactsLogSwitchEnum.YES.getFlag().equals(byEmployeeId.getReservedField1());
    }

}
