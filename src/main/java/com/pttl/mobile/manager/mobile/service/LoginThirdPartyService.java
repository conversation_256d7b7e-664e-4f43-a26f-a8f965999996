package com.pttl.mobile.manager.mobile.service;

import com.pttl.mobile.manager.mobile.vo.*;

import java.util.List;

/**
 * 第三方登陆服务层
 *
 * <AUTHOR>
 * @date 2023/5/29
 **/

public interface LoginThirdPartyService {

    /**
     * BpmCookie信息获取
     *
     * @param partyLoginVO 登陆参数
     * @return cookie列表
     */
    List<String> getBpmCookie(MobileThirdPartyLoginVO partyLoginVO);

    /**
     * VcrmCookie信息获取或通知登录Cookie信息获取
     *
     * @param partyLoginVO 登陆参数
     * @return cookie列表
     */
    List<String> getVcrmCookieOrInformCookie(MobileThirdPartyLoginVO partyLoginVO);

    /**
     * 获取各个系统Cookie信息
     *
     * @param partyLoginVO 登陆参数
     * @return cookie列表
     */
    Object getSysCookieBySysType(MobileThirdPartyLoginVO partyLoginVO);

    /**
     * 绑定极光推送
     *
     * @param bindAuroralPushVO 参数
     * @return 结果
     */
    String bindAuroraPush(BindAuroralPushVO bindAuroralPushVO);

    /**
     * 获取通知类型
     *
     * @param param 参数
     * @return 响应结果
     */
    String getNotificationType(MobileInformBaseVO param);

    /**
     * 获取通知列表
     *
     * @param param 参数
     * @return 响应体
     */
    String getNotificationList(GetNotificationListVO param);

    /**
     * 一键已读
     *
     * @param param 参数
     * @return 响应体
     */
    String oneClickRead(OneClickReadVO param);

    /**
     * 已读
     *
     * @param param 参数
     * @return 响应体
     */
    String processRead(ReadVO param);

    /**
     * BPM红点显示vo
     *
     * @param param 参数
     * @return 响应体
     */
    String bpmIsDisplayedWithRedDots(BpmRedDotsVO param);

    /**
     * 获取通知搜索
     *
     * @param param 参数
     * @return 响应体
     */
    String getNotificationSearch(GetNotificationSearchVO param);
}
