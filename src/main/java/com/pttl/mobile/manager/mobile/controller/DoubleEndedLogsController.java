package com.pttl.mobile.manager.mobile.controller;

import cn.hutool.core.collection.CollUtil;
import com.pttl.mobile.manager.domain.dto.LogDataDTO;
import com.pttl.mobile.manager.domain.entity.MobileLogs;
import com.pttl.mobile.manager.lib.ResponseMessage;
import com.pttl.mobile.manager.mobile.ws.service.MobilAddressBookExtendService;
import com.pttl.mobile.manager.service.FrontLogsService;
import com.pttl.mobile.manager.service.MobileLogsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 移动端和前端日志上报
 *
 * <AUTHOR>
 * @date 2024/5/8
 */
@Slf4j
@RestController
@RequestMapping("/mobile/logs")
@Api(tags = "移动端和前端-日志接口")
public class DoubleEndedLogsController {

    /**
     * 通讯录扩展字段
     */
    @Resource
    private MobilAddressBookExtendService mobilAddressBookExtendService;

    /**
     * 服务对象
     */
    @Autowired
    private MobileLogsService mobileLogsService;

    /**
     * 前端日志服务对象
     */
    @Resource
    private FrontLogsService frontLogsService;

    @ApiOperation(value = "通讯录开关状态", notes = "通讯录开关状态获取")
    @GetMapping("/contactsSwitchStatus")
    public ResponseMessage<Boolean> contactsSwitch(@RequestParam("employeeId") String employeeId) {
        log.info("into contactsSwitchStatus {}...", employeeId);

        return ResponseMessage.ok(mobilAddressBookExtendService.getContactsSwitchStatus(employeeId));
    }


    /**
     * 保存移动端日志
     * @param mobileLogs
     * @return
     */
    @ApiOperation(value = "移动端保存日志", notes = "移动端保存日志")
    @PostMapping("/saveMobileLog")
    public ResponseMessage insertMobileLogs(@RequestBody List<MobileLogs> mobileLogs) {
        log.info("into saveMobileLogs {}", mobileLogs);

        if (CollUtil.isEmpty(mobileLogs)) {
            return ResponseMessage.error("参数不能为空");
        }

        mobileLogsService.saveInBulk(mobileLogs);
        return ResponseMessage.ok();
    }

    @ApiOperation(value = "前端保存日志", notes = "前端保存日志")
    @PostMapping("/saveFrontLog")
    public ResponseMessage insertFrontLogs(@RequestBody List<LogDataDTO> logDataList) {
        log.info("into saveFrontLog {}", logDataList);

        if (CollUtil.isEmpty(logDataList)) {
            return ResponseMessage.error("参数不能为空");
        }

        frontLogsService.savefrontLog(logDataList);
        return ResponseMessage.ok();
    }
}
