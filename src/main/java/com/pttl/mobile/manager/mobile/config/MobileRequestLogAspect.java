package com.pttl.mobile.manager.mobile.config;

import com.alibaba.fastjson.JSON;
import com.pttl.mobile.manager.lib.ResponseMessage;
import com.pttl.mobile.manager.util.AspectSupportUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

import javax.servlet.http.HttpServletRequest;

/**
 * 移动端请求日志point
 *
 * <AUTHOR>
 * @date 2022/11/14
 **/
@Slf4j
@Aspect
@Component
public class MobileRequestLogAspect {

    /**
     * 设置操作日志切入点 记录移动端请求日志 扫描所有controller包下操作
     */
    @Pointcut("execution(* com.pttl.mobile.manager.mobile.controller..*.*(..))")
    public void requestLogPointCut() {
        // not do
    }

    /**
     * 设置操作异常切入点记录异常日志 扫描所有controller包下操作
     */
    @Pointcut("execution(* com.pttl.mobile.manager.mobile.controller..*.*(..))")
    public void requestExceptionLogPointCut() {
        // not do
    }

    @AfterReturning(value = "requestLogPointCut()", returning = "keys")
    public void processMobileRequestLog(JoinPoint joinPoint, Object keys) {
        String methodName = AspectSupportUtil.getMethodName(joinPoint);
        log.info("mobile current method: {}, request success...", methodName);

        // 日志body 信息
        logBodyInfo();

        // 方便查询这里记录日志 返回结果
        log.info("mobile current response params: {}", JSON.toJSONString(keys));

        // 日志结束
        log.info("mobile current method: {}, request success  finish...", methodName);
    }

    @AfterThrowing(value = "requestExceptionLogPointCut()", throwing = "e")
    public void processMobileRequestExceptionLog(JoinPoint joinPoint, Throwable e) {
        String methodName = AspectSupportUtil.getMethodName(joinPoint);
        log.info("mobile current method: {}, request exception...", methodName);

        // 日志body 信息
        logBodyInfo();

        // 异常信息
        String stackTraceString = AspectSupportUtil.stackTraceToString(e.getClass().getName(), e.getMessage(), e.getStackTrace());
        // 方便查询这里记录日志
        log.info("mobile current exception: {}, {}", e.getClass().getName(), stackTraceString);

        // 异常时 捕捉的默认返回异常信息
        String errorMag = JSON.toJSONString(ResponseMessage.error("系统异常 请稍后再试"));
        // 方便查询这里记录日志
        log.info("mobile current response params: {}", errorMag);

        // 日志结束
        log.info("mobile current  method: {}, request exception finish...", methodName);
    }

    /**
     * 日志body 信息
     */
    private void logBodyInfo() {
        // 获取RequestAttributes
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        // 从获取RequestAttributes中获取HttpServletRequest的信息
        HttpServletRequest request = (HttpServletRequest) requestAttributes
                .resolveReference(RequestAttributes.REFERENCE_REQUEST);

        // 方便查询这里记录日志
        log.info("mobile current request url: {}, params: {}", request.getRequestURI(), AspectSupportUtil.getRequestBody(request));

    }

}
