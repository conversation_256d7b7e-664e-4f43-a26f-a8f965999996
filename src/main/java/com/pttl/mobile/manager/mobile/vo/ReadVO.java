package com.pttl.mobile.manager.mobile.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 一键已读
 *
 * <AUTHOR>
 * @date 2023/6/8
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "移动端 一键已读 vo")
public class ReadVO extends MobileInformBaseVO {

    @ApiModelProperty(value = "获取通知列表noticeUserId")
    private Integer noticeUserId;
}
