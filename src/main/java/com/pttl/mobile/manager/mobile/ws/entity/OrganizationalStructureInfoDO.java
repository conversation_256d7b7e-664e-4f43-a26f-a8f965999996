package com.pttl.mobile.manager.mobile.ws.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 通讯录-组织架构信息
 *
 * <AUTHOR>
 */
@ApiModel(value = "通讯录-组织架构信息")
@Data
public class OrganizationalStructureInfoDO implements Serializable {

    /**
     * 树根节点 id
     */
    public static final int TREE_ROOT_ID = 99999;

    /**
     * 树二级节点
     */
    public static final int TREE_SECOND_LEVEL = 10000;

    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 上级部门简称
     */
    @ApiModelProperty(value = "上级部门简称")
    private String superiorDeptAbbreviation;

    /**
     * 上级部门ID
     */
    @ApiModelProperty(value = "上级部门ID")
    private Integer partDeptIdChn;

    /**
     * 部门ID
     */
    @ApiModelProperty(value = "部门ID")
    private Integer deptId;

    @ApiModelProperty(value = "")
    private Integer rowNumber;

    /**
     * 部门简称
     */
    @ApiModelProperty(value = "部门简称")
    private String deptAbbreviation;

    private static final long serialVersionUID = 1L;
}