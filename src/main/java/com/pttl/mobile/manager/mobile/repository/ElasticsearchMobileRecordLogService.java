package com.pttl.mobile.manager.mobile.repository;

import com.pttl.mobile.manager.mobile.document.MobileRecordLogDocument;

import java.io.IOException;
import java.util.List;

/**
 * 移动端 es service
 *
 * <AUTHOR>
 * @date 2022/11/8
 **/

public interface ElasticsearchMobileRecordLogService {

    /**
     * 删除索引(删表)
     *
     * @param index 索引
     * @return 是否成功
     * @throws IOException 异常
     */
    Boolean deleteIndex(String index) throws IOException;

    /**
     * 创建索引(建表)
     *
     * @param index                所以名称
     * @param propertiesJsonString es index properties json string
     * @return 1
     * @throws IOException io
     */
    boolean createIndex(String index, String propertiesJsonString) throws IOException;

    /**
     * 创建文档(插入数据) -- 这里使用了异步的方式
     *
     * @param document doc对象
     * @throws IOException 异常
     */
    void createDocument(MobileRecordLogDocument document) throws IOException;

    /**
     * 批量创建文档
     *
     * @param documents doc对象
     * @return 是否成功
     * @throws IOException 异常
     */
    Boolean bulkCreateDocument(List<MobileRecordLogDocument> documents) throws IOException;

    /**
     * 查看文档
     *
     * @param id id
     * @return doc对象
     * @throws IOException 异常
     */
    MobileRecordLogDocument getDocument(String id) throws IOException;

    /**
     * 更新文档
     *
     * @param document doc对象
     * @return 是否成功
     * @throws IOException 异常
     */
    Boolean updateDocument(MobileRecordLogDocument document) throws IOException;

    /**
     * 删除文档
     *
     * @param id doc id
     * @return 名称
     * @throws IOException 异常
     */
    String deleteDocument(String id) throws IOException;
}
