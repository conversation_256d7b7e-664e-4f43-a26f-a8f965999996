package com.pttl.mobile.manager.mobile.document;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 移动端日志记录 Document
 *
 * <AUTHOR>
 * @date 2022/11/8
 **/

@Data
public class MobileRecordLogDocument implements Serializable {

    /**
     * es索引名称
     */
    public static final String INDEX_NAME = "mobile-record-log";

    private static final long serialVersionUID = 6783091835941518557L;

    /**
     * es id
     */
    @JsonIgnore
    private String esId;

    /**
     * app版本
     */
    private String appVersion;

    /**
     * 描述
     */
    private String description;

    /**
     * 登陆名称
     */
    private String loginName;

    /**
     * 登陆时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date loginTime;

    /**
     * iPhone14,2-15.4.1 系统版本
     */
    private String model;

    /**
     * 模块
     */
    private String module;

    /**
     * 发生时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date occurTime;

    /**
     * 平台 在线商城/在线办公
     */
    private String platform;

    /**
     * 手机系统 Android/IOS
     */
    private String source;

    /**
     * 子模块
     */
    private String subModule;

    /**
     * 系统登陆名称
     */
    private String systemLoginName;

    /**
     * 使用次数
     */
    private Integer timeUsed;

    /**
     * 类型 Info/Error/success
     */
    private String type;

    private String vpnLoginName;
}
