package com.pttl.mobile.manager.mobile.ws.task;

import com.pttl.mobile.manager.mobile.ws.service.OrganizationalStructureInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 组织架构信息
 * 自动请求数据接口 并完成解析解密后入库
 *
 * <AUTHOR>
 * @date 2022/12/30
 **/
@Slf4j
@Component
@ConditionalOnProperty(prefix = "schedule.organizational-structure", name = "enabled", havingValue = "true")
public class RequestDataInterfaceAndStoreOfOrganizationalStructureTask {

    /**
     * 组织架构 服务
     */
    @Resource
    private OrganizationalStructureInfoService organizationalStructureInfoService;

    @Scheduled(cron = "${schedule.organizational-structure.cron}")
    public void requestDataToBeParsedAndStored() {
        log.info("start RequestDataInterfaceAndStoreOfOrganizationalStructureTask ...");

        organizationalStructureInfoService.automaticSynchronization2Database();

        log.info("RequestDataInterfaceAndStoreOfOrganizationalStructureTask success ...");
    }

}
