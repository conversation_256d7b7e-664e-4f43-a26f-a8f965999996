package com.pttl.mobile.manager.mobile.ws.util;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.pttl.mobile.manager.mobile.exception.ErrorMessageException;
import com.pttl.mobile.manager.mobile.dto.DateRange;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 移动端上报的日志 日志查询时间范围工具
 * 只能查询近三个月的, 根据给定的开始时间/结束时间,
 * 返回在时间范围内的每个月的开始时间和结束时间集合
 *
 * <AUTHOR>
 * @date 2023/11/16
 */
public class MonthlyDateRangeCalculator {

    public static List<DateRange> calculateMonthlyRanges(Date startDate, Date endDate) {
        List<DateRange> monthlyRanges = new ArrayList<>();

        Calendar currentCalendar = Calendar.getInstance();
        currentCalendar.setTime(startDate);

        Calendar endCalendar = Calendar.getInstance();
        endCalendar.setTime(endDate);

        // 确保查询范围在最近三个月内
        Calendar threeMonthsAgo = Calendar.getInstance();
        threeMonthsAgo.add(Calendar.MONTH, -3);
        if (currentCalendar.before(threeMonthsAgo)) {
            throw new ErrorMessageException("查询范围不能超过三个月");
        }

        while (!currentCalendar.after(endCalendar)) {
            Calendar startOfMonth = getStartOfMonth(currentCalendar);
            Calendar endOfMonth = getEndOfMonth(currentCalendar);

            Date rangeStartDate = (startDate.before(startOfMonth.getTime())) ? startOfMonth.getTime() : startDate;
            Date rangeEndDate = (endDate.before(endOfMonth.getTime())) ? endDate : endOfMonth.getTime();

            monthlyRanges.add(new DateRange(formatDate(rangeStartDate), rangeStartDate, rangeEndDate));

            currentCalendar.setTime(startOfMonth.getTime());
            currentCalendar.add(Calendar.MONTH, 1);
        }

        return monthlyRanges;
    }

    private static Calendar getStartOfMonth(Calendar calendar) {
        Calendar startOfMonth = (Calendar) calendar.clone();
        startOfMonth.set(Calendar.DAY_OF_MONTH, 1);
        setToBeginningOfDay(startOfMonth);
        return startOfMonth;
    }

    private static Calendar getEndOfMonth(Calendar calendar) {
        Calendar endOfMonth = (Calendar) calendar.clone();
        endOfMonth.set(Calendar.DAY_OF_MONTH, endOfMonth.getActualMaximum(Calendar.DAY_OF_MONTH));
        setToEndOfDay(endOfMonth);
        return endOfMonth;
    }

    private static void setToBeginningOfDay(Calendar calendar) {
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
    }

    private static void setToEndOfDay(Calendar calendar) {
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
    }


    /**
     * 格式化时间(因使用SimpleDateFormat格式化不符合, 区别: 2023_09与2023_9)
     *
     * @param date 时间
     * @return 例如: 2023_9
     */
    public static String formatDate(Date date) {
        //获得年的部分
        int year = DateUtil.year(date);
        //获得月份，从0开始计数
        int month = DateUtil.month(date) + 1;

        return year + "_" + month;
    }

    public static void main(String[] args) {
        Date endDate = new Date();
        // 一一天
        Date startDate = DateUtil.offsetDay(endDate, -1).toJdkDate();

        // 一周
        Date startDate1 = DateUtil.offsetDay(endDate, -6).toJdkDate();
        // 一个月
        Date startDate2 = DateUtil.offsetDay(endDate, -30).toJdkDate();
        // 三个月
        Date startDate3 = DateUtil.offsetMonth(endDate, -2).toJdkDate();
        long between = DateUtil.between(startDate, endDate, DateUnit.DAY);
        System.out.println(DateUtil.between(startDate3, endDate, DateUnit.DAY));
        System.out.println(between);

        if (startDate.after(endDate)) {
            System.out.println("时间不合规, 开始时间 > 结束时间");
            return;
        }

        List<DateRange> monthlyRanges = calculateMonthlyRanges(startDate3, endDate);

        for (DateRange range : monthlyRanges) {
            System.out.println(range);
        }
    }
}


