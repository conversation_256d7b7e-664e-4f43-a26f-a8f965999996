package com.pttl.mobile.manager.mobile.ws.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.pttl.mobile.manager.config.ApplicationDefinedConfiguration;
import com.pttl.mobile.manager.constant.ContactsLogSwitchEnum;
import com.pttl.mobile.manager.mobile.dto.PersonInfoDTO;
import com.pttl.mobile.manager.mobile.dto.PersonInfoOfQueryDTO;
import com.pttl.mobile.manager.mobile.ws.constant.SoapConstants;
import com.pttl.mobile.manager.mobile.ws.dao.PersonInfoMapper;
import com.pttl.mobile.manager.mobile.ws.dto.PersonnelListInfoDTO;
import com.pttl.mobile.manager.mobile.ws.entity.MobilAddressBookExtendDO;
import com.pttl.mobile.manager.mobile.ws.entity.PersonInfoDO;
import com.pttl.mobile.manager.mobile.ws.service.DeptListInfoService;
import com.pttl.mobile.manager.mobile.ws.service.MobilAddressBookExtendService;
import com.pttl.mobile.manager.mobile.ws.service.PersonInfoService;
import com.pttl.mobile.manager.mobile.ws.util.FileScanUtil;
import com.pttl.mobile.manager.util.BeanMapper;
import com.pttl.mobile.manager.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;


/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class PersonInfoServiceImpl implements PersonInfoService {

    /**
     * 数据抽取类
     */
    @Resource
    private DeptListInfoService deptListInfoService;

    @Resource
    private PersonInfoMapper personInfoMapper;

    /**
     * 动态配置
     */
    @Resource
    private ApplicationDefinedConfiguration applicationDefinedConfiguration;

    /**
     * 通讯录扩展字段
     */
    @Resource
    private MobilAddressBookExtendService mobilAddressBookExtendService;


    @Override
    public int deleteByPrimaryKey(Long id) {
        return personInfoMapper.deleteByPrimaryKey(id);
    }

    @Override
    public int insert(PersonInfoDO record) {
        return personInfoMapper.insert(record);
    }

    @Override
    public int insertSelective(PersonInfoDO record) {
        return personInfoMapper.insertSelective(record);
    }

    @Override
    public PersonInfoDO selectByPrimaryKey(Long id) {
        return personInfoMapper.selectByPrimaryKey(id);
    }

    @Override
    public PersonInfoDO leadershipDesensitizationByPrimaryKey(Long id) {
        return personInfoMapper.leadershipDesensitizationByPrimaryKey(id);
    }

    @Override
    public PersonInfoDO selectByEmployeeId(String employeeId) {
        return personInfoMapper.selectByEmployeeId(employeeId);
    }

    @Override
    public Integer countByDeptIds(List<Integer> deptIds) {

        return personInfoMapper.countByDeptIds(deptIds);
    }

    @Override
    public PageInfo<PersonInfoDTO> listByNameAndDeptId(PersonInfoOfQueryDTO queryVO) {
        PageMethod.startPage(queryVO.getPageNum(), queryVO.getPageSize());
        List<PersonInfoDTO> personInfoList = personInfoMapper.listByNameAndDeptId(queryVO);

        if (CollUtil.isNotEmpty(personInfoList)) {
            for (PersonInfoDTO personInfoDTO : personInfoList) {
                MobilAddressBookExtendDO addressBookExtendDO = mobilAddressBookExtendService.getByEmployeeId(personInfoDTO.getEmployeeId());

                // 处理通讯录日志开关
                processContactsLogSwitch(personInfoDTO, addressBookExtendDO);

                // 优先使用自定义的头像
                String imgUrl;
                if (Objects.nonNull(addressBookExtendDO) && StrUtil.isNotBlank(addressBookExtendDO.getCustomAvatar())) {
                    imgUrl = StringUtil.getApplicationIconUr(addressBookExtendDO.getCustomAvatar(),
                            applicationDefinedConfiguration.getSystemManager().getApplicationIconUrlPrefix());
                    personInfoDTO.setUserRemark(addressBookExtendDO.getUserRemark());
                    personInfoDTO.setHeadPicture(imgUrl);
                    continue;
                }

                // 不存在 使用默认
                imgUrl = applicationDefinedConfiguration.getMobileAddressBook().getHeadPictureVisitPre() +
                        personInfoDTO.getEmployeeId() + FileScanUtil.SUFFIX_PNG;
                personInfoDTO.setHeadPicture(imgUrl);
            }
        }

        return new PageInfo<>(personInfoList);
    }

    /**
     * 处理通讯录日志开关
     * @param personInfoDTO 人员信息
     * @param addressBookExtendDO 通讯录扩展信息
     */
    private void processContactsLogSwitch(PersonInfoDTO personInfoDTO, MobilAddressBookExtendDO addressBookExtendDO) {
        if (Objects.nonNull(addressBookExtendDO) && StrUtil.isNotBlank(addressBookExtendDO.getReservedField1())) {
            personInfoDTO.setContactsLogSwitch(ContactsLogSwitchEnum.getCodeByFlag(addressBookExtendDO.getReservedField1()));
        } else {
            personInfoDTO.setContactsLogSwitch(ContactsLogSwitchEnum.NO.getCode());
        }
    }

    @Override
    public List<PersonInfoDO> listByDeptId(Integer deptId) {
        return personInfoMapper.listByDeptId(deptId);
    }

    @Override
    public int updateByPrimaryKeySelective(PersonInfoDO record) {
        return personInfoMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(PersonInfoDO record) {
        return personInfoMapper.updateByPrimaryKey(record);
    }

    @Override
    public int updateBatch(List<PersonInfoDO> list) {
        return personInfoMapper.updateBatch(list);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchInsert(List<PersonInfoDO> list) {
        return personInfoMapper.batchInsert(list);
    }

    @Transactional(rollbackFor = Exception.class)
    void cleanTable() {
        personInfoMapper.cleanTable();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void automaticSynchronization2Database() {
        // 获取数据
        List<PersonnelListInfoDTO> personnelListInfoList = deptListInfoService.
                dataExtraction(SoapConstants.PARAMETER_HPS_INF_NAME_PER, PersonnelListInfoDTO.class);

        if (CollUtil.isEmpty(personnelListInfoList)) {
            log.info("web service api PersonnelListInfo is null, not into database...");
            return;
        }

        // 入库
        insert2Database(personnelListInfoList);
    }

    @Override
    public List<PersonInfoDO> listByNameAndDeptIdList(String nameDesc, List<Integer> deptIdList) {
        return personInfoMapper.listByNameAndDeptIdList(nameDesc, deptIdList);
    }

    /**
     * 入库
     *
     * @param personnelListInfoList
     */
    private void insert2Database(List<PersonnelListInfoDTO> personnelListInfoList) {
        // 清库
        this.cleanTable();

        // 数据入库 分批入库
        List<PersonInfoDO> dataList = bean2Bean(personnelListInfoList);

        int part = (dataList.size() - 1) / SoapConstants.BATCH_NUMBER;

        for (int i = 0; i <= part; i++) {
            // 求每一个批次起始位置
            int fromIndex = i * SoapConstants.BATCH_NUMBER;
            int toIndex = (i + 1) * SoapConstants.BATCH_NUMBER;
            // 若是是最后一个批次，则不能越界
            if (i == part) {
                toIndex = dataList.size();
            }
            // 截取批次长度的list
            List<PersonInfoDO> partList = dataList.subList(fromIndex, toIndex);

            // 入库
            this.batchInsert(partList);
        }

        // 处理完成清除缓存
        dataList.clear();
    }

    /**
     * bean 转换
     *
     * @param personnelListInfoList
     * @return
     */
    private List<PersonInfoDO> bean2Bean(List<PersonnelListInfoDTO> personnelListInfoList) {
        List<PersonInfoDO> dataList = new ArrayList<>();
        for (PersonnelListInfoDTO personnelListInfoDTO : personnelListInfoList) {
            PersonInfoDO personInfoDO = BeanMapper.map(personnelListInfoDTO, PersonInfoDO.class);
            if (StrUtil.isNotEmpty(personnelListInfoDTO.getDeptId())) {
                personInfoDO.setDeptId(Integer.valueOf(personnelListInfoDTO.getDeptId()));
            }
            dataList.add(personInfoDO);
        }

        return dataList;
    }
}
