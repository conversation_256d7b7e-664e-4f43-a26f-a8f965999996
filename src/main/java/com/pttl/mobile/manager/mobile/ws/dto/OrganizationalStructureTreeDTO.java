package com.pttl.mobile.manager.mobile.ws.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 组织架构 树
 *
 * <AUTHOR>
 * @date 2023/1/5
 **/
@Data
@ApiModel(value = "ws 组织架构树实体")
public class OrganizationalStructureTreeDTO {

    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 上级部门简称
     */
    @ApiModelProperty(value = "上级部门简称")
    private String superiorDeptAbbreviation;

    /**
     * 上级部门ID
     */
    @ApiModelProperty(value = "上级部门ID")
    private Integer partDeptIdChn;

    /**
     * 部门ID
     */
    @ApiModelProperty(value = "部门ID")
    private Integer deptId;

    /**
     * 部门简称
     */
    @ApiModelProperty(value = "部门简称")
    private String deptAbbreviation;


    /**
     * 当前部门的子部门
     */
    @ApiModelProperty(value = "当前部门的子部门")
    private List<OrganizationalStructureTreeDTO> childDeptList;
}
