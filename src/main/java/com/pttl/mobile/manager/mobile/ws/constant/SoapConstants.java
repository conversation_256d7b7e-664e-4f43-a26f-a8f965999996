package com.pttl.mobile.manager.mobile.ws.constant;

/**
 * soap定义使用常量
 *
 * <AUTHOR>
 * @date 2022/12/29
 **/

public class SoapConstants {

    private SoapConstants() {
    }

    /**
     * 请求响应 状态node name
     */
    public static final String NODE_NAME_HPS_INF_RESP_STATUS = "HPS_INF_RESP_STATUS";

    /**
     * 请求响应 状态 正常值 其他值则认为失败
     */
    public static final String HPS_INF_RESP_STATUS_VALUE = "Sucess";

    /**
     * 请求响应 状态 正常值时 数据node name
     */
    public static final String NODE_NAME_HPS_INF_RESP_DATA = "HPS_INF_RESP_DATA";

    /**
     * soap 请求参数 基础值
     */
    public static final String REQUIRED_PARAMETER_BASE_XML =
            "<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:sch=\"http://xmlns.oracle.com/Enterprise/Tools/schemas\">" +
                    "    <soapenv:Header/>" +
                    "   <soapenv:Body>" +
                    "      <InterfaceRequest>" +
                    "         <sch:HPS_INF_ID>%s</sch:HPS_INF_ID>" +
                    "         <sch:HPS_INF_APP>%s</sch:HPS_INF_APP>" +
                    "         <sch:HPS_INF_NAME>%s</sch:HPS_INF_NAME>" +
                    "         <sch:HPS_INF_ACTION>%s</sch:HPS_INF_ACTION>" +
                    "      </InterfaceRequest>" +
                    "   </soapenv:Body>" +
                    "</soapenv:Envelope>";


    /**
     * 部门信息
     */
    public static final String PARAMETER_HPS_INF_NAME_DEPT = "HPS_INT_DEPT_QRY";

    /**
     * 部门信息 redis 前缀
     */
    public static final String DEPT_INFO_PREFIX = "dept:info:%s";

    /**
     * 组织架构信息 redis 前缀
     */
    public static final String DEPT_TREE_INFO_PREFIX = "dept:tree:info:%s";

    /**
     * 人员信息
     */
    public static final String PARAMETER_HPS_INF_NAME_PER = "HPS_INT_PER_QRY";

    /**
     * 组织架构信息
     */
    public static final String PARAMETER_HPS_INF_NAME_TREE = "HPS_INT_TREE_QRY";

    /**
     * 查询请求参数 action 值  select
     */
    public static final String PARAMETER_HPS_INF_ACTION_SELECT = "SELECT";


    /**
     * 批次数量
     */
    public static final int BATCH_NUMBER = 500;


    /**
     * 当前部门的id以及所有子部门id集合 key
     */
    public static final String CHILD_DEPT_ID_KEY_PREFIX = "dept:child:id:%s";

    /**
     * 头像id key
     */
    public static final String HEAD_PICTURE_ID_KEY_PREFIX = "head:picture:id:%s";
}
