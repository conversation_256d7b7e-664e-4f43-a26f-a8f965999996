package com.pttl.mobile.manager.mobile.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * BpmCookie信息
 *
 * <AUTHOR>
 * @date 2023/5/30
 **/
@Data
@EqualsAndHashCode
@ApiModel(value = "BpmCookie信息结果dto")
public class BpmCookieInfoDTO {

    /**
     * BpmCookie list
     */
    @ApiModelProperty(value = "BpmCookie list")
    private List<String> cookieList;

    /**
     * 是否有rpa权限
     */
    @ApiModelProperty(value = "是否有rpa权限")
    private Boolean hasRpaAuthority;

    @ApiModelProperty(value = "是否有 人员项目管理 权限")
    private Boolean hasMenuNameAuthority;

    @ApiModelProperty(value = "是否有 RPA流程执行查询 权限")
    private Boolean hasRpaProcessExecutionPermission;
}
