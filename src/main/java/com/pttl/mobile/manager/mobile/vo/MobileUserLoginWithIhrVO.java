package com.pttl.mobile.manager.mobile.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 移动端用户登录VO（支持IHR token）
 *
 * <AUTHOR>
 * @date 2025/01/08
 **/
@Data
@ApiModel(value = "移动端用户登录（支持IHR token）")
public class MobileUserLoginWithIhrVO {

    /**
     * 用户姓名
     */
    @ApiModelProperty(value = "用户登录名, 使用UTF-8编码的Base64进行编码")
    private String userName;

    /**
     * 用户登录密码
     */
    @ApiModelProperty(value = "用户登录密码, 使用RSA加密方式进行加密")
    private String password;

    /**
     * IHR登录token（可选）
     */
    @ApiModelProperty(value = "IHR登录token，如果提供则优先使用IHR token验证")
    private String ihrToken;
}
