package com.pttl.mobile.manager.mobile.util;

import cn.hutool.core.util.StrUtil;
import cn.jiguang.common.utils.StringUtils;
import com.alibaba.fastjson.JSON;
import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTDecodeException;
import com.auth0.jwt.exceptions.JWTVerificationException;
import com.auth0.jwt.interfaces.JWTVerifier;
import com.pttl.mobile.manager.config.ApplicationDefinedConfiguration;
import com.pttl.mobile.manager.constant.CommonConstants;
import com.pttl.mobile.manager.mobile.dto.UserToken;
import com.pttl.mobile.manager.mobile.exception.ErrorMessageException;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * jetUtil
 *
 * <AUTHOR>
 * @date 2022/10/21
 **/
@Component
public class JwtUtil {

    /**
     * 动态配置
     */
    @Resource
    private ApplicationDefinedConfiguration applicationDefinedConfiguration;

    /**
     * redis客户端
     */
    @Resource
    private StringRedisTemplate stringRedisTemplate;


    /**
     * 创建token
     *
     * @param userToken
     * @return
     */
    public String createToken(UserToken userToken) {

        // 序列化Token对象到Redis
        stringRedisTemplate.opsForValue().set(String.format(CommonConstants.MOBILE_USER_TOKEN_KEY, userToken.getId()),
                JSON.toJSONString(userToken), applicationDefinedConfiguration.getMobileToken().getTokenExpirationTime(),
                TimeUnit.MINUTES);

        return JWT.create()
                // 把用户的id写入到token
                .withClaim("id", userToken.getUserId())
                .withIssuedAt(userToken.getIssuedAt())
                // .withExpiresAt(new Date(expiresAt.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()))
                // 使用生成的tokenId作为jwt的id
                .withJWTId(userToken.getId())
                .sign(Algorithm.HMAC256(applicationDefinedConfiguration.getMobileToken().getJwtKey()));
    }

    /**
     * 验证token合法性 成功返回token
     */
    public UserToken verify(String token) throws JWTVerificationException, ErrorMessageException {
        if (StringUtils.isEmpty(token)) {
            throw new ErrorMessageException("token不能为空");
        }

        //获取登录用户真正的密码假如数据库查出来的是123456
        JWTVerifier build = JWT.require(Algorithm.HMAC256(applicationDefinedConfiguration.getMobileToken().getJwtKey())).build();
        // 校验token是否合法
        build.verify(token);

        String userTokenInfo = stringRedisTemplate.opsForValue().get(String.format(CommonConstants.MOBILE_USER_TOKEN_KEY,
                JWT.decode(token).getId()));

        // 无效token
        if (StrUtil.isEmpty(userTokenInfo)) {
            throw new ErrorMessageException("mobile invalid token");
        }

        // 校验通过跟新
        stringRedisTemplate.expire(String.format(CommonConstants.MOBILE_USER_TOKEN_KEY,
                        JWT.decode(token).getId()), applicationDefinedConfiguration.getMobileToken().getTokenExpirationTime(),
                TimeUnit.MINUTES);

        return JSON.parseObject(userTokenInfo, UserToken.class);
    }

    /**
     * 根据token 和 key 获取token中的数据信息
     *
     * @param token
     * @param key
     * @return
     */
    public static String getAudience(String token, String key) {
        String audience;
        try {
            audience = JWT.decode(token).getClaim(key).toString();
        } catch (JWTDecodeException j) {
            //这里是token解析失败
            throw new ErrorMessageException("token解析失败");
        }
        return audience;
    }

    /**
     * 根据token获取redis key
     *
     * @param token
     * @return
     */
    public static String getRedisTokenId(String token) {
        String audience;
        try {
            audience = JWT.decode(token).getId();
        } catch (JWTDecodeException j) {
            //这里是token解析失败
            throw new ErrorMessageException("token解析失败");
        }
        return audience;
    }

}
