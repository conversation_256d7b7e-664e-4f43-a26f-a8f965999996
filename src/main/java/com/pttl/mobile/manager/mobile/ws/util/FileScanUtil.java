package com.pttl.mobile.manager.mobile.ws.util;

import cn.hutool.core.collection.CollUtil;

import java.io.File;
import java.util.List;
import java.util.Objects;

/**
 * 文件扫描
 *
 * <AUTHOR>
 * @date 2023/1/6
 **/

public class FileScanUtil {

    /**
     * 要扫描的文件后缀
     */
    public static final String SUFFIX_PNG = ".png";

    public static void main(String[] args) {
        List<String> list = printFiles(new File("D:\\doc\\manager\\tongxunlu"));
        System.out.println(list);
    }

    /**
     * 获取文件夹中png图片
     *
     * @param dir 路径
     * @return pngIdList
     */
    public static List<String> printFiles(File dir) {
        List<String> pngIdList = CollUtil.newArrayList();
        if (dir.isDirectory()) {
            File[] next = dir.listFiles();
            for (int i = 0; i < Objects.requireNonNull(next).length; i++) {
                String name = next[i].getName();
                if (name.endsWith(SUFFIX_PNG)) {
                    String substring = name.substring(0, name.length() - 4);
                    pngIdList.add(substring);
                }
            }
        }

        return pngIdList;
    }


}
