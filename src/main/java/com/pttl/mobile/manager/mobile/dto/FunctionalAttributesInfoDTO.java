package com.pttl.mobile.manager.mobile.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 查询结果vo
 *
 * <AUTHOR>
 * @date 2022/10/25
 **/
@Data
@EqualsAndHashCode
@ApiModel(value = "属性配置-查询结果dto")
public class FunctionalAttributesInfoDTO {

    /**
     * 功能属性, 例:消息推送：http://mpttl.com
     */
    @ApiModelProperty(value = "功能属性, 例:消息推送：http://mpttl.com")
    private String functionalAttributes;

    /**
     * 属性值
     */
    @ApiModelProperty(value = "属性值")
    private String attributeValue;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
}
