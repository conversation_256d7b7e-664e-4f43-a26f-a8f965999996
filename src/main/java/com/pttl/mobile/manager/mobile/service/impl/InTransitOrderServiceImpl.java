package com.pttl.mobile.manager.mobile.service.impl;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.http.ContentType;
import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.pttl.mobile.manager.config.ApplicationDefinedConfiguration;
import com.pttl.mobile.manager.mobile.service.InTransitOrderService;
import com.pttl.mobile.manager.mobile.vo.GetLoginTokenVO;
import com.pttl.mobile.manager.mobile.vo.GetTokenVO;
import com.pttl.mobile.manager.mobile.vo.GetUuidVO;
import com.pttl.mobile.manager.mobile.vo.MobileInformBaseVO;
import com.pttl.mobile.manager.mobile.ws.util.SSLUtils;
import com.pttl.mobile.manager.util.HttpClientUtil;
import com.pttl.mobile.manager.util.SM2Utils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * 在途挂单服务实现
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/4
 */

@Slf4j
@Service
public class InTransitOrderServiceImpl implements InTransitOrderService {

    /**
     * 系统配置参数
     */
    @Resource
    private ApplicationDefinedConfiguration applicationDefinedConfiguration;

    /**
     * 字段名称
     */
    private static final String FIELD_NAME_CODE = "code";
    private static final String FIELD_NAME_DATA = "data";
    private static final String FIELD_NAME_KEY = "key";

    /**
     * 默认成功状态码
     */
    private static final int DEFAULT_STATUS_CODE_SUCCESS = 200;


    @Override
    public String requestToken(GetLoginTokenVO tokenVO) {
        // 配置参数
        ApplicationDefinedConfiguration.InTransitOrder inTransitOrder = applicationDefinedConfiguration.getInTransitOrder();

        // 需要三步 获取中台key
        String key = getKey(inTransitOrder.getMiddleGroundKeyUrlAddress());

        // 获取UUID
        String uuid = getUuid(inTransitOrder.getUuidUrlAddress(), key, tokenVO);

        // 获取登录token
        return getToken(inTransitOrder.getLoginTokenUrlAddress(), uuid, tokenVO.getSubAccount());

    }


    /**
     * 获取登录token
     *
     * @param loginTokenUrlAddress 请求地址
     * @param uuid                 uuid
     * @param subAccount           子账号信息
     * @return token信息对象字符串
     */
    private String getToken(String loginTokenUrlAddress, String uuid, String subAccount) {
        if (CharSequenceUtil.isEmpty(uuid)) {
            log.info("获取token失败, uuid为空");
            return null;
        }

        try {
            // 延迟1.5s
            Thread.sleep(2000);
        } catch (Exception e) {
            e.printStackTrace();
            log.info("接口调用休眠异常");
        }

        GetTokenVO tokenVO = new GetTokenVO();
        tokenVO.setUuid(uuid);
        tokenVO.setSubAccount(subAccount + "-vcrm");

        // 请求
        String postJsonResult = postRequest(loginTokenUrlAddress, JSON.toJSONString(tokenVO));

        log.info("获取token result: {}", postJsonResult);
        if (CharSequenceUtil.isEmpty(postJsonResult)) {
            return null;
        }

        return postJsonResult;
    }

    /**
     * 获取UUID
     *
     * @param uuidUrlAddress 请求地址
     * @param key            sm2公钥
     * @param tokenVO        参数
     * @return uuid
     */
    private String getUuid(String uuidUrlAddress, String key, GetLoginTokenVO tokenVO) {
        if (CharSequenceUtil.isEmpty(key)) {
            log.info("获取UUID失败, key为空");
            return null;
        }

        GetUuidVO uuidVO = new GetUuidVO();
        uuidVO.setAccount(tokenVO.getAccount());
        String encrypt = SM2Utils.encryptByPublicKey(tokenVO.getPassword(), key);
        uuidVO.setPassword(encrypt);

        // 请求
        String postJson = postRequest(uuidUrlAddress, JSON.toJSONString(uuidVO));

        log.info("获取UUID result: {}", postJson);
        if (CharSequenceUtil.isEmpty(postJson)) {
            return null;
        }

        JSONObject jsonObject = JSON.parseObject(postJson);
        if (jsonObject.getInteger(FIELD_NAME_CODE).equals(DEFAULT_STATUS_CODE_SUCCESS)) {
            return jsonObject.getString(FIELD_NAME_DATA);
        }

        return null;
    }


    /**
     * 获取中台key
     *
     * @param getKeyUrl 请求地址
     * @return key
     */
    private String getKey(String getKeyUrl) {

        // 获取公钥
        String publicKeyResult = getRequest(getKeyUrl);
        log.info("获取中台key result: {}", publicKeyResult);
        if (CharSequenceUtil.isEmpty(publicKeyResult)) {
            return null;
        }

        JSONObject jsonObject = JSON.parseObject(publicKeyResult);
        if (jsonObject.getInteger(FIELD_NAME_CODE).equals(DEFAULT_STATUS_CODE_SUCCESS)) {
            return jsonObject.getJSONObject(FIELD_NAME_DATA).getString(FIELD_NAME_KEY);
        }

        return null;
    }

    @Override
    public String requestVcrmSubAccount(MobileInformBaseVO cookieVO) {
        // 配置参数
        ApplicationDefinedConfiguration.InTransitOrder inTransitOrder = applicationDefinedConfiguration.getInTransitOrder();

        // 请求头
        Map<String, String> headerParam = new HashMap<>(1);
        headerParam.put("cookie", cookieVO.getCookie());

        String result = HttpClientUtil.doPost2(inTransitOrder.getVcrmSubAccountUrlAddress(),
                null, headerParam);

        return (CharSequenceUtil.isEmpty(result) ? defaultVcrmSubAccountResult() : result);
    }

    /**
     * 默认返回401
     *
     * @return 默认返回401结果
     */
    private String defaultVcrmSubAccountResult() {
        return "{\"code\":401,\"message\":null,\"body\":null,\"validate_error\":null,\"ok\":true,\"fallback\":false}";
    }


    /**
     * get请求(忽略ssl证书)
     *
     * @param url 地址
     * @return 请求结果
     */
    private String getRequest(String url) {
        // 链式构建请求
        HttpRequest httpRequest = HttpRequest.get(url);
        // https 忽略证书
        httpRequest.setSSLSocketFactory(SSLUtils.getSSLSocketFactory());

        // 超时，毫秒
        return httpRequest.timeout(20000).execute().body();
    }

    /**
     * post请求 (忽略ssl证书)
     *
     * @param url        地址
     * @param jsonString 请求参数(json串)
     * @return 请求结果
     */
    private String postRequest(String url, String jsonString) {
        // 链式构建请求
        HttpRequest httpRequest = HttpRequest.post(url);

        // https 忽略证书
        httpRequest.setSSLSocketFactory(SSLUtils.getSSLSocketFactory());

        return httpRequest
                // 头信息，多个头信息多次调用此方法即可
                .header(Header.CONTENT_TYPE, ContentType.JSON.getValue())
                .body(jsonString)
                // 超时，毫秒
                .timeout(20000)
                .execute()
                .body();
    }
}
