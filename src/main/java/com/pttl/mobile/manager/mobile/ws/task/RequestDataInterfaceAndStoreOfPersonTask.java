package com.pttl.mobile.manager.mobile.ws.task;

import com.pttl.mobile.manager.mobile.ws.service.PersonInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 人员信息
 * 自动请求数据接口 并完成解析解密后入库
 *
 * <AUTHOR>
 * @date 2022/12/30
 **/
@Slf4j
@Component
@ConditionalOnProperty(prefix = "schedule.person", name = "enabled", havingValue = "true")
public class RequestDataInterfaceAndStoreOfPersonTask {

    /**
     * 人员信息 服务
     */
    @Resource
    private PersonInfoService personInfoService;

    @Scheduled(cron = "${schedule.person.cron}")
    public void requestDataToBeParsedAndStored() {
        log.info("start RequestDataInterfaceAndStoreOfPersonTask ...");

        personInfoService.automaticSynchronization2Database();

        log.info("RequestDataInterfaceAndStoreOfPersonTask success ...");
    }

}
