package com.pttl.mobile.manager.mobile.repository.impl;

import com.alibaba.fastjson.JSON;
import com.pttl.mobile.manager.mobile.document.MobileRecordLogDocument;
import com.pttl.mobile.manager.mobile.repository.ElasticsearchMobileRecordLogService;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.admin.indices.delete.DeleteIndexRequest;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.delete.DeleteRequest;
import org.elasticsearch.action.delete.DeleteResponse;
import org.elasticsearch.action.get.GetRequest;
import org.elasticsearch.action.get.GetResponse;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.index.IndexResponse;
import org.elasticsearch.action.support.master.AcknowledgedResponse;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.action.update.UpdateResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.indices.CreateIndexRequest;
import org.elasticsearch.client.indices.CreateIndexResponse;
import org.elasticsearch.common.settings.Settings;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.rest.RestStatus;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;

/**
 * 移动端es日志
 *
 * <AUTHOR>
 * @date 2022/11/8
 **/
@Slf4j
@Service
public class ElasticsearchMobileRecordLogServiceImpl implements ElasticsearchMobileRecordLogService {

    /**
     * es客户端
     */
    @Resource
    private RestHighLevelClient restHighLevelClient;


    /**
     * 删除索引(删表)
     *
     * @param index 索引
     * @return 是否成功
     * @throws IOException 异常
     */
    @Override
    public Boolean deleteIndex(String index) throws IOException {
        DeleteIndexRequest deleteIndexRequest = new DeleteIndexRequest(index);
        AcknowledgedResponse deleteIndexResponse = restHighLevelClient.indices().delete(deleteIndexRequest, RequestOptions.DEFAULT);
        return deleteIndexResponse.isAcknowledged();
    }

    /**
     * 创建索引(建表)
     *
     * @param index 所以名称
     * @return 1
     * @throws IOException io
     */
    @Override
    public boolean createIndex(String index, String propertiesJsonString) throws IOException {

        // 创建索引(建表)
        CreateIndexRequest createIndexRequest = new CreateIndexRequest(index);
        createIndexRequest.settings(Settings.builder()
                .put("index.number_of_shards", 1)
                .put("index.number_of_replicas", 0)
        );

        createIndexRequest.mapping(propertiesJsonString, XContentType.JSON);
        CreateIndexResponse createIndexResponse = restHighLevelClient.indices().create(createIndexRequest, RequestOptions.DEFAULT);
        return createIndexResponse.isAcknowledged();
    }


    /**
     * 创建文档(插入数据)  -- 这里使用了异步的方式
     *
     * @param document doc对象
     * @throws IOException 异常
     */
    @Async(value = "saveAsync")
    @Override
    public void createDocument(MobileRecordLogDocument document) throws IOException {
        // 这里不配置id, 让es自动生成
        IndexRequest indexRequest = new IndexRequest(MobileRecordLogDocument.INDEX_NAME)
                //.id(document.getId())
                .source(JSON.toJSONString(document), XContentType.JSON);
        IndexResponse indexResponse = restHighLevelClient.index(indexRequest, RequestOptions.DEFAULT);
        indexResponse.status();
    }

    /**
     * 批量创建文档
     *
     * @param documents doc对象
     * @return 是否成功
     * @throws IOException 异常
     */
    @Override
    public Boolean bulkCreateDocument(List<MobileRecordLogDocument> documents) throws IOException {
        BulkRequest bulkRequest = new BulkRequest();
        for (MobileRecordLogDocument document : documents) {
            IndexRequest indexRequest = new IndexRequest(MobileRecordLogDocument.INDEX_NAME)
                    .source(JSON.toJSONString(document), XContentType.JSON);
            bulkRequest.add(indexRequest);
        }
        BulkResponse bulkResponse = restHighLevelClient.bulk(bulkRequest, RequestOptions.DEFAULT);
        return bulkResponse.status().equals(RestStatus.OK);
    }

    /**
     * 查看文档
     *
     * @param id id
     * @return doc对象
     * @throws IOException 异常
     */
    @Override
    public MobileRecordLogDocument getDocument(String id) throws IOException {
        GetRequest getRequest = new GetRequest(MobileRecordLogDocument.INDEX_NAME, id);
        GetResponse getResponse = restHighLevelClient.get(getRequest, RequestOptions.DEFAULT);
        MobileRecordLogDocument result = new MobileRecordLogDocument();
        if (getResponse.isExists()) {
            String sourceAsString = getResponse.getSourceAsString();
            result = JSON.parseObject(sourceAsString, MobileRecordLogDocument.class);
        } else {
            log.error("没有找到该 id 的文档");
        }
        return result;
    }

    /**
     * 更新文档
     *
     * @param document doc对象
     * @return 是否成功
     * @throws IOException 异常
     */
    @Override
    public Boolean updateDocument(MobileRecordLogDocument document) throws IOException {
        UpdateRequest updateRequest = new UpdateRequest(MobileRecordLogDocument.INDEX_NAME, document.getEsId());
        updateRequest.doc(JSON.toJSONString(document), XContentType.JSON);
        UpdateResponse updateResponse = restHighLevelClient.update(updateRequest, RequestOptions.DEFAULT);
        return updateResponse.status().equals(RestStatus.OK);
    }

    /**
     * 删除文档
     *
     * @param id doc id
     * @return 名称
     * @throws IOException 异常
     */
    @Override
    public String deleteDocument(String id) throws IOException {
        DeleteRequest deleteRequest = new DeleteRequest(MobileRecordLogDocument.INDEX_NAME, id);
        DeleteResponse response = restHighLevelClient.delete(deleteRequest, RequestOptions.DEFAULT);
        return response.getResult().name();
    }
}
