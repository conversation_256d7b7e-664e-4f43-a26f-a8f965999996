package com.pttl.mobile.manager.mobile.util;

import cn.hutool.core.date.DateUtil;
import com.pttl.mobile.manager.mobile.exception.ErrorMessageException;

import java.util.Calendar;
import java.util.Date;

/**
 * 表名称-日期 工具
 *
 * <AUTHOR>
 * @date 2022/11/10
 **/

public class TableNameDateUtil {

    /**
     * 表名
     */
    public static final String TABLE_NAME = "mm_mobile_record_log";

    private TableNameDateUtil() {
    }

    /**
     * 获取当前年月  例: 2022_9
     *
     * @return 当前年月
     */
    public static String getCurrentYearMonth() {
        Date date = DateUtil.date();
        return formatDate(date);
    }

    /**
     * 获取距离当前月 几个月之前的月份 例: 传入: 3 当前:2022_9 --> 返回: 2022_6
     *
     * @param beforeMonth 几个月之前的月份
     * @return 例: 2022_7
     */
    public static String getBeforeYearMonth(int beforeMonth) {
        Date date = DateUtil.date();
        //获得年的部分
        int year = DateUtil.year(date);
        //获得月份，从0开始计数
        int month = DateUtil.month(date) + 1;

        if (month > beforeMonth) {
            month -= beforeMonth;
            return year + "_" + month;
        }

        if (month == beforeMonth) {
            month = 12;
            year -= 1;
            return year + "_" + month;
        }

        month -= beforeMonth;
        month = 12 + month;
        year -= 1;
        return year + "_" + month;
    }

    /**
     * 计算给定的时间参数与当前时间的月份距离，如果月份距离小于三个月，它将返回给定时间参数的年月份（格式为"yyyy_MM"），否则会抛出异常
     *
     * @param date          时间
     * @param monthQuantity 月份数量
     * @return 计算后的结果
     * @throws ErrorMessageException 异常信息
     */
    public static String getYearMonthFromDate(Date date, int monthQuantity) throws ErrorMessageException {
        if (date == null) {
            // 如果为null 则返回当前时间的年月份(格式: 2022_11)
            return formatDate(new Date());
        }

        Calendar currentCalendar = Calendar.getInstance();
        Calendar startCalendar = Calendar.getInstance();
        startCalendar.setTime(date);

        // 计算月份差距
        int monthsDiff = (currentCalendar.get(Calendar.YEAR) - startCalendar.get(Calendar.YEAR)) * 12
                + currentCalendar.get(Calendar.MONTH) - startCalendar.get(Calendar.MONTH);

        if (monthsDiff < monthQuantity) {
            return formatDate(date);
        } else {
            throw new ErrorMessageException("月份距离大于" + monthQuantity + "个月，不符合条件");
        }
    }

    /**
     * 格式化时间(因使用SimpleDateFormat格式化不符合, 区别: 2023_09与2023_9)
     *
     * @param date 时间
     * @return 例如: 2023_9
     */
    private static String formatDate(Date date) {
        //获得年的部分
        int year = DateUtil.year(date);
        //获得月份，从0开始计数
        int month = DateUtil.month(date) + 1;

        return year + "_" + month;
    }

}
