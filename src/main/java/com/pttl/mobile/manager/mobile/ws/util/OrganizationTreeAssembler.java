package com.pttl.mobile.manager.mobile.ws.util;

import com.pttl.mobile.manager.mobile.ws.dto.OrgNodeDTO;

import java.util.*;


/**
 * 部门操作
 *
 * <AUTHOR>
 * @date 2023/7/20
 **/
public class OrganizationTreeAssembler {

    public static void main(String[] args) {
        // 假设以下是数据集合
        List<OrgNodeDTO> orgData = new ArrayList<>();
        orgData.add(new OrgNodeDTO("CEO", 1, null, 0));
        orgData.add(new OrgNodeDTO("Engineering Department", 2, "CEO", 1));
        orgData.add(new OrgNodeDTO("HR Department", 3, "CEO", 1));
        orgData.add(new OrgNodeDTO("Manager 1", 4, "Engineering Department", 2));
        orgData.add(new OrgNodeDTO("Manager 2", 5, "Engineering Department", 2));
        orgData.add(new OrgNodeDTO("Employee 1", 6, "Manager 1", 4));
        orgData.add(new OrgNodeDTO("Employee 2", 7, "Manager 1", 4));
        orgData.add(new OrgNodeDTO("Employee 3", 8, "Manager 2", 5));

        OrgNodeDTO root = OrganizationTreeAssembler.buildOrganizationTree(orgData, 0);

        // 获取每个部门下的部门ID和子部门ID
        Map<Integer, Set<Integer>> departmentIdsMap = getDepartmentIdsMap(root);

        // 打印结果
        for (Map.Entry<Integer, Set<Integer>> entry : departmentIdsMap.entrySet()) {
            Integer departmentName = entry.getKey();
            Set<Integer> departmentIds = entry.getValue();
            System.out.println("部门ID：" + departmentName + "，部门ID及子部门ID：" + departmentIds);
        }
    }

    public static Map<Integer, Set<Integer>> getDepartmentIdsMap(OrgNodeDTO root) {
        Map<Integer, Set<Integer>> departmentIdsMap = new HashMap<>();
        populateDepartmentIdsMap(root, departmentIdsMap);
        return departmentIdsMap;
    }

    public static void populateDepartmentIdsMap(OrgNodeDTO node, Map<Integer, Set<Integer>> departmentIdsMap) {
        departmentIdsMap.put(node.getDeptId(), node.getAllDepartmentIds());
        for (OrgNodeDTO child : node.getChildren()) {
            populateDepartmentIdsMap(child, departmentIdsMap);
        }
    }

    /**
     * 构建树数据
     *
     * @param orgData 组织数据
     * @param rootId  跟id
     * @return
     */
    public static OrgNodeDTO buildOrganizationTree(List<OrgNodeDTO> orgData, Integer rootId) {
        OrgNodeDTO root = null;

        Map<Integer, OrgNodeDTO> departmentMap = new HashMap<>();
        for (OrgNodeDTO node : orgData) {
            if (node.getPartDeptIdChn() == rootId) {
                root = new OrgNodeDTO(node.getDeptAbbreviation(), node.getDeptId(), node.getSuperiorDeptAbbreviation(), rootId);
            }
            departmentMap.put(node.getDeptId(), node);
        }

        if (root != null) {
            buildChildren(root, orgData, departmentMap);
        }

        return root;
    }

    /**
     * 构建子树
     *
     * @param parent
     * @param orgData
     */
    public static void buildChildren(OrgNodeDTO parent, List<OrgNodeDTO> orgData, Map<Integer, OrgNodeDTO> departmentMap) {
        for (OrgNodeDTO node : orgData) {
            if (node.getPartDeptIdChn() == parent.getDeptId()) {
                OrgNodeDTO child = new OrgNodeDTO(node.getDeptAbbreviation(), node.getDeptId(), parent.getDeptAbbreviation(), parent.getDeptId());
                parent.addChild(child);
                buildChildren(child, orgData, departmentMap);
            }
        }
    }

    public static void displayOrganizationTree(OrgNodeDTO node, int level) {
        for (int i = 0; i < level; i++) {
            // 每层缩进4个空格
            System.out.print("    ");
        }
        System.out.println("|-- " + node.getDeptAbbreviation());
        for (OrgNodeDTO child : node.getChildren()) {
            displayOrganizationTree(child, level + 1);
        }
    }
}

