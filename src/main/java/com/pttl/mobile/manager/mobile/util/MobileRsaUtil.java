package com.pttl.mobile.manager.mobile.util;

import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.springframework.stereotype.Component;

import java.security.KeyPair;

/**
 * RSA加密
 *
 * <AUTHOR>
 * 其实与
 * @see com.pttl.mobile.manager.util.RSAUtil
 * 这个一样, 只是写法不同
 */
@Slf4j
@Component
public class MobileRsaUtil {

    /**
     * rsa加解密 密钥对
     */
    private static final String PUBLIC_KEY_STRING = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDIZips6X5rw/5/U/Z75qFyXAR+gMn7dhVq/ZoEtGksKT9FpGKGOc6eYe5JBXILvEu359N/S5VqWnwper3Hm09qe1GJyGaoWX2a+R5T/Fc/zqPotvaNrH1k8G6kRXERnlhIca741DS2J/C5y+WNcoeB9toP0+W7mH1sODHSX3LufwIDAQAB";
    private static final String PRIVATE_KEY_STRING = "MIICdQIBADANBgkqhkiG9w0BAQEFAASCAl8wggJbAgEAAoGBAMhmKmzpfmvD/n9T9nvmoXJcBH6Ayft2FWr9mgS0aSwpP0WkYoY5zp5h7kkFcgu8S7fn039LlWpafCl6vcebT2p7UYnIZqhZfZr5HlP8Vz/Oo+i29o2sfWTwbqRFcRGeWEhxrvjUNLYn8LnL5Y1yh4H22g/T5buYfWw4MdJfcu5/AgMBAAECgYASoyXI8UI3pYeysc1XopBbQWGNnk6qm0XMqH89dsXdPIzM3kfncdLcegEeO2pq2v0zwlSyHwZrWLT5apfhy3jsJRsgNvIly1d1yZTsSm1fHfLNH/DvHPF+2xUk3yxF4ZZSesn+CNkijPj4eQfW+uOTK5CwWpHXpTe5tehRTBOrQQJBAOnW/bAIZLDlaPPixg1uLdlqyWyEI4ou2tqFizj/Ilky9r9cfl3gA9J+AmZZLgpx6nSmQQpAebRW94F5GdquwaECQQDbY+UX+ZjmvwqfA0cPz9ine0WQq9Qb+yHwcfDseclj92iVMfn8ZJk2WFIWsrhAXpaUJJtIZfOuf6DfdkP9T/wfAkBoprDuYTM+/8ySqYDdbdvTG7l9eT22j/WeKnn+TiB/LxHC3pAnTyQmxgIo7+xs/EJjZixhfC17ihhGt33coq1BAkBPQCSvpnA/Wl+B1XNEGPbQeU1F12xFt1ApRV+cbNn9DJBw5J7DN6vI39q3bFP2nLSUSyz02C9Qp7X7QwOIa+5NAkBOJJVvaJaUTh6UtuZ09/bgEPLxsvyfg8yOWu+8/HrXvwDDDLq4f1y+q1Zdzr7kNLhbXzgbYNktiEtz12bBcpQC";

    /**
     * 获取实例
     *
     * @return 获取实例
     */
    public static MobileRsaUtil getInstance() {
        return new MobileRsaUtil();
    }

    /**
     * 数据加密
     *
     * @param plaintext 明文
     * @return 密文
     */
    public String encrypt(String plaintext) {
        RSA rsa2 = new RSA(null, PUBLIC_KEY_STRING);
        byte[] encrypt = rsa2.encrypt(StrUtil.bytes(plaintext, CharsetUtil.CHARSET_UTF_8), KeyType.PublicKey);
        String encodeBase64Strings = Base64.encodeBase64String(encrypt);
        return StrUtil.str(encodeBase64Strings, CharsetUtil.CHARSET_UTF_8);
    }

    /**
     * 密文解密
     *
     * @param ciphertext 密文
     * @return 明文
     */
    public String decrypt(String ciphertext) {
        RSA rsa3 = new RSA(PRIVATE_KEY_STRING, null);
        byte[] decodeBase64 = Base64.decodeBase64(ciphertext);
        byte[] decrypt = rsa3.decrypt(decodeBase64, KeyType.PrivateKey);
        return StrUtil.str(decrypt, CharsetUtil.CHARSET_UTF_8);
    }

    /**
     * 获取密钥对 获取rsa密钥对
     *
     * @return 密钥对
     */
    public KeyPair getKeyPair() {
        RSA rsa = new RSA();

        //获得私钥
        rsa.getPrivateKey();
        String privateKeyBase64 = rsa.getPrivateKeyBase64();

        //获得公钥
        rsa.getPublicKey();
        String publicKeyBase64 = rsa.getPublicKeyBase64();

        return SecureUtil.generateKeyPair("RSA");
    }


    public static void main(String[] args) {
        // BPM：wangxia   Bpm#6789
        // wangxia pzG4FZNWuQVAfoGEFwP8Ma6LMgvXKkY9QIF+K8ui8d3EJny0CAwsnZoEHi2hlD3NTM8Hywejl7PbzXCqNiAKV5zmOIDHc+WF5bLoxu1pf7r9GBRflv3L2BHftnfRtYTSvvCPGMyNmnJ3+ff9GYiCE55Gg5t3GIGJG/pduKrKtNg=
        // Bpm#6789 IC0wxB6us62UBOLC8t/I5xfotYDvVDdbQTRxFjI9ZztrNF8QiwF1S4LAHev1tyKdf5++E1JQbtMoyRTwqhAW5C4MJJdAmogwCX+KKIPw+BRQYwi32FNzlNx8xh++NqPN25RpbVKpZodlSepJ2K1U4k5PdtrrnfWNp+MqBFH4ZU0=
        // VCRM：tl000906  xia#590028
        // tl000906 JdwEkyWiJ5iRRfuGgIyq+dMFmAr7VmgZyI20zRngCJDA6Beg7YirQjUucMmk37oBJk63Jh+2uh/AfjnzBXE4zQ1jerViSrPF32Kb3OFoK67XtB33OTr8xny4u4o68gc+pGXz89lAjRIL2WjAw5jMGJvbkkvmiW2kwDIobUg5qjU=
        // xia#590028 EbYULlRt21gM1IseIybe+w8VR3zYfokqtkk+gotuBhVAHlpTmcj7O6RFf8kju6LUPJyircXTpKHWU0v5XNUTA1yMQw72MnT4BIfYMNhSr4IpCXJ5DuKPujwxaZiBbgI233KOU2ygfznyJxUTT9xJFAQ0BJeY9Ah94sfnJSgZ/tg=

        String str = "xia#590028";
        String encrypt = MobileRsaUtil.getInstance().encrypt(str);
        System.out.println("加密后：" + encrypt);

        String decrypt = MobileRsaUtil.getInstance().decrypt(encrypt);
        System.out.println("解密后：" + decrypt);
    }
}
