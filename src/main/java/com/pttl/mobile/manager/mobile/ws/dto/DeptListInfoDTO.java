package com.pttl.mobile.manager.mobile.ws.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

/**
 * 部门信息 数据列表dto
 *
 * <AUTHOR>
 * @date 2022/12/29
 **/

@Data
public class DeptListInfoDTO implements Serializable {

    private static final long serialVersionUID = 6896016287965931696L;

    public static final String NAME_PREFIX = "dept:info:";

    /**
     * 有效状态 A有效 I无效
     */
    public static final String EFF_STATUS = "A";

    /**
     * hpsDescrshort
     * 部门简称
     */
    @JSONField(name = "HPS_DESCRSHORT")
    private String deptAbbreviationShort;
    /**
     * hpsManagerId
     * 部门经理ID
     */
    @JSONField(name = "HPS_MANAGER_ID")
    private String managerId;
    /**
     * hpsServAttDescr
     * 业务属性
     */
    @JSONField(name = "HPS_SERV_ATT_DESCR")
    private String servAttAbbreviation;
    /**
     * effStatus
     */
    @JSONField(name = "EFF_STATUS")
    private String effStatus;
    /**
     * deptDescr
     * 部门全称
     */
    @JSONField(name = "DEPT_DESCR")
    private String deptFullName;
    /**
     * hpsSequence
     */
    @JSONField(name = "HPS_SEQUENCE")
    private Integer sequence;
    /**
     * hpsDeptCost
     * 成本承担部门ID
     */
    @JSONField(name = "HPS_DEPT_COST")
    private String deptCost;
    /**
     * hpsDepSortDescr
     * 部门类别
     */
    @JSONField(name = "HPS_DEP_SORT_DESCR")
    private String deptSortAbbreviation;
    /**
     * hpsDeptCount
     */
    @JSONField(name = "HPS_DEPT_COUNT")
    private Integer deptCount;
    /**
     * rownumber
     */
    @JSONField(name = "rownumber")
    private Integer rowNumber;
    /**
     * hpsDeptHieDescr
     * 部门层级
     */
    @JSONField(name = "HPS_DEPT_HIE_DESCR")
    private String deptHieAbbreviation;
    /**
     * deptid
     * <p>
     * 部门ID
     */
    @JSONField(name = "DEPTID")
    private String deptId;
    /**
     * companyDescr
     * 部门所属公司
     */
    @JSONField(name = "COMPANY_DESCR")
    private String companyAbbreviation;
}
