package com.pttl.mobile.manager.mobile.controller;

import com.pttl.mobile.manager.constant.ExtendedBusinessEnum;
import com.pttl.mobile.manager.lib.ResponseMessage;
import com.pttl.mobile.manager.service.ExtendedBusinessService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

/**
* 扩展业务数据控制层
*
* <AUTHOR>
*/
@RestController
@RequestMapping("/mobile/extended")
public class MobileExtendedBusinessController {

    /**
    * 服务对象
    */
    @Autowired
    private ExtendedBusinessService extendedBusinessService;

    @GetMapping("getVrSwitch")
    @ApiOperation(value = "ios vr 开关获取", notes = "ios vr 开关获取")
    public ResponseMessage<Boolean> gwtVrSwitch() {

        String data = extendedBusinessService.getByKey(ExtendedBusinessEnum.IOS_VR_SWITCH_ON);

        // 开关状态
        if (Objects.equals(data, ExtendedBusinessEnum.IOS_VR_SWITCH_OFF.getData())) {
            return ResponseMessage.ok(false);
        } else {
            return ResponseMessage.ok(true);
        }
    }

}
