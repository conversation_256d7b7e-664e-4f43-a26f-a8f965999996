package com.pttl.mobile.manager.mobile.ws.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.pttl.mobile.manager.config.ApplicationDefinedConfiguration;
import com.pttl.mobile.manager.mobile.dto.AddressBookAndPersonDTO;
import com.pttl.mobile.manager.mobile.dto.AddressBookInfoOfQueryDTO;
import com.pttl.mobile.manager.mobile.dto.OrganizationalStructureInfoDTO;
import com.pttl.mobile.manager.mobile.ws.constant.SoapConstants;
import com.pttl.mobile.manager.mobile.ws.dao.OrganizationalStructureInfoMapper;
import com.pttl.mobile.manager.mobile.ws.dto.DeptListInfoDTO;
import com.pttl.mobile.manager.mobile.ws.dto.OrgNodeDTO;
import com.pttl.mobile.manager.mobile.ws.dto.OrganizationalStructureListInfoDTO;
import com.pttl.mobile.manager.mobile.ws.entity.OrganizationalStructureInfoDO;
import com.pttl.mobile.manager.mobile.ws.entity.PersonInfoDO;
import com.pttl.mobile.manager.mobile.ws.service.DeptListInfoService;
import com.pttl.mobile.manager.mobile.ws.service.OrganizationalStructureInfoService;
import com.pttl.mobile.manager.mobile.ws.service.PersonInfoService;
import com.pttl.mobile.manager.mobile.ws.util.FileScanUtil;
import com.pttl.mobile.manager.mobile.ws.util.OrganizationTreeAssembler;
import com.pttl.mobile.manager.util.BeanMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class OrganizationalStructureInfoServiceImpl implements OrganizationalStructureInfoService {


    /**
     * 数据抽取类
     */
    @Resource
    private DeptListInfoService deptListInfoService;

    /**
     * redis客户端
     */
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private OrganizationalStructureInfoMapper organizationalStructureInfoMapper;

    /**
     * 人员信息 服务层
     */
    @Resource
    private PersonInfoService personInfoService;

    /**
     * 动态配置
     */
    @Resource
    private ApplicationDefinedConfiguration applicationDefinedConfiguration;

    @Resource
    private RedisTemplate<String, DeptListInfoDTO> redisTemplate;

    @Override
    public int deleteByPrimaryKey(Long id) {
        return organizationalStructureInfoMapper.deleteByPrimaryKey(id);
    }

    @Override
    public int insert(OrganizationalStructureInfoDO record) {
        return organizationalStructureInfoMapper.insert(record);
    }

    @Override
    public int insertSelective(OrganizationalStructureInfoDO record) {
        return organizationalStructureInfoMapper.insertSelective(record);
    }

    @Override
    public OrganizationalStructureInfoDO selectByPrimaryKey(Long id) {
        return organizationalStructureInfoMapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(OrganizationalStructureInfoDO record) {
        return organizationalStructureInfoMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(OrganizationalStructureInfoDO record) {
        return organizationalStructureInfoMapper.updateByPrimaryKey(record);
    }

    @Override
    public int updateBatch(List<OrganizationalStructureInfoDO> list) {
        return organizationalStructureInfoMapper.updateBatch(list);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int batchInsert(List<OrganizationalStructureInfoDO> list) {
        return organizationalStructureInfoMapper.batchInsert(list);
    }

    @Transactional(rollbackFor = Exception.class)
    void cleanTable() {
        organizationalStructureInfoMapper.cleanTable();
    }

    /**
     * 请求web service api 并解析解密后数据分批入库
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void automaticSynchronization2Database() {
        // 获取数据
        List<OrganizationalStructureListInfoDTO> structureListInfoList = deptListInfoService.
                dataExtraction(SoapConstants.PARAMETER_HPS_INF_NAME_TREE, OrganizationalStructureListInfoDTO.class);

        if (CollUtil.isEmpty(structureListInfoList)) {
            log.info("web service api OrganizationalStructureListInfo is null, not into database......");
            return;
        }

        // 数据类型及bean转换 231114增加 排除无效部门数据
        List<OrganizationalStructureInfoDO> dataList = bean2Bean(structureListInfoList);

        // 入库
        insert2Database(dataList);

        // 将数据组装为树形并查找出所有部门及子部门的 部门id
        processOrganizationTreeAssembler(dataList);

        // 处理完成清除缓存
        dataList.clear();
    }

    /**
     * 数据组装为树形并查找出所有部门及子部门的 部门id
     *
     * @param dataList 源数据
     */
    private void processOrganizationTreeAssembler(List<OrganizationalStructureInfoDO> dataList) {
        // 20230720
        TimeInterval timer = DateUtil.timer();
        log.info("开始进入 初始化一级及二级部门下的所有部门id到缓存");

        List<OrgNodeDTO> orgNodeList = BeanMapper.mapList(dataList, OrgNodeDTO.class);
        OrgNodeDTO root = OrganizationTreeAssembler.buildOrganizationTree(orgNodeList, OrganizationalStructureInfoDO.TREE_ROOT_ID);

        // 获取每个部门下的部门ID和子部门ID
        Map<Integer, Set<Integer>> departmentIdsMap = OrganizationTreeAssembler.getDepartmentIdsMap(root);

        // 写入缓存
        for (Map.Entry<Integer, Set<Integer>> entry : departmentIdsMap.entrySet()) {
            // 写入缓存
            stringRedisTemplate.opsForValue().set(String.format(SoapConstants.CHILD_DEPT_ID_KEY_PREFIX, entry.getKey()),
                    JSONObject.toJSONString(entry.getValue()), getExpirationTime(), TimeUnit.MINUTES);
        }

        log.info("完成 初始化二级部门下的所有部门id到缓存, 耗时: {}", timer.intervalPretty());
    }

    @Override
    public List<AddressBookAndPersonDTO> listAddressBookAndPersonInfo(AddressBookInfoOfQueryDTO queryVO) {
        // 条件 都为空时查询根目录下的一级目录
        if (Objects.isNull(queryVO) || (Objects.isNull(queryVO.getDeptId()) && StrUtil.isEmpty(queryVO.getNameDesc()))) {
            return listByDeptRootId();
        }

        // 条件 部门id为空, name有值时, 查询(模糊)所有符合条件的人员和部门
        if (Objects.isNull(queryVO.getDeptId())) {
            return listByNameAndDeptIdList(queryVO.getNameDesc(), null);
        }

        // 条件 部门id有值, name为空, 查询(模糊)当前部门下的人员和部门
        if (StrUtil.isEmpty(queryVO.getNameDesc())) {
            return listByNameAndDeptIdList(null, CollUtil.newArrayList(queryVO.getDeptId()));
        }

        // 条件 部门id有值, name有值时,
        // 查询(模糊)所有符合条件的人员和当前部门及子部门下的符合条件的
        // 因需要查询子部门 , 这里结合缓存, 避免每次数据库递归导致查询效率慢
        // 首先获取 当前部门及子部门 的部门id
        List<Integer> deptIds = getDeptListByDeptIdOfCache(queryVO.getDeptId());
        return listByNameAndDeptIdList(queryVO.getNameDesc(), deptIds);
    }

    @Override
    public List<OrganizationalStructureInfoDTO> listOrganizationalStructureInfo(Integer deptId, String nameDesc) {
        ArrayList<Integer> deptIds = null;
        // 部门名称和部门id都为空 即默认一级部门 默认查询一级
        if (StrUtil.isEmpty(nameDesc) && Objects.isNull(deptId)) {
            deptIds = CollUtil.newArrayList(OrganizationalStructureInfoDO.TREE_SECOND_LEVEL);
        }

        if (Objects.nonNull(deptId)) {
            deptIds = CollUtil.newArrayList(deptId);
        }

        // 获取当前部门的子部门信息及人数
        List<OrganizationalStructureInfoDO> list = organizationalStructureInfoMapper.listByDeptNameAndDeptIdList(nameDesc,
                deptIds);

        return processOrganizationalStructureAmount(list);
    }

    /**
     * 处理获取部门下人数
     *
     * @param list 待处理数据
     * @return 完成数据
     */
    private List<OrganizationalStructureInfoDTO> processOrganizationalStructureAmount(List<OrganizationalStructureInfoDO> list) {
        // 为空直接返回
        if (CollUtil.isEmpty(list)) {
            return CollUtil.newArrayList();
        }

        List<OrganizationalStructureInfoDTO> organizationalStructureInfos = BeanMapper.mapList(list, OrganizationalStructureInfoDTO.class);


        for (OrganizationalStructureInfoDTO organizationalStructureInfoDTO : organizationalStructureInfos) {
            List<Integer> deptIdOfCache = getDeptListByDeptIdOfCache(organizationalStructureInfoDTO.getDeptId());
            // 是否存在子部门
            if (deptIdOfCache.size() == 1) {
                organizationalStructureInfoDTO.setHasChildDirectory(false);
            }

            if (CollUtil.isEmpty(deptIdOfCache)) {
                organizationalStructureInfoDTO.setAmount(0);
                continue;
            }
            organizationalStructureInfoDTO.setAmount(personInfoService.countByDeptIds(deptIdOfCache));
        }

        return organizationalStructureInfos;
    }

    /**
     * 获取 当前部门及子部门 的部门id
     *
     * @param deptId
     * @return
     */
    private List<Integer> getDeptListByDeptIdOfCache(Integer deptId) {
        // 优先缓存获取
        String deptIdsString = stringRedisTemplate.opsForValue().get(String.format(SoapConstants.CHILD_DEPT_ID_KEY_PREFIX, deptId));
        if (StrUtil.isNotEmpty(deptIdsString)) {
            return JSONObject.parseArray(deptIdsString, Integer.class);
        }

        return CollUtil.newArrayList();
    }

    /**
     * 获取缓存失效时间 / 分钟
     *
     * @return 缓存失效时间
     */
    private Long getExpirationTime() {
        // 检查配置
        Long searchIndexExpireTime = applicationDefinedConfiguration.getMobileAddressBook().getSearchIndexExpireTime();
        if (Objects.nonNull(searchIndexExpireTime)) {
            return searchIndexExpireTime;
        }
        // 如果配置为空, 则计算当天 当前时间到当天凌晨剩余的分钟数 作为缓存失效时间
        // 一天的结束，结果：2017-03-01 23:59:59
        Date currentDate = new Date();
        Date endOfDay = DateUtil.endOfDay(currentDate);
        return DateUtil.between(currentDate, endOfDay, DateUnit.MINUTE);
    }

    /**
     * 根据名称或者部门id list 获取人员数据
     *
     * @param nameDesc   名称
     * @param deptIdList 部门di
     * @return 获取人员数据
     */
    private List<AddressBookAndPersonDTO> listByNameAndDeptIdList(String nameDesc, List<Integer> deptIdList) {
        // 先查人员
        List<PersonInfoDO> personList = personInfoService.listByNameAndDeptIdList(nameDesc, deptIdList);

        // bean转换
        List<AddressBookAndPersonDTO> personBeanList = personInfoDO2AddressBookAndPerson(personList);

        // 查组织架构
        List<OrganizationalStructureInfoDO> organizationalStructureList = organizationalStructureInfoMapper.
                listByDeptNameAndDeptIdList(nameDesc, deptIdList);

        // bean转换
        List<AddressBookAndPersonDTO> organizationalStructureBeanList = organizationalStructure2AddressBookAndPersonDTO(organizationalStructureList);

        ArrayList<AddressBookAndPersonDTO> objects = CollUtil.newArrayList();
        objects.addAll(personBeanList);
        objects.addAll(organizationalStructureBeanList);
        return objects;
    }

    /**
     * 查询一级目录
     *
     * @return
     */
    private List<AddressBookAndPersonDTO> listByDeptRootId() {
        // 查询一级目录
        List<OrganizationalStructureInfoDO> list = organizationalStructureInfoMapper.listByDeptRootId(OrganizationalStructureInfoDO.TREE_SECOND_LEVEL);

        // 对象转换
        return organizationalStructure2AddressBookAndPersonDTO(list);
    }

    /**
     * 部门数据对象转换dto
     *
     * @param list 数据
     * @return dto
     */
    private List<AddressBookAndPersonDTO> organizationalStructure2AddressBookAndPersonDTO(List<OrganizationalStructureInfoDO> list) {
        ArrayList<AddressBookAndPersonDTO> dtoList = CollUtil.newArrayList();

        if (CollUtil.isNotEmpty(list)) {
            list.forEach(organizationalStructureInfoDO -> {
                AddressBookAndPersonDTO addressBookAndPersonDTO = new AddressBookAndPersonDTO();
                addressBookAndPersonDTO.setDataId(organizationalStructureInfoDO.getDeptId().longValue());
                addressBookAndPersonDTO.setDataType(AddressBookAndPersonDTO.DATA_TYPE_DEPT);
                addressBookAndPersonDTO.setNameDesc(organizationalStructureInfoDO.getDeptAbbreviation());
                // 头像需要单独处理
                addressBookAndPersonDTO.setHeadPicture(getHeadPicture(organizationalStructureInfoDO.getDeptId().toString()));
                dtoList.add(addressBookAndPersonDTO);
            });
        }

        return dtoList;
    }

    /**
     * 根据部门id 或者 人员id 获取是否头像
     *
     * @param id
     * @return
     */
    private String getHeadPicture(String id) {
        // 获取部门或人员头像
        return applicationDefinedConfiguration.getMobileAddressBook().getHeadPictureVisitPre() + id + FileScanUtil.SUFFIX_PNG;
    }

    /**
     * 人员数据对象转换dto
     *
     * @param list 数据
     * @return dto
     */
    private List<AddressBookAndPersonDTO> personInfoDO2AddressBookAndPerson(List<PersonInfoDO> list) {
        ArrayList<AddressBookAndPersonDTO> dtoList = CollUtil.newArrayList();
        if (CollUtil.isNotEmpty(list)) {
            list.forEach(personInfoDO -> {
                AddressBookAndPersonDTO addressBookAndPersonDTO = new AddressBookAndPersonDTO();
                addressBookAndPersonDTO.setDataId(personInfoDO.getId());
                addressBookAndPersonDTO.setDataType(AddressBookAndPersonDTO.DATA_TYPE_PERSON);
                addressBookAndPersonDTO.setNameDesc(personInfoDO.getName());
                // 头像需要单独处理
                addressBookAndPersonDTO.setHeadPicture(getHeadPicture(personInfoDO.getEmployeeId()));
                addressBookAndPersonDTO.setPersonDeptName(personInfoDO.getDeptAbbreviation());
                addressBookAndPersonDTO.setPersonJobName(personInfoDO.getPersonnelDept());
                addressBookAndPersonDTO.setEmployeeId(personInfoDO.getEmployeeId());
                dtoList.add(addressBookAndPersonDTO);
            });
        }
        return dtoList;
    }

    /**
     * 数据入库 分批入库
     *
     * @param dataList 数据
     */
    private void insert2Database(List<OrganizationalStructureInfoDO> dataList) {
        // 清库
        this.cleanTable();
        // 数据入库 分批入库
        int part = (dataList.size() - 1) / SoapConstants.BATCH_NUMBER;

        for (int i = 0; i <= part; i++) {
            // 求每一个批次起始位置
            int fromIndex = i * SoapConstants.BATCH_NUMBER;
            int toIndex = (i + 1) * SoapConstants.BATCH_NUMBER;
            // 若是是最后一个批次，则不能越界
            if (i == part) {
                toIndex = dataList.size();
            }

            // 截取批次长度的list
            List<OrganizationalStructureInfoDO> partList = dataList.subList(fromIndex, toIndex);

            // 入库
            if (CollUtil.isNotEmpty(partList)) {
                batchInsert(partList);
            }
        }
    }

    /**
     * bean 转换
     *
     * @param structureListInfoList
     * @return
     */
    private List<OrganizationalStructureInfoDO> bean2Bean(List<OrganizationalStructureListInfoDTO> structureListInfoList) {
        List<OrganizationalStructureInfoDO> dataList = new ArrayList<>();
        for (OrganizationalStructureListInfoDTO organizationalStructureListInfoDTO : structureListInfoList) {
            DeptListInfoDTO deptListInfoDTO = redisTemplate.opsForValue().get(DeptListInfoDTO.NAME_PREFIX
                    + organizationalStructureListInfoDTO.getDeptId());
            // 排除无效部门数据
            if (Objects.isNull(deptListInfoDTO) || !DeptListInfoDTO.EFF_STATUS.equals(deptListInfoDTO.getEffStatus())) {
                continue;
            }

            OrganizationalStructureInfoDO structureInfoDO = new OrganizationalStructureInfoDO();
            if (StrUtil.isNotEmpty(organizationalStructureListInfoDTO.getDeptId())) {
                structureInfoDO.setDeptId(Integer.valueOf(organizationalStructureListInfoDTO.getDeptId()));
            }

            if (StrUtil.isNotEmpty(organizationalStructureListInfoDTO.getPartDeptIdChn())) {
                structureInfoDO.setPartDeptIdChn(Integer.valueOf(organizationalStructureListInfoDTO.getPartDeptIdChn()));
            }

            structureInfoDO.setDeptAbbreviation(organizationalStructureListInfoDTO.getDeptAbbreviation());
            structureInfoDO.setSuperiorDeptAbbreviation(organizationalStructureListInfoDTO.getSuperiorDeptAbbreviation());
            structureInfoDO.setRowNumber(organizationalStructureListInfoDTO.getRowNumber());
            dataList.add(structureInfoDO);
        }

        return dataList;
    }

}
