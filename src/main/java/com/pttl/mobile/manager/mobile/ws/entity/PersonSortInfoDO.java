package com.pttl.mobile.manager.mobile.ws.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 自定义用户排序表
 *
 * <AUTHOR>
 */
@ApiModel(value = "自定义用户排序")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PersonSortInfoDO implements Serializable {
    @ApiModelProperty(value = "")
    private Integer id;

    /**
     * 部门id
     */
    @ApiModelProperty(value = "部门id")
    private Integer deptId;

    /**
     * 员工id
     */
    @ApiModelProperty(value = "员工id")
    private String employeeId;

    /**
     * 排序 顺序数字, 数字越小越靠前; 0开始
     */
    @ApiModelProperty(value = "排序 顺序数字, 数字越小越靠前; 0开始")
    private Integer sortId;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    private static final long serialVersionUID = 1L;
}