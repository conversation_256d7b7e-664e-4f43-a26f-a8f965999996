package com.pttl.mobile.manager.mobile.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 通讯录 部门及用户查询dto
 *
 * <AUTHOR>
 * @date 2023/1/6
 **/
@Data
@EqualsAndHashCode
@ApiModel(value = "通讯录 部门及用户查询条件vo")
public class AddressBookInfoOfQueryDTO {

    /**
     * 部门d
     */
    @ApiModelProperty(value = "部门id", notes = "有值则检索当前及子部门;为空则检索全部;")
    private Integer deptId;

    /**
     * 名称描述
     */
    @ApiModelProperty(value = "名称描述")
    private String nameDesc;

}
