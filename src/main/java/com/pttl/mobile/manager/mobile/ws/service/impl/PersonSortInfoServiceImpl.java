package com.pttl.mobile.manager.mobile.ws.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.github.pagehelper.PageInfo;
import com.pttl.mobile.manager.domain.dto.AddressBookOfPersonnelRankingDTO;
import com.pttl.mobile.manager.mobile.dto.AddressBookAndPersonDTO;
import com.pttl.mobile.manager.mobile.dto.PersonInfoDTO;
import com.pttl.mobile.manager.mobile.ws.dao.PersonSortInfoMapper;
import com.pttl.mobile.manager.mobile.ws.entity.PersonInfoDO;
import com.pttl.mobile.manager.mobile.ws.entity.PersonSortInfoDO;
import com.pttl.mobile.manager.mobile.ws.service.PersonInfoService;
import com.pttl.mobile.manager.mobile.ws.service.PersonSortInfoService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class PersonSortInfoServiceImpl implements PersonSortInfoService {

    @Resource
    private PersonSortInfoMapper personSortInfoMapper;

    /**
     * 通讯录人员信息
     */
    @Resource
    private PersonInfoService personInfoService;

    @Override
    public int deleteByPrimaryKey(Integer id) {
        return personSortInfoMapper.deleteByPrimaryKey(id);
    }

    @Override
    public int insert(PersonSortInfoDO record) {
        return personSortInfoMapper.insert(record);
    }

    @Override
    public int insertSelective(PersonSortInfoDO record) {
        return personSortInfoMapper.insertSelective(record);
    }

    @Override
    public PersonSortInfoDO selectByPrimaryKey(Integer id) {
        return personSortInfoMapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(PersonSortInfoDO record) {
        return personSortInfoMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(PersonSortInfoDO record) {
        return personSortInfoMapper.updateByPrimaryKey(record);
    }

    @Override
    public int updateBatch(List<PersonSortInfoDO> list) {
        return personSortInfoMapper.updateBatch(list);
    }

    @Override
    public int batchInsert(List<PersonSortInfoDO> list) {
        return personSortInfoMapper.batchInsert(list);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void exchangeEmployeeSort(AddressBookOfPersonnelRankingDTO queryVO) {
        // 交换前 检查 并初始化默认排序
        initPersonSortInfo(queryVO);

        // 重新排序 以防人员增加 或 删除 导致数据不同步
        rearrangement(queryVO);

    }

    /**
     * 重新排序
     *
     * @param queryVO 排序参数
     */
    private void rearrangement(AddressBookOfPersonnelRankingDTO queryVO) {

        // 以防人员增加 或 删除 导致数据不同步 初始化不存在数据
        PersonSortInfoDO personSortInfo1 = personSortInfoMapper.selectByDeptIdAndEmployeeId(queryVO.getDeptId(),
                queryVO.getEmployeeId1());

        Integer count2 = personSortInfoMapper.countByDeptId(queryVO.getDeptId());
        if (Objects.isNull(personSortInfo1)) {
            personSortInfo1 = new PersonSortInfoDO(null, queryVO.getDeptId(), queryVO.getEmployeeId1(), count2 += 1,
                    new Date(), null);
            personSortInfoMapper.insert(personSortInfo1);
        }

        PersonSortInfoDO personSortInfo2 = personSortInfoMapper.selectByDeptIdAndEmployeeId(queryVO.getDeptId(),
                queryVO.getEmployeeId2());

        if (Objects.isNull(personSortInfo2)) {
            personSortInfo2 = new PersonSortInfoDO(null, queryVO.getDeptId(), queryVO.getEmployeeId2(), count2 += 1,
                    new Date(), null);
            personSortInfoMapper.insert(personSortInfo2);
        }

        // 交换
        Integer sortId2 = personSortInfo2.getSortId();
        Integer sortId1 = personSortInfo1.getSortId();

        // 绝对值等于1时为相邻 直接交换即可
        if (Math.abs(sortId1 - sortId2) == 1) {
            // 将被交换的顺序id 交换给 待交换的顺序id
            personSortInfo1.setSortId(sortId2);
            personSortInfo1.setUpdateTime(new Date());
            personSortInfoMapper.updateByPrimaryKey(personSortInfo1);

            personSortInfo2.setSortId(sortId1);
            personSortInfo2.setUpdateTime(new Date());
            personSortInfoMapper.updateByPrimaryKey(personSortInfo2);
            return;
        }

        // 绝对值大于于1时 需要 从被交换顺序id开始, 后续的 进行顺延 +1 处理
        if (Math.abs(sortId1 - sortId2) > 1) {
            // 获取需要修改排序值区间内的数据
            List<PersonSortInfoDO> personSortInfos = personSortInfoMapper.listByDeptIdBetweenSortId(queryVO.getDeptId(),
                    sortId1 < sortId2 ? sortId1 : sortId2, sortId1 > sortId2 ? sortId1 : sortId2);

            if (CollUtil.isNotEmpty(personSortInfos)) {
                ArrayList<PersonSortInfoDO> updateList = CollUtil.newArrayList();
                // 最后一个放到第一个
                PersonSortInfoDO personSortInfoDO = personSortInfos.get(personSortInfos.size() - 1);
                personSortInfoDO.setSortId(sortId1 < sortId2 ? sortId1 : sortId2);
                personSortInfoDO.setUpdateTime(new Date());
                updateList.add(personSortInfoDO);

                // 剩余的 顺延
                for (int i = 0; i < (personSortInfos.size() - 1); i++) {
                    PersonSortInfoDO personSortInfo = personSortInfos.get(i);
                    personSortInfo.setSortId(personSortInfo.getSortId() + 1);
                    personSortInfo.setUpdateTime(new Date());
                    updateList.add(personSortInfo);
                }

                personSortInfoMapper.updateBatch(updateList);
            }
        }
    }

    @Override
    public List<PersonInfoDTO> sortPersonInfoDTO(PageInfo<PersonInfoDTO> dataList) {
        if (Objects.isNull(dataList) || CollUtil.isEmpty(dataList.getList())) {
            return CollUtil.newArrayList();
        }

        List<PersonInfoDTO> personInfos = dataList.getList();
        // 查找已经排序人员信息集合
        List<PersonSortInfoDO> personSortInfos = personSortInfoMapper.listByDeptId(personInfos.get(0).getDeptId());
        if (CollUtil.isEmpty(personSortInfos)) {
            return personInfos;
        }

        // 排序
        ArrayList<PersonInfoDTO> results = CollUtil.newArrayList();
        for (PersonSortInfoDO personSortInfo : personSortInfos) {
            for (PersonInfoDTO personInfo : personInfos) {
                // 存在排序的
                if (personSortInfo.getEmployeeId().equals(personInfo.getEmployeeId())) {
                    results.add(personInfo);
                }
            }
        }

        List<String> employeeIds = results.stream().map(PersonInfoDO::getEmployeeId).collect(Collectors.toList());
        personInfos.forEach(personInfoDTO -> {
            // 不存在排序的 往list后追加
            if (!employeeIds.contains(personInfoDTO.getEmployeeId())) {
                results.add(personInfoDTO);
            }
        });

        return results;
    }

    @Override
    public List<AddressBookAndPersonDTO> sortAddressBookAndPersons(Integer deptId, List<AddressBookAndPersonDTO> dataList) {
        if (Objects.isNull(dataList) || CollUtil.isEmpty(dataList)) {
            return CollUtil.newArrayList();
        }

        // 不需要排序
        if (Objects.isNull(deptId)) {
            return dataList;
        }

        // 查找已经排序人员信息集合
        List<PersonSortInfoDO> personSortInfos = personSortInfoMapper.listByDeptId(deptId);
        // 不需要排序
        if (CollUtil.isEmpty(personSortInfos)) {
            return dataList;
        }

        // 排序
        ArrayList<AddressBookAndPersonDTO> results = CollUtil.newArrayList();
        for (PersonSortInfoDO personSortInfo : personSortInfos) {
            for (AddressBookAndPersonDTO addressBookAndPersonDTO : dataList) {
                // 存在排序的
                if (addressBookAndPersonDTO.getDataType() == AddressBookAndPersonDTO.DATA_TYPE_PERSON
                        && personSortInfo.getEmployeeId().equals(addressBookAndPersonDTO.getEmployeeId())) {
                    results.add(addressBookAndPersonDTO);
                }
            }
        }

        List<String> employeeIds = results.stream().filter(addressBookAndPersonDTO -> addressBookAndPersonDTO.getDataType() == AddressBookAndPersonDTO.DATA_TYPE_PERSON)
                .map(AddressBookAndPersonDTO::getEmployeeId).collect(Collectors.toList());
        dataList.forEach(addressBookAndPersonDTO -> {
            // 没有进行排序的新人员 追加到列表后
            if (addressBookAndPersonDTO.getDataType() == AddressBookAndPersonDTO.DATA_TYPE_PERSON &&
                    !employeeIds.contains(addressBookAndPersonDTO.getEmployeeId())) {
                results.add(addressBookAndPersonDTO);
            }

            // 部门列表 不存在排序的 往list后追加
            if (addressBookAndPersonDTO.getDataType() == AddressBookAndPersonDTO.DATA_TYPE_DEPT) {
                results.add(addressBookAndPersonDTO);
            }
        });
        return results;
    }

    /**
     * 初始化 人员排序
     *
     * @param queryVO 参数
     */
    private void initPersonSortInfo(AddressBookOfPersonnelRankingDTO queryVO) {
        // 部门是否存在 不存在则初始化
        Integer count = personSortInfoMapper.countByDeptId(queryVO.getDeptId());

        if (count == 0) {
            // 不存在则初始化
            List<PersonInfoDO> personInfoList = personInfoService.listByDeptId(queryVO.getDeptId());

            List<PersonSortInfoDO> personSortInfoList = CollUtil.newArrayList();
            for (int i = 0; i < personInfoList.size(); i++) {
                personSortInfoList.add(new PersonSortInfoDO(null, personInfoList.get(i).getDeptId(), personInfoList.get(i).getEmployeeId(), i,
                        new Date(), null));
            }

            // 初始化入库
            personSortInfoMapper.batchInsert(personSortInfoList);
        }

    }

}
