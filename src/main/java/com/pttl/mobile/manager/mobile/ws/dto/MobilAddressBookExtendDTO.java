package com.pttl.mobile.manager.mobile.ws.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 移动端-通讯录扩展dto
 *
 * <AUTHOR>
 */
@ApiModel(value = "移动端-通讯录扩展dto")
@Data
public class MobilAddressBookExtendDTO implements Serializable {

    /**
     * 关联通讯录员工id(employee_id)
     */
    @ApiModelProperty(value = "关联通讯录员工id(employee_id)")
    private String personInfoId;

    /**
     * 人员备注
     */
    @ApiModelProperty(value = "人员备注")
    private String userRemark;

    /**
     * 自定义头像
     */
    @ApiModelProperty(value = "自定义头像")
    private String customAvatar;

    private static final long serialVersionUID = 1L;
}