package com.pttl.mobile.manager.mobile.dto;

import com.pttl.mobile.manager.mobile.ws.entity.PersonInfoDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 通讯录-人员信息
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "通讯录-人员信息")
@Data
public class PersonInfoDTO extends PersonInfoDO implements Serializable {

    /**
     * 头像地址
     */
    @ApiModelProperty(value = "头像地址, 为空时需要移动端自动生成")
    private String headPicture;

    /**
     * 人员备注
     */
    @ApiModelProperty(value = "人员备注")
    private String userRemark;

    /**
     * 通讯录日志开关
     * @see com.pttl.mobile.manager.constant.ContactsLogSwitchEnum code
     */
    @ApiModelProperty(value = "通讯录日志开关, 1-打开, 0-关闭, 默认0")
    private int contactsLogSwitch = 0;

    private static final long serialVersionUID = 1L;
}