package com.pttl.mobile.manager.mobile.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

/**
 * 获取uuid参数vo
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/4
 */
@Data
@ApiModel(value = "移动端 获取token参数vo")
@ToString
public class GetTokenVO {

    @ApiModelProperty(value = "uuid")
    private String uuid;

    @ApiModelProperty(value = "子账号信息")
    private String subAccount;

    @ApiModelProperty(value = "source")
    private String source = "mobile";
}
