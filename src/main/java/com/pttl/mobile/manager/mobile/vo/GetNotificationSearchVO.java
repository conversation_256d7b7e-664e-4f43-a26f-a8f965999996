package com.pttl.mobile.manager.mobile.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 获取通知搜索
 *
 * <AUTHOR>
 * @date 2023/6/20
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "移动端 获取通知搜索 vo")
public class GetNotificationSearchVO extends MobileInformBaseVO {

    @ApiModelProperty(value = "关键词")
    private String keyword;

    @ApiModelProperty(value = "分页")
    private Integer pageNum = 1;

    @ApiModelProperty(value = "分页数量默认10条")
    private Integer pageSize = 10;
}
