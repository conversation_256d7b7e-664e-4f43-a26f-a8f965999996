package com.pttl.mobile.manager.mobile.ws.util;

import com.pttl.mobile.manager.mobile.ws.constant.SoapConstants;

import javax.xml.soap.*;
import java.io.ByteArrayInputStream;
import java.nio.charset.StandardCharsets;
import java.util.Iterator;
import java.util.Objects;


/**
 * soap xml 解析工具类
 *
 * <AUTHOR>
 */
public class SoapUtil {

    private static String resultData;


    public static SoapUtil getInstance() {
        return new SoapUtil();
    }


    public static void main(String[] args) {
        String deptXml = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>" +
                "<soapenv:Envelope   xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:soapenc=\"http://schemas.xmlsoap.org/soap/encoding/\" xmlns:xsd=\"http://www.w3.org/2001/XMLSchema\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\">" +
                "    <soapenv:Body>" +
                "        <InterfaceResponse  xmlns=\"http://xmlns.oracle.com/Enterprise/Tools/schemas\">" +
                "            <HPS_INF_ID>1111</HPS_INF_ID>" +
                "            <HPS_INF_APP>Portal</HPS_INF_APP>" +
                "            <HPS_INF_RESP_DT>2022-12-29 09:24:16</HPS_INF_RESP_DT>" +
                "            <HPS_INF_RESP_STATUS>Sucess</HPS_INF_RESP_STATUS>" +
                "            <HPS_INF_RESP_DATA>hfeF</HPS_INF_RESP_DATA>" +
                "         </InterfaceResponse>" +
                "     </soapenv:Body>" +
                "</soapenv:Envelope>";
        try {
            SOAPMessage msg = formatSoapString(deptXml);
            assert msg != null;
            SOAPBody body = msg.getSOAPBody();
            Iterator iterator = body.getChildElements();
            printBody(iterator, null);
            getBodyResult(iterator);
            System.out.println(resultData);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 把soap字符串格式化为SOAPMessage
     *
     * @param soapString soapString
     * @return SOAPMessage
     * @see [类、类#方法、类#成员]
     */
    public static SOAPMessage formatSoapString(String soapString) {
        MessageFactory msgFactory;
        try {
            msgFactory = MessageFactory.newInstance();
            SOAPMessage reqMsg = msgFactory.createMessage(new MimeHeaders(),
                    new ByteArrayInputStream(soapString.getBytes(StandardCharsets.UTF_8)));
            reqMsg.saveChanges();
            return reqMsg;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }


    private static void printBody(Iterator iterator, String side) {
        while (iterator.hasNext()) {
            Object o = iterator.next();
            if (o != null) {
                SOAPElement element = null;
                try {
                    element = (SOAPElement) o;
                    // 获取响应状态
                    if (Objects.equals(SoapConstants.NODE_NAME_HPS_INF_RESP_STATUS, element.getNodeName())
                            && Objects.nonNull(element.getValue())) {
                        resultData = element.getValue();
                    }

                    // 状态为成功时 覆盖为响应数据
                    if (Objects.equals(SoapConstants.HPS_INF_RESP_STATUS_VALUE, resultData)
                            && Objects.equals(SoapConstants.NODE_NAME_HPS_INF_RESP_DATA, element.getNodeName())) {
                        resultData = element.getValue();
                    }
                } catch (Exception ignored) {
                }

                if (element != null) {
                    printBody(element.getChildElements(), side);
                }
            }
        }
    }

    /**
     * 获取数据
     *
     * @param iterator iterator
     * @return 数据
     */
    private static void getBodyResult(Iterator iterator) {
        while (iterator.hasNext()) {
            Object o = iterator.next();
            if (o != null) {
                SOAPElement element = null;
                try {
                    element = (SOAPElement) o;
                    String nodeName = element.getNodeName();
                    String value = element.getValue();
                    // 成功并且有值时 返回数据
                    if (Objects.equals(SoapConstants.NODE_NAME_HPS_INF_RESP_STATUS, nodeName)
                            && Objects.nonNull(value)) {
                        resultData = value;
                    }
                } catch (Exception ignored) {
                }

                getBodyResult(element.getChildElements());
            }
        }
    }

    /**
     * 获取soap响应成功时 的数据
     *
     * @param soapXml xml
     * @return 成功是数据
     */
    public String getSoapResultData(String soapXml) {
        try {
            SOAPMessage msg = formatSoapString(soapXml);
            assert msg != null;
            SOAPBody body = msg.getSOAPBody();
            Iterator iterator = body.getChildElements();
            printBody(iterator, null);
            return resultData;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}
