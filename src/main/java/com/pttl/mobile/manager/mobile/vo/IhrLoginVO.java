package com.pttl.mobile.manager.mobile.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * IHR登录请求参数VO
 *
 * <AUTHOR>
 * @date 2025/01/08
 **/
@Data
@ApiModel(value = "IHR登录请求参数")
public class IhrLoginVO {

    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID", required = true)
    private String userId;

    /**
     * 用户密码
     */
    @ApiModelProperty(value = "用户密码", required = true)
    private String password;
}
