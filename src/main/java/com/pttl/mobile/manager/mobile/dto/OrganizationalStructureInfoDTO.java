package com.pttl.mobile.manager.mobile.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 组织架构信息
 *
 * <AUTHOR>
 * @date 2023/3/15
 **/
@Data
@EqualsAndHashCode
@ApiModel(value = "通讯录 部门dto")
public class OrganizationalStructureInfoDTO implements Serializable {

    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 上级部门简称
     */
    @ApiModelProperty(value = "上级部门简称")
    private String superiorDeptAbbreviation;

    /**
     * 上级部门ID
     */
    @ApiModelProperty(value = "上级部门ID")
    private Integer partDeptIdChn;

    /**
     * 部门ID
     */
    @ApiModelProperty(value = "部门ID")
    private Integer deptId;

    /**
     * 部门简称
     */
    @ApiModelProperty(value = "部门简称")
    private String deptAbbreviation;

    /**
     * 当前部门人数
     */
    @ApiModelProperty(value = "当前部门人数")
    private Integer amount;

    /**
     * 是否存在子部门
     */
    @ApiModelProperty(value = "是否存在子部门, true存在, null或false不存在")
    private Boolean hasChildDirectory = true;
}
