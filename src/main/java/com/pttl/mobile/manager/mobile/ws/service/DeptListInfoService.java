package com.pttl.mobile.manager.mobile.ws.service;

import java.util.List;

/**
 * 部门列表 服务层
 *
 * <AUTHOR>
 * @date 2022/12/29
 **/

public interface DeptListInfoService {

    /**
     * 处理部门数据写入缓存
     */
    void handleDeptData2Cache();

    /**
     * 解密并转bean
     *
     * @param soapJsonData data
     * @param clazz        clazz
     * @return bean list
     */
    <T> List<T> data2Bean(String soapJsonData, Class<T> clazz);


    /**
     * 获取web Service结果
     *
     * @param infoName 查询数据类型
     * @return xml
     */
    String getWebServiceResult(String infoName);

    /**
     * 根据查询数据类型 返回webService api返回的结果集
     *
     * @param infoNameType 请求类型
     * @param clazz        目标装换类
     * @param <T>          泛型
     * @return 最终处理数据结果
     */
    <T> List<T> dataExtraction(String infoNameType, Class<T> clazz);
}
