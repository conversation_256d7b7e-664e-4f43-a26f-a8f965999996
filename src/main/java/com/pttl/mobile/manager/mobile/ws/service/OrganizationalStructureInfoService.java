package com.pttl.mobile.manager.mobile.ws.service;

import com.pttl.mobile.manager.mobile.dto.AddressBookAndPersonDTO;
import com.pttl.mobile.manager.mobile.dto.AddressBookInfoOfQueryDTO;
import com.pttl.mobile.manager.mobile.dto.OrganizationalStructureInfoDTO;
import com.pttl.mobile.manager.mobile.ws.entity.OrganizationalStructureInfoDO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface OrganizationalStructureInfoService {


    int deleteByPrimaryKey(Long id);

    int insert(OrganizationalStructureInfoDO record);

    int insertSelective(OrganizationalStructureInfoDO record);

    OrganizationalStructureInfoDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(OrganizationalStructureInfoDO record);

    int updateByPrimaryKey(OrganizationalStructureInfoDO record);

    int updateBatch(List<OrganizationalStructureInfoDO> list);

    int batchInsert(List<OrganizationalStructureInfoDO> list);

    /**
     * 请求web service api 并解析解密后数据分批入库
     */
    void automaticSynchronization2Database();

    /**
     * 通讯录获取
     *
     * @param queryVO 查询条件
     * @return results
     */
    List<AddressBookAndPersonDTO> listAddressBookAndPersonInfo(AddressBookInfoOfQueryDTO queryVO);

    /**
     * 根据部门id获取 同级部门信息及人数
     *
     * @param deptId 部门di
     * @return
     */
    List<OrganizationalStructureInfoDTO> listOrganizationalStructureInfo(Integer deptId, String nameDesc);
}
