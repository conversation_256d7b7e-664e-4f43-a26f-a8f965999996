package com.pttl.mobile.manager.mobile.ws.util;

import cn.hutool.http.ContentType;
import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import com.pttl.mobile.manager.mobile.ws.constant.SoapConstants;

/**
 * soap客户端
 *
 * <AUTHOR>
 * @date 2022/12/29
 **/

public class SoapClient {

    /**
     * 接口地址
     */
    private static final String URL = "/PSIGW/PeopleSoftServiceListeningConnector/PSFT_HR/HPS_STD_SERVICE.1.wsdl";

    /**
     * https  or http
     */
    private static final String HTTPS = "https";

    /**
     * 获取web service 查询结果
     *
     * @param urlPrefix 地址前缀  http://ip:port
     * @param id        HPS_INF_ID
     * @param app       HPS_INF_APP
     * @param name      HPS_INF_NAME 查询数据的类型
     * @param action    HPS_INF_ACTION  操作类型 SELECT
     * @return 响应结果
     */
    public static String post(String urlPrefix, String id, String app, String name, String action) {
        //链式构建请求
        HttpRequest httpRequest = HttpRequest.post(urlPrefix + URL);

        // https 忽略证书
        if (urlPrefix.startsWith(HTTPS)) {
            httpRequest.setSSLSocketFactory(SSLUtils.getSSLSocketFactory());
        }

        return httpRequest
                // 头信息，多个头信息多次调用此方法即可
                .header(Header.CONTENT_TYPE, ContentType.TEXT_XML.getValue())
                // 头信息，多个头信息多次调用此方法即可
                .header("soapAction", "HPS_INF_SERVICE_OPR.v1")
                .body(String.format(SoapConstants.REQUIRED_PARAMETER_BASE_XML, id, app, name, action))
                //超时，毫秒
                .timeout(20000)
                .execute().body();
    }

}
