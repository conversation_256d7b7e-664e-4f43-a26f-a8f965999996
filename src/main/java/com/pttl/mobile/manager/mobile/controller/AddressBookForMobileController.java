package com.pttl.mobile.manager.mobile.controller;

import cn.hutool.core.util.StrUtil;
import com.pttl.mobile.manager.config.ApplicationDefinedConfiguration;
import com.pttl.mobile.manager.constant.CommonConstants;
import com.pttl.mobile.manager.lib.ResponseMessage;
import com.pttl.mobile.manager.mobile.dto.PersonInfoDTO;
import com.pttl.mobile.manager.mobile.ws.entity.MobilAddressBookExtendDO;
import com.pttl.mobile.manager.mobile.ws.entity.PersonInfoDO;
import com.pttl.mobile.manager.mobile.ws.service.MobilAddressBookExtendService;
import com.pttl.mobile.manager.mobile.ws.service.OrganizationalStructureInfoService;
import com.pttl.mobile.manager.mobile.ws.service.PersonInfoService;
import com.pttl.mobile.manager.mobile.ws.service.PersonSortInfoService;
import com.pttl.mobile.manager.util.BeanMapper;
import com.pttl.mobile.manager.util.StringUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 移动端 获取通讯录相关接口
 *
 * <AUTHOR>
 * @date 2023/1/3
 **/
@Slf4j
@RestController
@RequestMapping("/mobile/addressBook")
@Api(tags = "移动端-通讯录接口")
public class AddressBookForMobileController {

    /**
     * 人员信息
     */
    @Resource
    private PersonInfoService personInfoService;

    /**
     * 组织架构
     */
    @Resource
    private OrganizationalStructureInfoService organizationalStructureInfoService;

    /**
     * 动态配置
     */
    @Resource
    private ApplicationDefinedConfiguration applicationDefinedConfiguration;

    /**
     * 移动罐通讯录扩展字段
     */
    @Resource
    private MobilAddressBookExtendService mobilAddressBookExtendService;

    /**
     * 通讯录人员排序
     */
    @Resource
    private PersonSortInfoService personSortInfoService;

    /**
     * 公司领导id
     */
    private static final Long COMPANY_LEADERSHIP_DEPARTMENT_ID = 0L;


    /*@ApiOperation(value = "获取人员详情", notes = "获取人员详情信息")
    @GetMapping("/getUserDetail/{id}")
    public ResponseMessage<PersonInfoDTO> getUserDetail(@PathVariable Long id) {
        // 判空
        if (Objects.isNull(id)) {
            return ResponseMessage.error(CommonConstants.ERROR_MESSAGE_PARAMETER_IS_NULL);
        }

        // 领导部门的人员敏感信息脱敏 有可能当前人员非领导部门,但是是领导, 领导身兼数职
        PersonInfoDO personInfoDO = personInfoService.leadershipDesensitizationByPrimaryKey(id);
        if (Objects.isNull(personInfoDO)) {
            return ResponseMessage.error(CommonConstants.ERROR_MESSAGE_DATA_NOT_EXIST);
        }

        return ResponseMessage.ok(getPersonInfoDTO(personInfoDO));
    }*/

    @ApiOperation(value = "我的-获取人员详情", notes = "移动端 我的-获取人员详情信息")
    @GetMapping("/getUserByEmployeeId/{id}")
    public ResponseMessage<PersonInfoDTO> selectByEmployeeId(@PathVariable String id) {
        // 判空
        if (StrUtil.isEmpty(id)) {
            return ResponseMessage.error(CommonConstants.ERROR_MESSAGE_PARAMETER_IS_NULL);
        }

        PersonInfoDO personInfoDO = personInfoService.selectByEmployeeId(id);

        return ResponseMessage.ok(getPersonInfoDTO(personInfoDO));
    }

    /**
     * 获取dto
     *
     * @param personInfoDO do
     * @return 获取dto
     */
    private PersonInfoDTO getPersonInfoDTO(PersonInfoDO personInfoDO) {
        if (Objects.nonNull(personInfoDO)) {
            PersonInfoDTO personInfoDTO = BeanMapper.map(personInfoDO, PersonInfoDTO.class);

            // 检查是否存在自定义扩展 优先使用
            MobilAddressBookExtendDO mobilAddressBookExtend = mobilAddressBookExtendService.getByEmployeeId(personInfoDO.getEmployeeId());
            if (Objects.nonNull(mobilAddressBookExtend)) {
                // 优先使用自定义的 头像
                if (StrUtil.isNotEmpty(mobilAddressBookExtend.getCustomAvatar())) {
                    personInfoDTO.setHeadPicture(StringUtil.getApplicationIconUr(mobilAddressBookExtend.getCustomAvatar(),
                            applicationDefinedConfiguration.getSystemManager().getApplicationIconUrlPrefix()));
                }

                if (StrUtil.isNotEmpty(mobilAddressBookExtend.getUserRemark())) {
                    personInfoDTO.setUserRemark(mobilAddressBookExtend.getUserRemark());
                }
            }

            // 使用默认的
            if (StrUtil.isEmpty(personInfoDTO.getHeadPicture())) {
                personInfoDTO.setHeadPicture(getHeadPicture(personInfoDO.getEmployeeId()));
            }
            return personInfoDTO;
        }

        return new PersonInfoDTO();
    }

    /**
     * 获取人员头像 (移动端 我的 专用)
     *
     * @param id id
     * @return 访问地址
     */
    private String getHeadPicture(String id) {
        // 获取人员头像 (移动端 我的 专用)
        return applicationDefinedConfiguration.getMobileAddressBook().getHeadPictureVisitPreSelf() + id;
    }


    /*@ApiOperation(value = "通讯录列表及条件筛选", notes = "获取录列表,通过名称和部门组织id条件检索功能接口")
    @PostMapping("/getAddressBookList")
    public ResponseMessage<List<AddressBookAndPersonDTO>> getAddressBookListAndPersonListInfo(
            @RequestBody AddressBookInfoOfQueryDTO queryVO) {

        List<AddressBookAndPersonDTO> results = organizationalStructureInfoService.listAddressBookAndPersonInfo(queryVO);

        // 部门id不为空 并且 检索名称为空 时 需要进行排序
        if (Objects.nonNull(queryVO) && Objects.nonNull(queryVO.getDeptId()) && StrUtil.isEmpty(queryVO.getNameDesc())) {
            results = personSortInfoService.sortAddressBookAndPersons(queryVO.getDeptId(), results);
        }
        return ResponseMessage.ok(processDeptCustomAvatar(results));
    }

    *//**
     * 处理 自定义的部门头像
     *
     * @param results 当前数据
     *//*
    private List<AddressBookAndPersonDTO> processDeptCustomAvatar(List<AddressBookAndPersonDTO> results) {
        if (CollUtil.isNotEmpty(results)) {
            results.forEach(addressBookAndPersonDTO -> {
                if (addressBookAndPersonDTO.getDataType() == AddressBookAndPersonDTO.DATA_TYPE_PERSON) {
                    MobilAddressBookExtendDO addressBookExtendDO = mobilAddressBookExtendService
                            .getByEmployeeId(addressBookAndPersonDTO.getEmployeeId());
                    if (Objects.nonNull(addressBookExtendDO)) {
                        addressBookAndPersonDTO.setHeadPicture(StringUtil.getApplicationIconUr(addressBookExtendDO.getCustomAvatar(),
                                applicationDefinedConfiguration.getSystemManager().getApplicationIconUrlPrefix()));
                    }
                }

                // 公司领导部门的头像需要处理
                if (addressBookAndPersonDTO.getDataType() == AddressBookAndPersonDTO.DATA_TYPE_DEPT
                        && COMPANY_LEADERSHIP_DEPARTMENT_ID.equals(addressBookAndPersonDTO.getDataId())) {
                    addressBookAndPersonDTO.setHeadPicture(applicationDefinedConfiguration.getMobileAddressBook()
                            .getHeadPictureVisitPre() + "10000" + FileScanUtil.SUFFIX_PNG);
                }
            });
        }

        return results;
    }

    @ApiOperation(value = "根据员工id(employee_id)新增/修改头像及备注", notes = "根据员工id新增/修改 头像及备注")
    @PostMapping("/updateAddressBookExtend")
    public ResponseMessage<String> updateAddressBookExtend(
            @RequestBody MobilAddressBookExtendDTO queryVO) {
        // 判空
        if (Objects.isNull(queryVO) || StrUtil.isEmpty(queryVO.getPersonInfoId())) {
            return ResponseMessage.error(CommonConstants.ERROR_MESSAGE_PARAMETER_IS_NULL);
        }

        mobilAddressBookExtendService.updateAddressBookExtend(queryVO);

        return ResponseMessage.ok();
    }*/
}
