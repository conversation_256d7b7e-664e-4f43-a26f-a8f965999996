package com.pttl.mobile.manager.mobile.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 获取通知列表
 *
 * <AUTHOR>
 * @date 2023/6/8
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "移动端 获取通知列表 vo")
public class GetNotificationListVO extends MobileInformBaseVO {

    @ApiModelProperty(value = "获取通知类型接口的id")
    private Integer noticeTypeId;

    @ApiModelProperty(value = "获取通知列表的noticeUserId")
    private Integer lastNoticeUserId;

    @ApiModelProperty(value = "分页数量默认10条")
    private Integer pageSize = 10;
}
