package com.pttl.mobile.manager.mobile.ws.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IORuntimeException;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.symmetric.DES;
import com.alibaba.fastjson.JSONObject;
import com.pttl.mobile.manager.config.ApplicationDefinedConfiguration;
import com.pttl.mobile.manager.mobile.ws.constant.SoapConstants;
import com.pttl.mobile.manager.mobile.ws.dto.DeptListInfoDTO;
import com.pttl.mobile.manager.mobile.ws.entity.DeptInfoDO;
import com.pttl.mobile.manager.mobile.ws.service.DeptInfoService;
import com.pttl.mobile.manager.mobile.ws.service.DeptListInfoService;
import com.pttl.mobile.manager.mobile.ws.util.SoapClient;
import com.pttl.mobile.manager.mobile.ws.util.SoapUtil;
import com.pttl.mobile.manager.util.BeanMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 部门列表 服务层 实现
 *
 * <AUTHOR>
 * @date 2022/12/29
 **/
@Slf4j
@Service
public class DeptListInfoServiceImpl implements DeptListInfoService {

    /**
     * 获取配置参数
     */
    @Resource
    private ApplicationDefinedConfiguration applicationDefinedConfiguration;

    @Resource
    private RedisTemplate<String, DeptListInfoDTO> redisTemplate;

    @Resource
    private DeptInfoService deptInfoService;

    /**
     * 处理部门数据写入缓存
     */
    @Override
    public void handleDeptData2Cache() {
        // 获取部门接口结果
        String deptXml = getWebServiceResult(SoapConstants.PARAMETER_HPS_INF_NAME_DEPT);
        log.info("handleDeptData2Cache, deptXml: {}", deptXml);
        // 无结果停止执行
        if (StrUtil.isEmpty(deptXml)) {
            return;
        }

        // 解析结果
        String soapJsonData = SoapUtil.getInstance().getSoapResultData(deptXml);
        log.info("handleDeptData2Cache, soapJsonData: {}", soapJsonData);
        // 无结果停止执行
        if (StrUtil.isEmpty(soapJsonData)) {
            return;
        }

        // 解密并转bean
        List<DeptListInfoDTO> deptListInfo = data2Bean(soapJsonData, DeptListInfoDTO.class);
        log.info("handleDeptData2Cache, deptListInfo: {}", deptListInfo);
        if (CollUtil.isEmpty(deptListInfo)) {
            return;
        }

        // 方便查询这里入库, 用处不大
        deptDataInsertDb(deptListInfo);

        // 数据入缓存  先清除老数据 再新增
        data2Cache(deptListInfo);
    }

    /**
     * 通讯录-部门信息入库
     *
     * @param deptListInfo
     */
    private void deptDataInsertDb(List<DeptListInfoDTO> deptListInfo) {
        List<DeptInfoDO> deptInfoList = BeanMapper.mapList(deptListInfo, DeptInfoDO.class);
        // 入库前先清库
        deptInfoService.cleanTable();
        // 入库
        deptInfoService.batchInsert(deptInfoList);
    }

    /**
     * 数据写入缓存
     *
     * @param deptListInfo
     */
    private void data2Cache(List<DeptListInfoDTO> deptListInfo) {
        // 入库
        Map<String, DeptListInfoDTO> collect = deptListInfo.stream().collect(Collectors
                .toMap(deptListInfoDTO -> DeptListInfoDTO.NAME_PREFIX + deptListInfoDTO.getDeptId(), deptListInfoDTO -> deptListInfoDTO));
        redisTemplate.opsForValue().multiSet(collect);
    }

    /**
     * 解密并转bean
     *
     * @param soapJsonData data
     */
    @Override
    public <T> List<T> data2Bean(String soapJsonData, Class<T> clazz) {
        // 解密
        // 获取日期戳 用于解密时密钥
        String key = DateUtil.format(new Date(), "yyyyMMdd");
        DES des = SecureUtil.des(key.getBytes(StandardCharsets.UTF_8));
        String decryptStr = des.decryptStr(soapJsonData);

        // json 处理
        JSONObject jsonObject = JSONObject.parseObject(decryptStr);
        JSONObject query = jsonObject.getJSONObject("query");
        if (Objects.nonNull(query)) {
            Integer numRows = query.getInteger("numrows");
            if (Objects.nonNull(numRows) && numRows > 0) {
                String rows = query.getString("row");
                return JSONObject.parseArray(rows, clazz);
            }
        }
        return CollUtil.newArrayList();
    }

    /**
     * 获取 WebService
     *
     * @return 结果
     */
    @Override
    public String getWebServiceResult(String infoName) {
        ApplicationDefinedConfiguration.IhrAddressList ihrAddressList = applicationDefinedConfiguration.getIhrAddressList();
        String urlPrefix = ihrAddressList.getUrlPrefix();
        String infoId = ihrAddressList.getInfoId();
        String infoApp = ihrAddressList.getInfoApp();
        String infoAction = ihrAddressList.getInfoAction();

        // 获取 部门信息列表
        try {
            return SoapClient.post(urlPrefix, infoId, infoApp, infoName, infoAction);
        } catch (IORuntimeException runtimeException) {
            log.error("infoName: {}, invoking error, into retry ...", infoName);
            return SoapClient.post(urlPrefix, infoId, infoApp, infoName, infoAction);
        }
    }

    /**
     * 根据查询数据类型 返回webService api返回的结果集
     *
     * @param infoNameType 请求类型
     * @param clazz        目标装换类
     * @param <T>          泛型
     * @return 最终处理数据结果
     */
    @Override
    public <T> List<T> dataExtraction(String infoNameType, Class<T> clazz) {
        log.info("entry data extraction, data type: {}", infoNameType);

        // 获取部门接口结果
        String deptXml = getWebServiceResult(infoNameType);
        log.info("data type: {}, web service api results: {}", infoNameType, deptXml);
        // 无结果停止执行
        if (StrUtil.isEmpty(deptXml)) {
            return CollUtil.newArrayList();
        }

        // 解析结果
        String soapJsonData = SoapUtil.getInstance().getSoapResultData(deptXml);
        log.info("data type: {}, soapJsonData results: {}", infoNameType, soapJsonData);
        // 无结果停止执行
        if (StrUtil.isEmpty(soapJsonData)) {
            return CollUtil.newArrayList();
        }

        // 解密并转bean
        List<T> deptListInfo = data2Bean(soapJsonData, clazz);
        log.info("data type: {}, decrypt and transform bean results-size: {}", infoNameType, deptListInfo.size());
        if (CollUtil.isEmpty(deptListInfo)) {
            return CollUtil.newArrayList();
        }

        log.info("finish data extraction, data type: {}", infoNameType);
        return deptListInfo;
    }
}
