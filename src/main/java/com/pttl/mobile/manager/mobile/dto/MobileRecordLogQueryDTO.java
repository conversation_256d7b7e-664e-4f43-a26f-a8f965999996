package com.pttl.mobile.manager.mobile.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.pttl.mobile.manager.domain.request.BasePageRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * 日志查询
 *
 * <AUTHOR>
 * @date 2023/11/23
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MobileRecordLogQueryDTO extends BasePageRequest implements Serializable {

    /**
     * 登录名称(工号)
     */
    @ApiModelProperty(value = "登录名称(工号)")
    private String loginName;

    /**
     * 类型(error/info)
     */
    @ApiModelProperty(value = "类型(error/info)")
    private String type;

    /**
     * Android/ios
     */
    @ApiModelProperty(value = "Android/ios")
    private String source;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "")
    private Date startLoginTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "")
    private Date endLoginTime;
}
