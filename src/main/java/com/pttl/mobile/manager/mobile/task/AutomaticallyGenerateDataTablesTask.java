package com.pttl.mobile.manager.mobile.task;

import com.pttl.mobile.manager.mobile.util.TableNameDateUtil;
import com.pttl.mobile.manager.service.OperateTableService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 自动生成 移动端操作记录日志数据表task
 * 完整表名 mm_mobile_record_log_{年_月} -> mm_mobile_record_log_2022_8
 *
 * <AUTHOR>
 * @date 2022/11/10
 **/
@Slf4j
@Component
@ConditionalOnProperty(prefix = "schedule.create", name = "enabled", havingValue = "true")
public class AutomaticallyGenerateDataTablesTask {

    /**
     * 操作数据库表 服务
     */
    @Resource
    private OperateTableService operateTableService;

    @Scheduled(cron = "${schedule.create.cron}")
    public void generateMobileRecordLogTable() {
        log.info("start generate mm_mobile_record_log table ...");
        // 获取当前月的 年月份 2022_10
        String currentYearMonth = TableNameDateUtil.getCurrentYearMonth();

        // 补充完整表名 mm_mobile_record_log_2022_10
        String fullTableName = TableNameDateUtil.TABLE_NAME + "_" + currentYearMonth;

        // 检查是否存在
        int existTable = operateTableService.existTable(fullTableName);
        if (existTable > 0) {
            log.info("generate mm_mobile_record_log table, exist table name: {} ", fullTableName);
            return;
        }

        log.info("generate mm_mobile_record_log table, table name: {} ", fullTableName);

        // 生成
        operateTableService.createTableOfMobileRecordLog(fullTableName);

        log.info("generate {} table success ...", fullTableName);
    }

}
