package com.pttl.mobile.manager.mobile.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 上报记录dto
 *
 * <AUTHOR>
 * @date 2022/11/8
 **/
@Data
@ApiModel(value = "上报操作记录dto")
public class MobileRecordLogDocumentVO {

    /**
     * app版本
     */
    @ApiModelProperty(value = "app版本")
    private String appVersion;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String description;

    /**
     * 登陆名称
     */
    @ApiModelProperty(value = "登陆名称")
    private String loginName;

    /**
     * iPhone14,2-15.4.1 系统版本
     */
    @ApiModelProperty(value = "系统版本")
    private String model;

    /**
     * 模块
     */
    @ApiModelProperty(value = "模块")
    private String module;

    /**
     * 发生时间
     */
    @ApiModelProperty(value = "发生时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date loginTime;

    /**
     * 平台 在线商城/在线办公
     */
    @ApiModelProperty(value = "平台")
    private String platform;

    /**
     * 手机系统 Android/IOS
     */
    @ApiModelProperty(value = "手机系统")
    private String source;

    /**
     * 子模块
     */
    @ApiModelProperty(value = "子模块")
    private String subModule;

    /**
     * 系统登陆名称
     */
    @ApiModelProperty(value = "系统登陆名称")
    private String systemLoginName;

    /**
     * 使用次数
     */
    @ApiModelProperty(value = "使用次数")
    private Integer timeUsed;

    /**
     * 类型 Info/Error/success
     */
    @ApiModelProperty(value = "类型")
    private String type;

    @ApiModelProperty(value = "vpn登陆名")
    private String vpnLoginName;
}
