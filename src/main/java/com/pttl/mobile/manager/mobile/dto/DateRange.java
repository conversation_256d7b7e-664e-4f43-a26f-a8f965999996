package com.pttl.mobile.manager.mobile.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 时间范围dto
 *
 * <AUTHOR>
 * @date 2023/11/22
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DateRange {

    private String yearStr;
    private Date startDate;
    private Date endDate;

    @Override
    public String toString() {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return "DateRange{yearStr=" + yearStr +
                ", startDate=" + dateFormat.format(startDate) +
                ", endDate=" + dateFormat.format(endDate) +
                '}';
    }
}
