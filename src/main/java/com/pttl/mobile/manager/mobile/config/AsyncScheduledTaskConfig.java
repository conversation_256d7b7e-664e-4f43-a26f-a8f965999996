package com.pttl.mobile.manager.mobile.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 异步线程池配置 不使用默认的, 默认的会一直创建线程
 * 默认是concurrencyLimit取值为-1，即不启用资源节流
 * 所以
 * Spring默认的线程池simpleAsyncTaskExecutor，
 * 但是Spring更加推荐我们开发者使用ThreadPoolTaskExecutor类来创建线程池,
 * 这里配置为ThreadPoolTaskExecutor线程池
 *
 * <AUTHOR>
 * @date 2022/11/8
 **/
@Slf4j
@Configuration
public class AsyncScheduledTaskConfig {

    /**
     * 核心线程池数
     */
    private static final int CORE_POOL_SIZE = 6;

    /**
     * 最大线程池数
     */
    private static final int MAX_POOL_SIZE = 8;

    /**
     * 任务队列的容量
     */
    private static final int QUEUE_CAPACITY = 100;

    /**
     * 线程池的前缀名称
     */
    private static final String NAME_PREFIX = "save-job-";

    /**
     * 非核心线程的存活时间
     */
    private static final int KEEP_ALIVE_SECONDS = 60;

    @Bean(name = "saveAsync")
    public Executor saveAsync() {
        log.info("start loading async-scheduled configuration ...");

        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 最大线程数
        executor.setMaxPoolSize(MAX_POOL_SIZE);
        // 核心线程数
        executor.setCorePoolSize(CORE_POOL_SIZE);
        // 任务队列的大小
        executor.setQueueCapacity(QUEUE_CAPACITY);
        // 线程前缀名
        executor.setThreadNamePrefix(NAME_PREFIX);
        // 线程存活时间
        executor.setKeepAliveSeconds(KEEP_ALIVE_SECONDS);

        // 拒绝处理策略
        // CallerRunsPolicy()：交由调用方线程运行，比如 main 线程。
        // AbortPolicy()：直接抛出异常。
        // DiscardPolicy()：直接丢弃。
        // DiscardOldestPolicy()：丢弃队列中最老的任务。
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());
        // 线程初始化
        executor.initialize();

        log.info("async-scheduled configuration loading success ...");
        return executor;
    }
}

