package com.pttl.mobile.manager.mobile.ws.dto;


import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

/**
 * 组织架构  数据列表dto
 *
 * <AUTHOR>
 * @date 2022/12/29
 **/
@Data
public class OrganizationalStructureListInfoDTO implements Serializable {

    private static final long serialVersionUID = 9185260444667966753L;

    /**
     * 上级部门简称
     */
    @JSONField(name = "A.DEPT_DESCR")
    private String superiorDeptAbbreviation;

    /**
     * 上级部门ID
     */
    @JSONField(name = "A.PART_DEPTID_CHN")
    private String partDeptIdChn;


    /**
     * 部门ID
     */
    @JSONField(name = "A.DEPTID")
    private String deptId;

    @JSONField(name = "rownumber")
    private Integer rowNumber;

    /**
     * 部门简称
     */
    @JSONField(name = "<PERSON><PERSON>DESCR")
    private String deptAbbreviation;
}
