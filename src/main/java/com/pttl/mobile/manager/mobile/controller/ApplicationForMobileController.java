package com.pttl.mobile.manager.mobile.controller;

import cn.hutool.core.collection.CollUtil;
import com.pttl.mobile.manager.config.ApplicationDefinedConfiguration;
import com.pttl.mobile.manager.domain.dto.ApplicationAdapterClientDTO;
import com.pttl.mobile.manager.domain.dto.ConfigurationClientDTO;
import com.pttl.mobile.manager.domain.dto.ReleaseClientDTO;
import com.pttl.mobile.manager.lib.ResponseMessage;
import com.pttl.mobile.manager.mobile.dto.ApplicationInfoDTO;
import com.pttl.mobile.manager.mobile.dto.ApplicationMobileInfoDTO;
import com.pttl.mobile.manager.service.ApplicationService;
import com.pttl.mobile.manager.service.ClientService;
import com.pttl.mobile.manager.util.StringUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 移动端获取 系统参数属性配置控制层
 *
 * <AUTHOR>
 * @date 2022/10/25
 **/

@Slf4j
@RestController
@RequestMapping("/mobile/app")
@Api(tags = "移动端获取 用户关联应用")
public class ApplicationForMobileController {

    /**
     * Application应用 服务层
     */
    @Resource
    private ApplicationService applicationService;

    /**
     * 适配应用信息
     */
    @Autowired
    private ClientService clientService;


    /**
     * 系统配置参数
     */
    @Resource
    private ApplicationDefinedConfiguration applicationDefinedConfiguration;


    @ApiOperation(value = "获取数据信息", notes = "获取数据信息")
    @GetMapping("/infoList")
    public ResponseMessage<List<ApplicationMobileInfoDTO>> pageInfoList() {
        List<ApplicationInfoDTO> infoList = applicationService.infoListForMobile();

        return ResponseMessage.ok(applicationGrouping(infoList));
    }

    /**
     * 应用分组
     *
     * @param infoList
     * @return
     */
    private List<ApplicationMobileInfoDTO> applicationGrouping(List<ApplicationInfoDTO> infoList) {
        if (CollUtil.isEmpty(infoList)) {
            return CollUtil.newArrayList();
        }

        List<ApplicationMobileInfoDTO> results = new ArrayList<>();

        Map<String, List<ApplicationInfoDTO>> allApplicationGroupMap =
                infoList.stream().collect(Collectors.groupingBy(ApplicationInfoDTO::getApplicationGroupId));
        for (Map.Entry<String, List<ApplicationInfoDTO>> stringListEntry : allApplicationGroupMap.entrySet()) {
            String applicationGroupId = stringListEntry.getKey();
            List<ApplicationInfoDTO> applicationInfoList = stringListEntry.getValue();

            // 设置图片访问地址
            String applicationIconUrlPrefix = applicationDefinedConfiguration.getSystemManager().getApplicationIconUrlPrefix();
            applicationInfoList.forEach(applicationInfoDTO -> {
                applicationInfoDTO.setVisitLogoUrl(StringUtil.getApplicationIconUr(applicationInfoDTO.getLogoUrl(),
                        applicationIconUrlPrefix));
                applicationInfoDTO.setPackageUrl(StringUtil.getApplicationIconUr(applicationInfoDTO.getPackageUrl(),
                        applicationIconUrlPrefix));
            });

            // 应用排序
            applicationInfoList = applicationInfoList.stream().sorted(Comparator.comparing(
                    ApplicationInfoDTO::getApplicationWeight,
                    Comparator.nullsLast(Comparator.reverseOrder())))
                    .collect(Collectors.toList());

            ApplicationInfoDTO applicationInfoDTO = applicationInfoList.get(0);
            ApplicationMobileInfoDTO applicationMobileInfoDTO = new ApplicationMobileInfoDTO();
            applicationMobileInfoDTO.setApplicationGroupId(applicationGroupId);
            applicationMobileInfoDTO.setApplicationGroupName(applicationInfoDTO.getApplicationGroupName());
            applicationMobileInfoDTO.setWeight(applicationInfoDTO.getWeight());
            applicationMobileInfoDTO.setLastUpdate(applicationInfoDTO.getLastUpdate());
            applicationMobileInfoDTO.setApplicationInfoList(applicationInfoList);
            results.add(applicationMobileInfoDTO);
        }

        return results.stream()
                .sorted((a, b) -> b.getLastUpdate().compareTo(a.getLastUpdate()))
                .collect(Collectors.toList());
    }


    @ApiOperation(value = "获取适配应用信息", notes = "获取应用说明数据, 适配应用信息")
    @PostMapping(value = "/manifest")
    public ResponseMessage<List<ApplicationAdapterClientDTO>> getApplicationManifest(@RequestBody List<String> ids) {
        return clientService.getApplicationManifest(ids);
    }


    @ApiOperation(value = "获取最新的发版信息", notes = "获取最新的发版信息")
    @GetMapping("/release/lastversion")
    public ResponseMessage<ReleaseClientDTO> getReleaseLastVersion(@RequestParam("type") Integer type, @RequestParam("operatingSystem") Integer operatingSystem) {
        return clientService.getReleaseLastversion(type, operatingSystem);
    }

    @ApiOperation(value = "获取客户端需要的配置", notes = "获取客户端需要的配置")
    @GetMapping(value = "/configuration")
    public ResponseMessage<ConfigurationClientDTO> getConfiguration() {
        return clientService.getConfiguration();
    }

    @ApiOperation(value = "获取最新的商城发版信息", notes = "获取最新的商城发版信息")
    @GetMapping("/releaseMall/lastversion")
    public ResponseMessage<ReleaseClientDTO> getReleaseMallLastVersion(@RequestParam("type") Integer type, @RequestParam("operatingSystem") Integer operatingSystem) {
        return clientService.getReleaseMallLastversion(type, operatingSystem);
    }

    @ApiOperation(value = "获取最新的鸿蒙发版信息", notes = "获取最新的鸿蒙发版信息")
    @GetMapping("/releaseHm/lastversion")
    public ResponseMessage<ReleaseClientDTO> getReleaseHongmengLastVersion(@RequestParam("type") Integer type, @RequestParam("operatingSystem") Integer operatingSystem) {
        return clientService.getReleaseHongmengLastversion(type, operatingSystem);
    }
}
