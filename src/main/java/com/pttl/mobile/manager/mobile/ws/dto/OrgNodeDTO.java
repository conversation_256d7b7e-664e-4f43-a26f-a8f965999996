package com.pttl.mobile.manager.mobile.ws.dto;

import lombok.Data;
import lombok.ToString;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 部门节点
 *
 * <AUTHOR>
 * @date 2023/7/20
 **/
@ToString
@Data
public class OrgNodeDTO {
    /**
     * 部门简称
     */
    private String deptAbbreviation;

    /**
     * 部门ID
     */
    private Integer deptId;

    /**
     * 上级部门简称
     */
    private String superiorDeptAbbreviation;

    /**
     * 上级部门ID
     */
    private Integer partDeptIdChn;

    /**
     * 子部门
     */
    private List<OrgNodeDTO> children;

    public OrgNodeDTO(String departmentName, int departmentId, String parentDepartmentName, int partDeptIdChn) {
        this.deptAbbreviation = departmentName;
        this.deptId = departmentId;
        this.superiorDeptAbbreviation = parentDepartmentName;
        this.partDeptIdChn = partDeptIdChn;
        this.children = new ArrayList<>();
    }

    public OrgNodeDTO() {
    }

    public String getDeptAbbreviation() {
        return deptAbbreviation;
    }

    public int getDeptId() {
        return deptId;
    }

    public String getSuperiorDeptAbbreviation() {
        return superiorDeptAbbreviation;
    }

    public int getPartDeptIdChn() {
        return partDeptIdChn;
    }

    public List<OrgNodeDTO> getChildren() {
        return children;
    }

    public void addChild(OrgNodeDTO child) {
        children.add(child);
    }

    /**
     * 获取当前节点的所有子部门ID
     *
     * @return
     */
    public Set<Integer> getAllDepartmentIds() {
        Set<Integer> departmentIds = new HashSet<>();
        departmentIds.add(this.getDeptId());
        for (OrgNodeDTO child : children) {
            departmentIds.addAll(child.getAllDepartmentIds());
        }
        return departmentIds;
    }
}

