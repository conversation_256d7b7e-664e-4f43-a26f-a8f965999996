package com.pttl.mobile.manager.mobile.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 移动端用户登录vo
 *
 * <AUTHOR>
 * @date 2022/10/21
 **/
@Data
@ApiModel(value = "移动端用户登录")
public class MobileUserLoginVO {

    /**
     * 用户姓名
     */
    @ApiModelProperty(value = "用户登录名, 使用UTF-8编码的Base64进行编码")
    private String userName;

    /**
     * 用户登录密码
     */
    @ApiModelProperty(value = "用户登录密码, 使用RSA加密方式进行加密")
    private String password;

}
