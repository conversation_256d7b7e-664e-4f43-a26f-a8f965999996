package com.pttl.mobile.manager.mobile.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

/**
 * 获取uuid参数vo
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/4
 */
@Data
@ApiModel(value = "移动端 获取uuid参数vo")
@ToString
public class GetUuidVO {

    @ApiModelProperty(value = "工号")
    private String account;

    @ApiModelProperty(value = "密码  （需要中台key进行SM2加密）")
    private String password;

    @ApiModelProperty(value = "source")
    private String source = "mobile";
}
