package com.pttl.mobile.manager.mobile.ws.task;

import com.pttl.mobile.manager.mobile.ws.service.DeptListInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 部门信息
 * 自动请求数据接口 并完成解析解密后入库
 *
 * <AUTHOR>
 * @date 2023/11/14
 **/
@Slf4j
@Component
@ConditionalOnProperty(prefix = "schedule.dept-info", name = "enabled", havingValue = "true")
public class RequestDataInterfaceAndStoreOfDeptInfoTask {

    /**
     * 部门信息 服务
     */
    @Resource
    private DeptListInfoService deptListInfoService;

    @Scheduled(cron = "${schedule.dept-info.cron}")
    public void requestDataToBeParsedAndStored() {
        log.info("start RequestDataInterfaceAndStoreOfDeptInfoTask ...");

        deptListInfoService.handleDeptData2Cache();

        log.info("RequestDataInterfaceAndStoreOfDeptInfoTask success ...");
    }

}
