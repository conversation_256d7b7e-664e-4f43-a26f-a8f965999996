package com.pttl.mobile.manager.mobile.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.pttl.mobile.manager.config.ApplicationDefinedConfiguration;
import com.pttl.mobile.manager.lib.ResponseMessage;
import com.pttl.mobile.manager.mobile.encrypt.DesUtils;
import com.pttl.mobile.manager.mobile.service.LoginThirdPartyService;
import com.pttl.mobile.manager.mobile.util.JwtUtil;
import com.pttl.mobile.manager.mobile.vo.*;
import com.pttl.mobile.manager.util.HttpClientUtil;
import com.pttl.mobile.manager.util.RSAUtil;
import com.pttl.mobile.manager.util.RedisUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 移动端 第三方登录
 *
 * <AUTHOR>
 * @date 2023/5/26
 **/

@Slf4j
@RestController
@RequestMapping("/mobile/tp")
@Api(tags = "移动端 第三方登录 信息获取")
public class LoginThirdPartyController {

    /**
     * 第三方登陆服务层
     */
    @Resource
    private LoginThirdPartyService loginThirdPartyService;

    /**
     * JWT工具类
     */
    @Resource
    private JwtUtil jwtUtil;

    /**
     * Redis工具类
     */
    @Resource
    private RedisUtil redisUtil;

    /**
     * 系统配置参数
     */
    @Resource
    private ApplicationDefinedConfiguration applicationDefinedConfiguration;


    @PostMapping("/getBpmCookie")
    @ApiOperation(value = "BpmCookie信息获取", notes = "移动端 bpm cookie信息获取")
    public ResponseMessage<Object> getBpmCookie(@RequestBody MobileThirdPartyLoginVO partyLoginVO) {
        // 校验参数
        if (Objects.isNull(partyLoginVO) || StrUtil.isEmpty(partyLoginVO.getUserName())
                || StrUtil.isEmpty(partyLoginVO.getPassword())) {
            return ResponseMessage.error("请输入用户名或密码");
        }

        List<String> bpmCookie = loginThirdPartyService.getBpmCookie(partyLoginVO);

        // 为空则为失败
        if (CollUtil.isEmpty(bpmCookie)) {
            return ResponseMessage.error("登录失败");
        }

        return ResponseMessage.ok(bpmCookie);
    }

    @PostMapping("/getVcrmCookie")
    @ApiOperation(value = "VcrmCookie信息获取", notes = "移动端 vcrm cookie信息获取")
    public ResponseMessage<Object> getVcrmCookie(@RequestBody MobileThirdPartyLoginVO partyLoginVO) {
        // 校验参数
        if (Objects.isNull(partyLoginVO) || StrUtil.isEmpty(partyLoginVO.getUserName())
                || StrUtil.isEmpty(partyLoginVO.getPassword())) {
            return ResponseMessage.error("请输入用户名或密码");
        }

        // 获取vcrm cookie
        List<String> vcrmCookie = loginThirdPartyService.getVcrmCookieOrInformCookie(partyLoginVO);

        // 为空则为失败
        if (CollUtil.isEmpty(vcrmCookie)) {
            return ResponseMessage.error("登录失败");
        }

        return ResponseMessage.ok(vcrmCookie);
    }

    @PostMapping("/getCookie")
    @ApiOperation(value = "各个系统Cookie信息获取", notes = "移动端 各个系统Cookie信息获取")
    public ResponseMessage<Object> getCookie(@RequestBody MobileThirdPartyLoginVO partyLoginVO) {
        // 校验参数
        if (Objects.isNull(partyLoginVO) || StrUtil.isEmpty(partyLoginVO.getUserName())
                || StrUtil.isEmpty(partyLoginVO.getPassword()) || Objects.isNull(partyLoginVO.getSysType())) {
            return ResponseMessage.error("用户名/密码/系统类型不能为空");
        }

        // 数据解密
        processDecode(partyLoginVO);

        // 获取各个系统Cookie信息
        Object sysCookie = loginThirdPartyService.getSysCookieBySysType(partyLoginVO);

        // 为空则为失败
        if (Objects.isNull(sysCookie)) {
            return ResponseMessage.error("登录失败");
        }

        return ResponseMessage.ok(sysCookie);
    }

    @PostMapping("/getCookie4Harmony")
    @ApiOperation(value = "各个系统Cookie信息获取", notes = "鸿蒙版移动端 各个系统Cookie信息获取")
    public ResponseMessage<Object> getCookie4Harmony(@RequestBody MobileThirdPartyLoginVO partyLoginVO) {
        partyLoginVO.setHarmony(true);
        return getCookie(partyLoginVO);
    }

    /**
     * 解密数据
     *
     * @param partyLoginVO 登陆参数
     */
    private void processDecode(MobileThirdPartyLoginVO partyLoginVO) {
        RSAUtil rsaUtil = new RSAUtil();
        try {
            partyLoginVO.setUserName(rsaUtil.decrypt(partyLoginVO.getUserName()));
            partyLoginVO.setPassword(rsaUtil.decrypt(partyLoginVO.getPassword()));
        } catch (Exception e) {
            log.error("decryption failure: {}", partyLoginVO.toString());
            e.printStackTrace();
            throw new RuntimeException("account decryption failure");
        }
    }


    @PostMapping("/bindAuroraPush")
    @ApiOperation(value = "绑定极光推送", notes = "移动端 绑定极光推送")
    public ResponseMessage<Object> bindAuroraPush(@RequestBody BindAuroralPushVO bindAuroralPushVO) {
        // 校验参数
        if (Objects.isNull(bindAuroralPushVO) || StrUtil.isEmpty(bindAuroralPushVO.getCookie())
                || StrUtil.isEmpty(bindAuroralPushVO.getDeviceToken())) {
            return ResponseMessage.error("登录Cookie/deviceToken不能为空");
        }

        // 绑定极光推送  这里不再进行响应体转换操作 因为 与本系统完全一致
        String resultBody = loginThirdPartyService.bindAuroraPush(bindAuroralPushVO);

        log.info("bindAuroraPush result: {}", resultBody);
        if (StrUtil.isEmpty(resultBody)) {
            return ResponseMessage.error("请求异常");
        }

        return JSONObject.parseObject(resultBody, ResponseMessage.class);
    }


    @PostMapping("/getNotificationType")
    @ApiOperation(value = "获取通知类型", notes = "移动端 获取通知类型")
    public ResponseMessage<Object> getNotificationType(@RequestBody MobileInformBaseVO param) {
        // 校验参数
        if (Objects.isNull(param) || StrUtil.isEmpty(param.getCookie())) {
            return ResponseMessage.error("登录Cookie不能为空");
        }

        // 获取通知类型  这里不再进行响应体转换操作 因为 与本系统完全一致
        String resultBody = loginThirdPartyService.getNotificationType(param);

        log.info("getNotificationType result: {}", resultBody);
        if (StrUtil.isEmpty(resultBody)) {
            return ResponseMessage.error("请求异常");
        }

        return JSONObject.parseObject(resultBody, ResponseMessage.class);
    }

    @PostMapping("/getNotificationList")
    @ApiOperation(value = "获取通知列表", notes = "移动端 获取通知列表")
    public ResponseMessage<Object> getNotificationList(@RequestBody GetNotificationListVO param) {
        // 校验参数
        if (Objects.isNull(param) || StrUtil.isEmpty(param.getCookie()) || Objects.isNull(param.getNoticeTypeId())) {
            return ResponseMessage.error("登录Cookie/noticeTypeId不能为空");
        }

        // 获取通知列表  这里不再进行响应体转换操作 因为 与本系统完全一致
        String resultBody = loginThirdPartyService.getNotificationList(param);

        log.info("getNotificationList result: {}", resultBody);
        if (StrUtil.isEmpty(resultBody)) {
            return ResponseMessage.error("请求异常");
        }

        return JSONObject.parseObject(resultBody, ResponseMessage.class);
    }

    @PostMapping("/oneClickRead")
    @ApiOperation(value = "一键已读", notes = "移动端 一键已读")
    public ResponseMessage<Object> oneClickRead(@RequestBody OneClickReadVO param) {
        // 校验参数
        if (Objects.isNull(param) || StrUtil.isEmpty(param.getCookie()) || Objects.isNull(param.getNoticeTypeId())) {
            return ResponseMessage.error("登录Cookie/noticeTypeId不能为空");
        }

        // 一键已读  这里不再进行响应体转换操作 因为 与本系统完全一致
        String resultBody = loginThirdPartyService.oneClickRead(param);

        log.info("oneClickRead result: {}", resultBody);
        if (StrUtil.isEmpty(resultBody)) {
            return ResponseMessage.error("请求异常");
        }

        return JSONObject.parseObject(resultBody, ResponseMessage.class);
    }

    @PostMapping("/read")
    @ApiOperation(value = "已读", notes = "移动端 已读")
    public ResponseMessage<Object> read(@RequestBody ReadVO param) {
        // 校验参数
        if (Objects.isNull(param) || StrUtil.isEmpty(param.getCookie()) || Objects.isNull(param.getNoticeUserId())) {
            return ResponseMessage.error("登录Cookie/noticeUserId不能为空");
        }

        // 已读  这里不再进行响应体转换操作 因为 与本系统完全一致
        String resultBody = loginThirdPartyService.processRead(param);

        log.info("read result: {}", resultBody);
        if (StrUtil.isEmpty(resultBody)) {
            return ResponseMessage.error("请求异常");
        }

        return JSONObject.parseObject(resultBody, ResponseMessage.class);
    }

    @PostMapping("/bpmRedDots")
    @ApiOperation(value = "BPM红点显示", notes = "移动端 BPM红点显示")
    public ResponseMessage<String> bpmIsDisplayedWithRedDots(@RequestBody BpmRedDotsVO param) {
        // 校验参数
        if (Objects.isNull(param) || StrUtil.isEmpty(param.getLoginName())) {
            return ResponseMessage.error("BPM用户名不能为空");
        }

        // BPM红点显示  这里不再进行响应体转换操作 因为 与本系统完全一致
        String resultBody = loginThirdPartyService.bpmIsDisplayedWithRedDots(param);

        log.info("bpmIsDisplayedWithRedDots result: {}", resultBody);
        if (StrUtil.isEmpty(resultBody)) {
            return ResponseMessage.error("请求异常");
        }

        return readRedDotsResult(resultBody);
    }

    /**
     * 处理红点显示结果
     *
     * @param resultBody 响应体
     * @return 统一格式
     */
    private ResponseMessage<String> readRedDotsResult(String resultBody) {
        JSONObject resultBodyJsonObject = JSONObject.parseObject(resultBody);
        String resultCode = resultBodyJsonObject.getString("ResultCode");
        String resultMsg = resultBodyJsonObject.getString("ResultMsg");
        String newCount = resultBodyJsonObject.getString("NewCount");
        int anInt = Integer.parseInt(resultCode);
        return new ResponseMessage<>(anInt == 0 ? 200 : anInt, resultMsg, newCount);
    }

    @PostMapping("/getNotificationSearch")
    @ApiOperation(value = "获取通知搜索", notes = "移动端 获取通知搜索")
    public ResponseMessage<Object> getNotificationSearch(@RequestBody GetNotificationSearchVO param) {
        // 校验参数
        if (Objects.isNull(param) || StrUtil.isEmpty(param.getCookie()) || Objects.isNull(param.getKeyword())) {
            return ResponseMessage.error("登录Cookie/keyword不能为空");
        }

        // 获取通知搜索  这里不再进行响应体转换操作 因为 与本系统完全一致
        String resultBody = loginThirdPartyService.getNotificationSearch(param);

        log.info("getNotificationSearch result: {}", resultBody);
        if (StrUtil.isEmpty(resultBody)) {
            return ResponseMessage.error("请求异常");
        }

        return JSONObject.parseObject(resultBody, ResponseMessage.class);
    }

    @PostMapping("/ihrLogin")
    @ApiOperation(value = "IHR登录", notes = "移动端 IHR登录")
    public ResponseMessage<Object> ihrLogin(@RequestBody IhrLoginVO ihrLoginVO) {
        // 校验参数
        if (Objects.isNull(ihrLoginVO) || StrUtil.isEmpty(ihrLoginVO.getUserId())
                || StrUtil.isEmpty(ihrLoginVO.getPassword())) {
            return ResponseMessage.error("用户ID/密码不能为空");
        }

        try {
            // 获取IHR登录配置
            ApplicationDefinedConfiguration.IhrLoginClient ihrLoginClient = applicationDefinedConfiguration.getIhrLoginClient();

            // DES加密用户ID和密码
            String encryptedUserId = DesUtils.desEncrypt(ihrLoginVO.getUserId(), ihrLoginClient.getDesKey());
            String encryptedPassword = DesUtils.desEncrypt(ihrLoginVO.getPassword(), ihrLoginClient.getDesKey());

            // 构建请求参数
            JSONObject bodyJsonObject = new JSONObject();
            bodyJsonObject.put("Flag", "mobile");
            bodyJsonObject.put("Password", encryptedPassword);
            bodyJsonObject.put("UserId", encryptedUserId);

            JSONObject requestJsonObject = new JSONObject();
            requestJsonObject.put("Body", bodyJsonObject);

            // 设置请求头
            Map<String, String> headerParam = new HashMap<>(1);
            headerParam.put("X-TLSI-APPKEY", ihrLoginClient.getAppKey());

            // 调用第三方IHR登录接口
            String resultBody = HttpClientUtil.doPostJsonAndHeader(ihrLoginClient.getUrlAddress(),
                    requestJsonObject.toJSONString(), headerParam);

            log.info("IHR login result: {}", resultBody);
            if (StrUtil.isEmpty(resultBody)) {
                return ResponseMessage.error("IHR登录请求异常");
            }

            // 解析响应结果
            JSONObject resultJsonObject = JSONObject.parseObject(resultBody);
            String resultCode = resultJsonObject.getString("ResultCode");

            if (!"0".equals(resultCode)) {
                String resultMsg = resultJsonObject.getString("ResultMsg");
                return ResponseMessage.error("IHR登录失败: " + (StrUtil.isEmpty(resultMsg) ? "未知错误" : resultMsg));
            }

            // 登录成功，生成token并存储到Redis
            String token = generateAndStoreToken(ihrLoginVO.getUserId(), resultJsonObject, ihrLoginClient);

            // 将token整合到响应体中
            JSONObject responseData = new JSONObject();
            responseData.put("token", token);
            responseData.put("ihrLoginResult", resultJsonObject);

            return ResponseMessage.ok(responseData);

        } catch (Exception e) {
            log.error("IHR登录异常: {}", e.getMessage(), e);
            return ResponseMessage.error("IHR登录处理异常: " + e.getMessage());
        }
    }

    /**
     * 生成token并存储到Redis缓存中
     *
     * @param userId IHR用户ID
     * @param ihrResult IHR登录结果
     * @param ihrLoginClient IHR登录配置
     * @return 生成的token
     */
    private String generateAndStoreToken(String userId, JSONObject ihrResult,
                                       ApplicationDefinedConfiguration.IhrLoginClient ihrLoginClient) {
        // 生成唯一的token ID
        String tokenId = IdUtil.simpleUUID();

        // 构建token数据
        JSONObject tokenData = new JSONObject();
        tokenData.put("tokenId", tokenId);
        tokenData.put("userId", userId);
        tokenData.put("email", ihrResult.getString("Email"));
        tokenData.put("punchType", ihrResult.getString("PunchType"));
        tokenData.put("loginTime", System.currentTimeMillis());
        tokenData.put("source", "IHR");

        // 存储到Redis，使用配置文件中的过期时间
        String redisKey = "ihr:login:token:" + tokenId;
        redisUtil.set(redisKey, tokenData.toJSONString(), ihrLoginClient.getTokenExpirationTime());

        log.info("IHR token generated and stored: tokenId={}, userId={}, expirationTime={}s",
                tokenId, userId, ihrLoginClient.getTokenExpirationTime());

        return tokenId;
    }
}
