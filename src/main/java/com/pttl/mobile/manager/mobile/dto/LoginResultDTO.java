package com.pttl.mobile.manager.mobile.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 登录返回结果
 *
 * <AUTHOR>
 * @date 2022/10/21
 **/
@Data
@ApiModel(value = "登录结果dto")
public class LoginResultDTO {

    @ApiModelProperty(value = "登录成功token")
    private String token;

    public LoginResultDTO(String token) {
        this.token = token;
    }

    public LoginResultDTO() {
    }
}
