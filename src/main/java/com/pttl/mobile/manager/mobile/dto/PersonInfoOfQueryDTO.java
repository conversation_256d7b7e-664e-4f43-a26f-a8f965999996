package com.pttl.mobile.manager.mobile.dto;

import com.pttl.mobile.manager.domain.request.BasePageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 通讯录 部门及用户查询dto
 *
 * <AUTHOR>
 * @date 2023/1/6
 **/
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "通讯录 用户查询条件vo")
public class PersonInfoOfQueryDTO extends BasePageRequest {

    /**
     * 部门d
     */
    @ApiModelProperty(value = "部门id", notes = "有值则检索当前部门;为空则检索全部;")
    private Integer deptId;

    /**
     * 名称描述
     */
    @ApiModelProperty(value = "名称/邮箱/电话")
    private String nameDesc;

}
