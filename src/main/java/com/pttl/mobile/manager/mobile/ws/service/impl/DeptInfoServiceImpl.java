package com.pttl.mobile.manager.mobile.ws.service.impl;

import com.pttl.mobile.manager.mobile.ws.dao.DeptInfoMapper;
import com.pttl.mobile.manager.mobile.ws.entity.DeptInfoDO;
import com.pttl.mobile.manager.mobile.ws.service.DeptInfoService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class DeptInfoServiceImpl implements DeptInfoService {

    @Resource
    private DeptInfoMapper deptInfoMapper;

    @Override
    public int deleteByPrimaryKey(Long id) {
        return deptInfoMapper.deleteByPrimaryKey(id);
    }

    @Override
    public int insert(DeptInfoDO record) {
        return deptInfoMapper.insert(record);
    }

    @Override
    public int insertSelective(DeptInfoDO record) {
        return deptInfoMapper.insertSelective(record);
    }

    @Override
    public DeptInfoDO selectByPrimaryKey(Long id) {
        return deptInfoMapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(DeptInfoDO record) {
        return deptInfoMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(DeptInfoDO record) {
        return deptInfoMapper.updateByPrimaryKey(record);
    }

    @Override
    public int updateBatch(List<DeptInfoDO> list) {
        return deptInfoMapper.updateBatch(list);
    }

    @Override
    public int batchInsert(List<DeptInfoDO> list) {
        return deptInfoMapper.batchInsert(list);
    }

    @Override
    public void cleanTable() {
        deptInfoMapper.cleanTable();
    }

}
