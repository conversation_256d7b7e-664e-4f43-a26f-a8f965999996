package com.pttl.mobile.manager.mobile.ws.service;

import com.pttl.mobile.manager.mobile.ws.dto.MobilAddressBookExtendDTO;
import com.pttl.mobile.manager.mobile.ws.entity.MobilAddressBookExtendDO;

import java.util.List;

public interface MobilAddressBookExtendService {


    int deleteByPrimaryKey(Long id);

    int insert(MobilAddressBookExtendDO record);

    int insertSelective(MobilAddressBookExtendDO record);

    MobilAddressBookExtendDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(MobilAddressBookExtendDO record);

    int updateByPrimaryKey(MobilAddressBookExtendDO record);

    int updateBatch(List<MobilAddressBookExtendDO> list);

    int batchInsert(List<MobilAddressBookExtendDO> list);

    /**
     * 根据员工id获取
     *
     * @param employeeId employeeId
     * @return
     */
    MobilAddressBookExtendDO getByEmployeeId(String employeeId);

    /**
     * 根据员工id修改 头像及备注
     *
     * @param queryVO queryVO
     */
    void updateAddressBookExtend(MobilAddressBookExtendDTO queryVO);

    /**
     * 更新日志开关
     * @param employeeId 员工id
     */
    void updateContactsSwitch(String employeeId);

    /**
     * 获取日志开关状态
     * @param employeeId 员工id
     */
    boolean getContactsSwitchStatus(String employeeId);
}
