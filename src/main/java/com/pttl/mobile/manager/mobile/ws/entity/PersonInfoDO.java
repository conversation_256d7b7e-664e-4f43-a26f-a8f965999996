package com.pttl.mobile.manager.mobile.ws.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 通讯录-人员信息
 *
 * <AUTHOR>
 */
@ApiModel(value = "通讯录-人员信息")
@Data
public class PersonInfoDO implements Serializable {

    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 机构ID
     */
    @ApiModelProperty(value = "机构ID")
    private String company;

    /**
     * 邮箱地址
     */
    @ApiModelProperty(value = "邮箱地址")
    private String emailAddress;

    /**
     * 用户姓名
     */
    @ApiModelProperty(value = "用户姓名")
    private String name;

    /**
     * 出生日期
     */
    @ApiModelProperty(value = "出生日期")
    private String birthdate;

    /**
     * 所属部门
     */
    @ApiModelProperty(value = "所属部门")
    private String deptAbbreviation;

    /**
     * 用户岗位
     */
    @ApiModelProperty(value = "用户岗位")
    private String personnelDept;

    @ApiModelProperty(value = "")
    private Integer personCount;

    /**
     * 岗位ID
     */
    @ApiModelProperty(value = "岗位ID")
    private String positionNbr;

    /**
     * 座机号码
     */
    @ApiModelProperty(value = "座机号码")
    private String phone1;

    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话")
    private String phone;

    /**
     * 员工记录号
     */
    @ApiModelProperty(value = "员工记录号")
    private Integer employeeRcd;

    /**
     * 用户状态
     */
    @ApiModelProperty(value = "用户状态")
    private String hrStatus;

    /**
     * 员工职级ID
     */
    @ApiModelProperty(value = "员工职级ID")
    private String jobLevel;

    /**
     * 考勤类型
     */
    @ApiModelProperty(value = "考勤类型")
    private String punchType;

    @ApiModelProperty(value = "")
    private String address;

    /**
     * 性别
     */
    @ApiModelProperty(value = "性别 (M男  F女)")
    private String sex;

    /**
     * 员工id
     */
    @ApiModelProperty(value = "员工id")
    private String employeeId;

    /**
     * rowNumber
     */
    @ApiModelProperty(value = "rowNumber")
    private Integer rowNumber;

    /**
     * 所属部门id
     */
    @ApiModelProperty(value = "所属部门id")
    private Integer deptId;

    /**
     * 用户所属机构
     */
    @ApiModelProperty(value = "用户所属机构")
    private String companyDescription;

    private static final long serialVersionUID = 1L;
}