package com.pttl.mobile.manager.mobile.encrypt;


import sun.misc.BASE64Encoder;

import javax.crypto.Cipher;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.DESKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.Key;
import java.security.Security;


/**
 * des加密工具
 *
 * <AUTHOR>
 */
public class DesUtils {

    static {
        // 添加 默认加密提供者
        Security.addProvider(new org.bouncycastle.jce.provider.BouncyCastleProvider());
    }

    /**
     * des加密
     *
     * @param inputData 数据
     * @param inputKey  密钥
     * @return 密文
     * @throws Exception 异常
     */
    public static String desEncrypt(String inputData, String inputKey) throws Exception {
        // 设置密钥
        byte[] DESkey = fixSize(inputKey.getBytes(StandardCharsets.UTF_8));
        // 设置密钥参数
        DESKeySpec keySpec = new DESKeySpec(DESkey);
        // 获得密钥工厂
        SecretKeyFactory keyFactory = SecretKeyFactory.getInstance("DES");
        // 得到密钥对象
        Key key = keyFactory.generateSecret(keySpec);

        // 得到加密对象Cipher
        Cipher enCipher = Cipher.getInstance("DES/ECB/PKCS7Padding");
        // 设置工作模式为加密模式，给出密钥和向量
        enCipher.init(Cipher.ENCRYPT_MODE, key);
        byte[] pasByte = enCipher.doFinal(inputData.getBytes(StandardCharsets.UTF_8));
        BASE64Encoder base64Encoder = new BASE64Encoder();
        return base64Encoder.encode(pasByte);
    }

    /**
     * fixSize
     *
     * @param input 输入流
     * @return
     */
    private static byte[] fixSize(byte[] input) {
        byte[] output = new byte[8];
        for (int i = 0; i < input.length; i++) {
            output[i & 7] ^= input[i];
        }
        return output;
    }

}
