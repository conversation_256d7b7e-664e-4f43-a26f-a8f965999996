package com.pttl.mobile.manager.mobile.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 移动端用户登录token
 *
 * <AUTHOR>
 * @date 2022/10/21
 **/

@Data
public class UserToken implements Serializable {

    private static final long serialVersionUID = 8798594496773855969L;

    /**
     * token id
     */
    private String id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 用户名称
     */
    private String name;

    /**
     * 登录ip
     */
    private String ip;

    /**
     * 客户端 ua
     */
    private String userAgent;


    /**
     * 授权时间
     */
    private Date issuedAt;


    /**
     * 过期时间
     */
    private Date expiresAt;
}
