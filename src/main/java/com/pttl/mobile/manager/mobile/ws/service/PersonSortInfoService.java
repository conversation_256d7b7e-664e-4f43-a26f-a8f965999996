package com.pttl.mobile.manager.mobile.ws.service;

import com.github.pagehelper.PageInfo;
import com.pttl.mobile.manager.domain.dto.AddressBookOfPersonnelRankingDTO;
import com.pttl.mobile.manager.mobile.dto.AddressBookAndPersonDTO;
import com.pttl.mobile.manager.mobile.dto.PersonInfoDTO;
import com.pttl.mobile.manager.mobile.ws.entity.PersonSortInfoDO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface PersonSortInfoService {


    int deleteByPrimaryKey(Integer id);

    int insert(PersonSortInfoDO record);

    int insertSelective(PersonSortInfoDO record);

    PersonSortInfoDO selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(PersonSortInfoDO record);

    int updateByPrimaryKey(PersonSortInfoDO record);

    int updateBatch(List<PersonSortInfoDO> list);

    int batchInsert(List<PersonSortInfoDO> list);

    /**
     * 交换 员工id排序
     *
     * @param queryVO 参数
     */
    void exchangeEmployeeSort(AddressBookOfPersonnelRankingDTO queryVO);

    /**
     * 排序
     *
     * @param dataList 待排序
     * @return 结果
     */
    List<PersonInfoDTO> sortPersonInfoDTO(PageInfo<PersonInfoDTO> dataList);

    /**
     * 排序 移动端
     *
     * @param dataList 待排序
     * @param deptId   部门id
     * @return 结果
     */
    List<AddressBookAndPersonDTO> sortAddressBookAndPersons(Integer deptId, List<AddressBookAndPersonDTO> dataList);
}
