package com.pttl.mobile.manager.mobile.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;


/**
 * 查询结果vo
 *
 * <AUTHOR>
 * @date 2022/10/25
 **/

@Data
@EqualsAndHashCode
@ApiModel(value = "关联应用-分组查询结果dto")
public class ApplicationMobileInfoDTO {


    /**
     * 应用组id
     */
    @ApiModelProperty(value = "应用组id")
    private String applicationGroupId;

    /**
     * 应用组名
     */
    @ApiModelProperty(value = "应用组名")
    private String applicationGroupName;


    /**
     * 排序级别 越小排在越前面
     */
    @ApiModelProperty(value = "排序级别 越小排在越前面")
    private Integer weight;


    /**
     * 当前组内应用
     */
    @ApiModelProperty(value = "当前组内应用列表")
    private List<ApplicationInfoDTO> applicationInfoList;

    @ApiModelProperty(value = "应用组的修改时间")
    @JsonIgnore
    private Date lastUpdate;
}