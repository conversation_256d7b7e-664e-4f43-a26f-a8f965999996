package com.pttl.mobile.manager.mobile.ws.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@ApiModel(value = "通讯录-部门信息")
@Data
public class DeptInfoDO implements Serializable {

    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 部门简称
     */
    @ApiModelProperty(value = "部门简称")
    private String deptAbbreviationShort;

    /**
     * 部门经理ID
     */
    @ApiModelProperty(value = "部门经理ID")
    private String managerId;

    /**
     * 业务属性
     */
    @ApiModelProperty(value = "业务属性")
    private String servAttAbbreviation;

    @ApiModelProperty(value = "")
    private String effStatus;

    /**
     * 部门全称
     */
    @ApiModelProperty(value = "部门全称")
    private String deptFullName;

    @ApiModelProperty(value = "")
    private Integer sequence;

    /**
     * 成本承担部门ID
     */
    @ApiModelProperty(value = "成本承担部门ID")
    private String deptCost;

    /**
     * 部门类别
     */
    @ApiModelProperty(value = "部门类别")
    private String deptSortAbbreviation;

    @ApiModelProperty(value = "")
    private String deptCount;

    @ApiModelProperty(value = "")
    private Integer rowNumber;

    /**
     * 部门层级
     */
    @ApiModelProperty(value = "部门层级")
    private String deptHieAbbreviation;

    /**
     * 部门ID
     */
    @ApiModelProperty(value = "部门ID")
    private String deptId;

    /**
     * 部门所属公司
     */
    @ApiModelProperty(value = "部门所属公司")
    private String companyAbbreviation;

    private static final long serialVersionUID = 1L;
}