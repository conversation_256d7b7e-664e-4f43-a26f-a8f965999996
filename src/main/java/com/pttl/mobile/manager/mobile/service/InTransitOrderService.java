package com.pttl.mobile.manager.mobile.service;

import com.pttl.mobile.manager.mobile.vo.GetLoginTokenVO;
import com.pttl.mobile.manager.mobile.vo.MobileInformBaseVO;

/**
 * 在途挂单服务层
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/4
 */

public interface InTransitOrderService {

    /**
     * 获取token
     *
     * @param tokenVO 参数
     * @return token对象
     */
    String requestToken(GetLoginTokenVO tokenVO);

    /**
     * 获取VCRM子账号
     *
     * @param cookieVO cookie
     * @return VCRM子账号
     */
    String requestVcrmSubAccount(MobileInformBaseVO cookieVO);
}
