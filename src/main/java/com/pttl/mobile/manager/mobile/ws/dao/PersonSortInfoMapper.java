package com.pttl.mobile.manager.mobile.ws.dao;

import com.pttl.mobile.manager.mobile.ws.entity.PersonSortInfoDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface PersonSortInfoMapper {
    /**
     * delete by primary key
     *
     * @param id primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(PersonSortInfoDO record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(PersonSortInfoDO record);

    /**
     * select by primary key
     *
     * @param id primary key
     * @return object by primary key
     */
    PersonSortInfoDO selectByPrimaryKey(Integer id);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(PersonSortInfoDO record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(PersonSortInfoDO record);

    /**
     * 批量修改
     *
     * @param list 数据
     * @return 结果
     */
    int updateBatch(List<PersonSortInfoDO> list);

    /**
     * 批量新增
     *
     * @param list 数据
     * @return 结果
     */
    int batchInsert(@Param("list") List<PersonSortInfoDO> list);

    /**
     * 根据员工id和部门id查询排序数据
     *
     * @param employeeId 参数 员工id
     * @param deptId     参数 部门di
     * @return 结果
     */
    PersonSortInfoDO selectByDeptIdAndEmployeeId(@Param("deptId") Integer deptId, @Param("employeeId") String employeeId);

    /**
     * 统计部门是否存在排序
     *
     * @param deptId 部门id
     * @return 数量
     */
    Integer countByDeptId(@Param("deptId") Integer deptId);

    /**
     * 根据部门id获取数据
     *
     * @param deptId 部门id
     * @return 结果
     */
    List<PersonSortInfoDO> listByDeptId(Integer deptId);

    /**
     * 获取sortid 区间内的数据
     *
     * @param deptId  部门id
     * @param sortId1 排序id
     * @param sortId2 排序id
     * @return 结果集
     */
    List<PersonSortInfoDO> listByDeptIdBetweenSortId(@Param("deptId") Integer deptId,
                                                     @Param("sortId1") Integer sortId1, @Param("sortId2") Integer sortId2);
}