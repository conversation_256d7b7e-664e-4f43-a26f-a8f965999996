package com.pttl.mobile.manager.mobile.ws.service;

import com.github.pagehelper.PageInfo;
import com.pttl.mobile.manager.mobile.dto.PersonInfoDTO;
import com.pttl.mobile.manager.mobile.dto.PersonInfoOfQueryDTO;
import com.pttl.mobile.manager.mobile.ws.entity.PersonInfoDO;

import java.util.List;

public interface PersonInfoService {


    int deleteByPrimaryKey(Long id);

    int insert(PersonInfoDO record);

    int insertSelective(PersonInfoDO record);

    PersonInfoDO selectByPrimaryKey(Long id);

    /**
     * 根据主键查询, 并将为领导部门的人员手机号脱敏处理
     *
     * @param id 主键
     * @return 详情信息
     */
    PersonInfoDO leadershipDesensitizationByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(PersonInfoDO record);

    int updateByPrimaryKey(PersonInfoDO record);

    int updateBatch(List<PersonInfoDO> list);

    int batchInsert(List<PersonInfoDO> list);

    /**
     * 请求web service api 并解析解密后数据分批入库
     */
    void automaticSynchronization2Database();

    /**
     * 根据名称获取
     *
     * @param nameDesc   名称
     * @param deptIdList deptIdList
     * @return data
     */
    List<PersonInfoDO> listByNameAndDeptIdList(String nameDesc, List<Integer> deptIdList);


    /**
     * 根据员工id获取详情
     *
     * @param employeeId
     * @return
     */
    PersonInfoDO selectByEmployeeId(String employeeId);

    /**
     * 根据部门id 获取部门下的人数
     *
     * @param deptIds ids
     * @return 人数
     */
    Integer countByDeptIds(List<Integer> deptIds);

    /**
     * 获取用户
     *
     * @param queryVO 条件
     * @return 数据
     */
    PageInfo<PersonInfoDTO> listByNameAndDeptId(PersonInfoOfQueryDTO queryVO);

    /**
     * 更加部门id获取 部门下所有
     *
     * @param deptId 部门id
     * @return 结果集
     */
    List<PersonInfoDO> listByDeptId(Integer deptId);
}
