package com.pttl.mobile.manager.mobile.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

import static com.pttl.mobile.manager.mobile.config.ElasticsearchConfig.CONFIGURE_PREFIX;


/**
 * es配置读取
 *
 * <AUTHOR>
 * @date 2022/11/8
 **/

@Data
@Component
@ConfigurationProperties(prefix = CONFIGURE_PREFIX)
public class ElasticsearchConfig {
    public static final String CONFIGURE_PREFIX = "elasticsearch";

    /**
     * es 用户验证名称
     */
    private String esUserName;

    /**
     * es 用户验证密码
     */
    private String esPassword;

    /**
     * host
     */
    private List<String> hostList;

    /**
     * 连接超时
     */
    private Integer connectTimeout;

    /**
     * socket超时
     */
    private Integer socketTimeout;

    /**
     * 请求连接超时
     */
    private Integer connectionRequestTimeout;
}
