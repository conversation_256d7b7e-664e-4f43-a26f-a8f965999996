package com.pttl.mobile.manager.mobile.controller;

import com.pttl.mobile.manager.constant.CommonConstants;
import com.pttl.mobile.manager.lib.ResponseMessage;
import com.pttl.mobile.manager.mobile.dto.FunctionalAttributesInfoDTO;
import com.pttl.mobile.manager.mobile.vo.FunctionalAttributesVO;
import com.pttl.mobile.manager.service.FunctionalAttributesService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * 移动端获取 系统参数属性配置控制层
 *
 * <AUTHOR>
 * @date 2022/10/25
 **/

@Slf4j
@RestController
@RequestMapping("/mobile/configuration")
@Api(tags = "移动端获取 系统参数属性配置")
public class FunctionalAttributesForMobileController {

    /**
     * 系统参数属性配置 服务层
     */
    @Resource
    private FunctionalAttributesService functionalAttributesService;


    @ApiOperation(value = "获取数据信息", notes = "获取数据信息")
    @PostMapping("/infoList")
    public ResponseMessage<List<FunctionalAttributesInfoDTO>> pageInfoList(@RequestBody FunctionalAttributesVO functionalAttributesVO) {
        if (Objects.isNull(functionalAttributesVO) || Objects.isNull(functionalAttributesVO.getDataType())
                || Objects.isNull(functionalAttributesVO.getOperatingSystem())) {
            return ResponseMessage.error(CommonConstants.ERROR_MESSAGE_PARAMETER_IS_NULL);
        }

        List<FunctionalAttributesInfoDTO> infoList = functionalAttributesService.infoListForMobile(functionalAttributesVO);
        return ResponseMessage.ok(infoList);
    }

}
