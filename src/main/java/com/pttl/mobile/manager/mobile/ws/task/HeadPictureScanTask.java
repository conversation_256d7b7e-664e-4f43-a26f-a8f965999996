package com.pttl.mobile.manager.mobile.ws.task;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.pttl.mobile.manager.config.ApplicationDefinedConfiguration;
import com.pttl.mobile.manager.mobile.ws.constant.SoapConstants;
import com.pttl.mobile.manager.mobile.ws.util.FileScanUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;

import javax.annotation.Resource;
import java.io.File;
import java.util.List;

/**
 * 头像扫描  并写入缓存
 *
 * <AUTHOR>
 * @date 2023/1/6
 **/
@Slf4j
//@Component
//@ConditionalOnProperty(prefix = "schedule.head-picture-scan", name = "enabled", havingValue = "true")
public class HeadPictureScanTask {

    /**
     * redis客户端
     */
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 动态配置
     */
    @Resource
    private ApplicationDefinedConfiguration applicationDefinedConfiguration;

    @Scheduled(cron = "${schedule.head-picture-scan.cron}")
    public void requestDataToBeParsedAndStored() {
        log.info("start HeadPictureScanTask ...");

        // 头像扫描目标目录
        List<String> headPictureDirectory = applicationDefinedConfiguration.getMobileAddressBook().getHeadPictureDirectory();

        if (CollUtil.isNotEmpty(headPictureDirectory)) {
            for (String path : headPictureDirectory) {
                if (StrUtil.isEmpty(path)) {
                    continue;
                }

                // 已有头像 id集合
                List<String> idList = FileScanUtil.printFiles(new File(path));
                log.info("HeadPictureScanTask, path: {}, idList size: {}", path, idList.size());

                // 写入缓存
                if (CollUtil.isNotEmpty(idList)) {
                    // 原本想设置过期时间, 现在改为永久有效 (正式环境,定时扫描任务为每天的早6点至下午6点, 每三分钟 扫描一次)
                    idList.forEach(id -> {
                        stringRedisTemplate.opsForValue().set(String.format(SoapConstants.HEAD_PICTURE_ID_KEY_PREFIX, id), id);
                    });
                }
            }

        }

        log.info("HeadPictureScanTask success ...");
    }

}
