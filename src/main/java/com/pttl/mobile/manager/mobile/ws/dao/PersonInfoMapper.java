package com.pttl.mobile.manager.mobile.ws.dao;

import com.pttl.mobile.manager.mobile.dto.PersonInfoDTO;
import com.pttl.mobile.manager.mobile.dto.PersonInfoOfQueryDTO;
import com.pttl.mobile.manager.mobile.ws.entity.PersonInfoDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface PersonInfoMapper {
    /**
     * delete by primary key
     *
     * @param id primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long id);

    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(PersonInfoDO record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(PersonInfoDO record);

    /**
     * select by primary key
     *
     * @param id primary key
     * @return object by primary key
     */
    PersonInfoDO selectByPrimaryKey(Long id);

    /**
     * 根据主键查询, 并将为领导部门的人员手机号脱敏处理
     *
     * @param id 主键
     * @return 详情信息
     */
    PersonInfoDO leadershipDesensitizationByPrimaryKey(Long id);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(PersonInfoDO record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(PersonInfoDO record);

    /**
     * 批量更新
     *
     * @param list list
     * @return
     */
    int updateBatch(List<PersonInfoDO> list);

    /**
     * 批量添加
     *
     * @param list list
     * @return
     */
    int batchInsert(@Param("list") List<PersonInfoDO> list);

    /**
     * 清库数据
     */
    void cleanTable();

    /**
     * 根据名称获取
     *
     * @param nameDesc 名称
     * @return data
     */
    List<PersonInfoDO> listByNameAndDeptIdList(@Param("nameDesc") String nameDesc, @Param("list") List<Integer> list);

    /**
     * 根据原id获取详情
     *
     * @param employeeId
     * @return
     */
    PersonInfoDO selectByEmployeeId(String employeeId);

    /**
     * 根据员工id获取员工详情
     *
     * @param employeeId 工号
     * @return 员工详情
     */
    PersonInfoDO selectOneByEmployeeId(String employeeId);

    /**
     * 根据部门id 获取部门下的人数
     *
     * @param deptIds ids
     * @return 人数
     */
    Integer countByDeptIds(@Param("deptIds") List<Integer> deptIds);

    /**
     * 获取用户
     *
     * @param queryVO 条件
     * @return 数据
     */
    List<PersonInfoDTO> listByNameAndDeptId(@Param("queryVO") PersonInfoOfQueryDTO queryVO);

    /**
     * 根据部门id获取部门下所有人员
     *
     * @param deptId 部门id
     * @return 结果集
     */
    List<PersonInfoDO> listByDeptId(@Param("deptId") Integer deptId);

    /**
     * 通过名称拼音获取用户信息
     *
     * @param loginName 用户名称拼音
     * @return
     */
    PersonInfoDO selectByNamePinYin(@Param("loginName") String loginName);
}