package com.pttl.mobile.manager.mobile.vo;

import com.pttl.mobile.manager.domain.entity.FunctionalAttributesDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode
@ApiModel(value = "移动端属性查询vo")
public class FunctionalAttributesVO implements Serializable {


    private static final long serialVersionUID = -4896233602599739914L;

    /**
     * 属于哪种数据类型: 1:环境参数 2:Webview拦截系统加载地址 3:系统应用描述（/）区分 ; 后续可自己定义
     *
     * @see FunctionalAttributesDO#DATA_TYPE_ENVIRONMENTAL_PARAMETERS
     */
    @ApiModelProperty(value = "属于哪种数据类型: 1:环境参数 2:Webview拦截系统加载地址 3:系统应用描述（/）区分 ; 后续可自己定义")
    private Integer dataType;

    /**
     * 操作系统 1-Android 2-IOS
     */
    @ApiModelProperty(value = "操作系统 1-Android  2-IOS")
    private Integer operatingSystem;

}
