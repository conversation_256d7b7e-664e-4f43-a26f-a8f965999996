package com.pttl.mobile.manager.mobile.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 绑定极光推送
 *
 * <AUTHOR>
 * @date 2023/6/8
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "移动端 绑定极光推送 vo")
public class BindAuroralPushVO extends MobileInformBaseVO {

    @ApiModelProperty(value = "deviceToken")
    private String deviceToken;
}
