package com.pttl.mobile.manager.mobile.ws.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 移动端-通讯录扩展字段 (我方服务端单独保留-非同步)
 *
 * <AUTHOR>
 */
@ApiModel(value = "移动端-通讯录扩展")
@Data
public class MobilAddressBookExtendDO implements Serializable {
    @ApiModelProperty(value = "")
    private Long id;

    /**
     * 关联通讯录员工id(employee_id)
     */
    @ApiModelProperty(value = "关联通讯录员工id(employee_id)")
    private String personInfoId;

    /**
     * 人员备注
     */
    @ApiModelProperty(value = "人员备注")
    private String userRemark;

    /**
     * 自定义头像
     */
    @ApiModelProperty(value = "自定义头像")
    private String customAvatar;

    /**
     * 预留字段
     * 20240618将预留资源作为日志收集开关使用(YES为打开,NO为关闭,默认为NO或者为null)
     * @see com.pttl.mobile.manager.constant.ContactsLogSwitchEnum
     */
    @ApiModelProperty(value = "预留字段")
    private String reservedField1;

    /**
     * 预留字段2
     */
    @ApiModelProperty(value = "预留字段2")
    private String reservedField2;

    /**
     * 预留字段3
     */
    @ApiModelProperty(value = "预留字段3")
    private String reservedField3;

    private static final long serialVersionUID = 1L;
}