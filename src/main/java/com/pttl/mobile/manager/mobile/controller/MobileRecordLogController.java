package com.pttl.mobile.manager.mobile.controller;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.pttl.mobile.manager.constant.CommonConstants;
import com.pttl.mobile.manager.domain.entity.MobileRecordLogDO;
import com.pttl.mobile.manager.lib.ResponseMessage;
import com.pttl.mobile.manager.mobile.document.MobileRecordLogDocument;
import com.pttl.mobile.manager.mobile.dto.LoginStatisticsResult;
import com.pttl.mobile.manager.mobile.dto.MobileRecordLogQueryDTO;
import com.pttl.mobile.manager.mobile.repository.ElasticsearchMobileRecordLogService;
import com.pttl.mobile.manager.mobile.vo.MobileRecordLogDocumentVO;
import com.pttl.mobile.manager.mobile.ws.util.MonthlyDateRangeCalculator;
import com.pttl.mobile.manager.service.MobileRecordLogService;
import com.pttl.mobile.manager.util.BeanMapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;

/**
 * 移动端操作记录控制层
 *
 * <AUTHOR>
 * @date 2022/11/10
 **/
@Slf4j
@RestController
@RequestMapping("/mobile/record")
@Api(tags = "移动端 上报操作记录")
public class MobileRecordLogController {

    /**
     * 操作记录es Service
     */
    @Resource
    private ElasticsearchMobileRecordLogService elasticsearchMobileRecordLogService;

    /**
     * 移动端业务操作记录日志 mysql Service
     */
    @Resource
    private MobileRecordLogService mobileRecordLogService;

    /**
     * 限制月份
     */
    private static final int RESTRICTED_MONTHS = 2;

    @ApiOperation(value = "保存操作记录", notes = "保存当前操作记录详情")
    @PostMapping("/save")
    public ResponseMessage<String> saveInfo(@RequestBody MobileRecordLogDocumentVO mobileRecordLogDocumentVO) {
        // 判空
        if (Objects.isNull(mobileRecordLogDocumentVO)) {
            return ResponseMessage.error(CommonConstants.ERROR_MESSAGE_PARAMETER_IS_NULL);
        }

        MobileRecordLogDocument mobileRecordLogDocument = BeanMapper.map(mobileRecordLogDocumentVO, MobileRecordLogDocument.class);
        mobileRecordLogDocument.setOccurTime(new Date());
        // 异步的方式 es
        //elasticsearchMobileRecordLogService.createDocument(mobileRecordLogDocument);

        // 这里经协商修改为 写入本地 由es那边监听读取日志文件
        log.info("MobileRecordLog.saveInfo(), body: {}", JSONObject.toJSONString(mobileRecordLogDocument));

        // 异步的方式 mysql
        mobileRecordLogService.insert(BeanMapper.map(mobileRecordLogDocument, MobileRecordLogDO.class));
        return ResponseMessage.ok();

    }


    @ApiOperation(value = "ihr登陆统计", notes = "ihr登陆统计数据获取")
    @GetMapping("/ihrLoginStatistics")
    public ResponseMessage<LoginStatisticsResult> ihrLoginStatistics(@RequestParam(value = "dayType", required = false,
            defaultValue = CommonConstants.DAY_TYPE_ONE_DAY) String dayType) {

        return ResponseMessage.ok(mobileRecordLogService.getLoginStatistics(dayType));
    }

    @ApiOperation(value = "登陆日志查询", notes = "登陆日志查询")
    @PostMapping("/logPage")
    public ResponseMessage<PageInfo<MobileRecordLogDO>> logPage(@RequestBody MobileRecordLogQueryDTO mobileRecordLogQueryDTO) {

        // 参数校验
        if (mobileRecordLogQueryDTO.getStartLoginTime() != null && mobileRecordLogQueryDTO.getEndLoginTime() != null) {
            Date startLoginTime = mobileRecordLogQueryDTO.getStartLoginTime();
            boolean flag = MonthlyDateRangeCalculator.formatDate(startLoginTime)
                    .equals(MonthlyDateRangeCalculator.formatDate(mobileRecordLogQueryDTO.getEndLoginTime()));
            if (!flag) {
                return ResponseMessage.error("时间参数异常,非同一个月内");
            }

            // 校验时间范围
            String checkMonthRange = checkMonthRange(startLoginTime);
            if (checkMonthRange != null) {
                return ResponseMessage.error(checkMonthRange);
            }
        }

        PageInfo<MobileRecordLogDO> pageInfo = mobileRecordLogService.pageInfoForMobile(mobileRecordLogQueryDTO);
        return ResponseMessage.ok(pageInfo);
    }

    /**
     * 校验时间范围
     *
     * @param startDate 开始时间
     * @return
     */
    private static String checkMonthRange(Date startDate) {
        int month = DateUtil.month(new Date()) + 1;
        int month2 = DateUtil.month(startDate) + 1;
        if ((month - month2) > RESTRICTED_MONTHS) {
            return "查询范围不能超过三个月";
        }

        return null;
    }

}
