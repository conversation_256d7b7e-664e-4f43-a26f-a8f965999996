package com.pttl.mobile.manager.mobile.task;

import com.pttl.mobile.manager.mobile.util.TableNameDateUtil;
import com.pttl.mobile.manager.service.OperateTableService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 自动删除 移动端操作记录日志数据表task
 *
 * <AUTHOR>
 * @date 2022/11/10
 **/
@Slf4j
@Component
@ConditionalOnProperty(prefix = "schedule.delete", name = "enabled", havingValue = "true")
public class AutomaticallyDeleteDataTablesTask {

    /**
     * 保留近几个月的数据表  数量
     */
    @Value("${schedule.delete.quantity}")
    private Integer quantity;

    /**
     * 操作数据库表 服务
     */
    @Resource
    private OperateTableService operateTableService;

    @Scheduled(cron = "${schedule.delete.cron}")
    public void generateMobileRecordLogTable() {
        log.info("start delete mm_mobile_record_log table, quantity: {} ...", quantity);

        // 默认近两个月  本月和上月
        if (Objects.isNull(quantity)) {
            quantity = 2;
        }

        // 获取保留近几个月的 年月份 2022_10
        String beforeYearMonth = TableNameDateUtil.getBeforeYearMonth(quantity);
        // 补充完整表名 mm_mobile_record_log_2022_10
        String fullTableName = TableNameDateUtil.TABLE_NAME + "_" + beforeYearMonth;

        // 检查是否存在
        int existTable = operateTableService.existTable(fullTableName);
        if (existTable == 0) {
            log.info("delete mm_mobile_record_log table, not exist table name: {} ", fullTableName);
            return;
        }

        log.info("delete mm_mobile_record_log table, table name: {} ", fullTableName);

        // 生成
        operateTableService.dropTable(fullTableName);

        log.info("delete {} table success ...", fullTableName);
    }
}
