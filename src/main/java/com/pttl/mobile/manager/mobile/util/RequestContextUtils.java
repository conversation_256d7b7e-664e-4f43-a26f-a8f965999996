package com.pttl.mobile.manager.mobile.util;


import com.pttl.mobile.manager.mobile.dto.UserToken;

/**
 * <AUTHOR>
 * @date 2022/10/21
 */
public class RequestContextUtils {

    /**
     * 请求头中的nonce
     */
    private static final ThreadLocal<UserToken> CURRENT_USER_INFO = new ThreadLocal<>();


    private RequestContextUtils() {
    }

    public static void clear() {
        removeUserToken();
    }

    public static UserToken getUserToken() {
        return CURRENT_USER_INFO.get();
    }

    public static String getUserRedisKey() {
        return CURRENT_USER_INFO.get().getId();
    }

    public static Long getUserId() {
        return CURRENT_USER_INFO.get().getUserId();
    }

    public static void setUserToken(UserToken userToken) {
        CURRENT_USER_INFO.set(userToken);
    }

    public static void removeUserToken() {
        CURRENT_USER_INFO.remove();
    }


}
