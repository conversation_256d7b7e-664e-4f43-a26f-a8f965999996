package com.pttl.mobile.manager.mobile.config;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;
import java.util.List;

/**
 * es配置
 *
 * <AUTHOR>
 * @date 2022/11/8
 **/
@Slf4j
@Configuration
public class ElasticsearchConfiguration {

    /**
     * 获取es配置
     */
    @Resource
    private ElasticsearchConfig elasticsearchConfig;

    /**
     * 高版本客户端
     *
     * @return RestHighLevelClient
     */
    @Bean(destroyMethod = "close")
    public RestHighLevelClient restHighLevelClient() {
        log.info("start loading elasticsearch configuration ...");

        // 初始化http host
        HttpHost[] httpHostArray = processHttpHostArray();


        // 创建RestHighLevelClient客户端
        RestClientBuilder restClientBuilder = RestClient.builder(httpHostArray).setRequestConfigCallback(requestConfigBuilder -> requestConfigBuilder
                .setConnectTimeout(elasticsearchConfig.getConnectTimeout())
                .setSocketTimeout(elasticsearchConfig.getSocketTimeout())
                .setConnectionRequestTimeout(elasticsearchConfig.getConnectionRequestTimeout()));

        // 设置账号/密码
        if (StrUtil.isNotEmpty(elasticsearchConfig.getEsUserName()) && StrUtil.isNotEmpty(elasticsearchConfig.getEsPassword())) {
            final CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
            credentialsProvider.setCredentials(AuthScope.ANY, new UsernamePasswordCredentials(elasticsearchConfig.getEsUserName(),
                    elasticsearchConfig.getEsPassword()));

            restClientBuilder.setHttpClientConfigCallback(httpClientBuilder -> {
                httpClientBuilder.disableAuthCaching();
                return httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider);
            });
        }


        log.info("elasticsearch configuration loading success ...");
        return new RestHighLevelClient(restClientBuilder);
    }

    /**
     * 项目主要使用 RestHighLevelClient，对于低级的客户端暂时不用
     *
     * @return RestClient
     */
    @Bean
    public RestClient restClient() {
        // 初始化http host
        HttpHost[] httpHostArray = processHttpHostArray();
        return RestClient.builder(httpHostArray).build();
    }

    /**
     * 处理http host数据
     *
     * @return HttpHost
     */
    private HttpHost[] processHttpHostArray() {
        List<String> hostList = elasticsearchConfig.getHostList();

        // es HttpHost
        HttpHost[] httpHostArray = new HttpHost[hostList.size()];
        for (int i = 0; i < hostList.size(); i++) {
            String item = hostList.get(i);
            // 创建 HttpHost 数组，其中存放es主机和端口的配置信息
            httpHostArray[i] = new HttpHost(item.split(":")[0], Integer.parseInt(item.split(":")[1]),
                    HttpHost.DEFAULT_SCHEME_NAME);

        }
        return httpHostArray;
    }
}
