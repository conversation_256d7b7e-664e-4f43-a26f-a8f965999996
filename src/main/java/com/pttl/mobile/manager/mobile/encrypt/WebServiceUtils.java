package com.pttl.mobile.manager.mobile.encrypt;


import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringEscapeUtils;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;
import java.nio.charset.StandardCharsets;

/**
 * Author: Lihe
 * Date: 2022/11/17 14:50
 * Description:
 *
 * <AUTHOR>
 */
@Slf4j
public class WebServiceUtils {

    /**
     * xml节点 状态及结果 字段名称
     */
    private static final String WS_FIELD_CODE = "<ReturenCode>";
    private static final String WS_FIELD_CODE2 = "</ReturenCode>";
    private static final String WS_FIELD_CODE_VALUE = "00";
    private static final String WS_FIELD_BODY = "<ReturenBody>";
    private static final String WS_FIELD_BODY2 = "</ReturenBody>";

    /**
     * 需要检查的字段 及其值
     */
    private static final String WS_FIELD_ROLE_NAME = "roleName";
    private static final String WS_FIELD_ROLE_NAME_VALUE = "RPA";

    /**
     * 获取rpa权限请求
     *
     * @param url    地址
     * @param userId id
     * @return 权限集合
     */
    public static String getRpaRule(String url, String userId) {
        JsonObject jsonObject = new JsonObject();
        jsonObject.addProperty("userId", userId);

        URL wsUrl;
        String result = "";
        try {
            wsUrl = new URL(url);
            HttpURLConnection conn = (HttpURLConnection) wsUrl.openConnection();

            conn.setDoInput(true);
            conn.setDoOutput(true);
            conn.setRequestMethod("POST");
            conn.setRequestProperty("Content-Type", "text/xml;charset=UTF-8");
            conn.setConnectTimeout(2000);
            conn.setReadTimeout(2000);
            OutputStream os = conn.getOutputStream();

            //请求体
            String buffer = "<Envelope xmlns=\"http://schemas.xmlsoap.org/soap/envelope/\"><Body><invoke xmlns=\"http://service.webservice.fix.founder.com/\"><MessageRequest xmlns=\"\">" +
                    "<ApplyNum>API007" + userId + "</ApplyNum><ApplyType>API007</ApplyType><DataBody>" + jsonObject + "</DataBody></MessageRequest></invoke></Body></Envelope>";
            os.write(buffer.getBytes());
            InputStream is = conn.getInputStream();

            byte[] b = new byte[1024];
            int len = 0;
            StringBuilder s = new StringBuilder();
            while ((len = is.read(b)) != -1) {
                String ss = new String(b, 0, len, StandardCharsets.UTF_8);
                s.append(ss);
            }
            result = s.toString();

            is.close();
            os.close();
            conn.disconnect();
        } catch (MalformedURLException e) {
            log.error("WebServiceUtils.getRpaRule 通讯模块1: {}", e.getMessage());
        } catch (IOException e) {
            log.error("WebServiceUtils.getRpaRule 通讯模块2: {}", e.getMessage());
        }

        log.info("WebServiceUtils.getRpaRule result: {}", result);
        return result;
    }

    /**
     * 检查是否有rpa权限
     *
     * @param url    地址
     * @param userId 用户id
     * @return true有权限
     */
    public static Boolean checkRpaAuthority(String url, String userId) {
        String deptXml = getRpaRule(url, userId.toUpperCase());
        if (StrUtil.isEmpty(deptXml)) {
            return false;
        }

        // 检查状态
        int start = deptXml.indexOf(WS_FIELD_CODE) + 13;
        int end = deptXml.indexOf(WS_FIELD_CODE2);
        String resultCode = deptXml.substring(start, end);
        // 状态成功 则继续检查数据权限
        if (WS_FIELD_CODE_VALUE.equals(resultCode)) {
            int start2 = deptXml.indexOf(WS_FIELD_BODY) + 13;
            int end2 = deptXml.indexOf(WS_FIELD_BODY2);
            String resultBody = deptXml.substring(start2, end2);
            if (StrUtil.isEmpty(deptXml)) {
                return false;
            }

            // 反转义符号 否则json序列号失败
            String unescapeJava = StringEscapeUtils.unescapeHtml(resultBody);
            JSONArray jsonArray = JSON.parseArray(unescapeJava);
            for (int i = 0; i < jsonArray.size(); i++) {
                com.alibaba.fastjson.JSONObject jsonDescription = jsonArray.getJSONObject(i);
                if (jsonDescription.getString(WS_FIELD_ROLE_NAME).contains(WS_FIELD_ROLE_NAME_VALUE)) {
                    return true;
                }
            }
        }

        return false;
    }
}
