package com.pttl.mobile.manager.mobile.ws.dao;

import com.pttl.mobile.manager.mobile.ws.entity.MobilAddressBookExtendDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface MobilAddressBookExtendMapper {
    /**
     * delete by primary key
     *
     * @param id primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long id);

    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(MobilAddressBookExtendDO record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(MobilAddressBookExtendDO record);

    /**
     * select by primary key
     *
     * @param id primary key
     * @return object by primary key
     */
    MobilAddressBookExtendDO selectByPrimaryKey(Long id);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(MobilAddressBookExtendDO record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(MobilAddressBookExtendDO record);

    int updateBatch(List<MobilAddressBookExtendDO> list);

    int batchInsert(@Param("list") List<MobilAddressBookExtendDO> list);

    /**
     * 根据员工id 获取
     *
     * @param employeeId 员工id
     * @return
     */
    MobilAddressBookExtendDO getByEmployeeId(@Param("employeeId") String employeeId);
}