package com.pttl.mobile.manager.mobile.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 通讯录 部门及用户dto
 *
 * <AUTHOR>
 * @date 2023/1/6
 **/
@Data
@EqualsAndHashCode
@ApiModel(value = "通讯录 部门及用户dto")
public class AddressBookAndPersonDTO {

    /**
     * 数据类型 部门:1 人员:2
     */
    public static final int DATA_TYPE_DEPT = 1;
    public static final int DATA_TYPE_PERSON = 2;

    /**
     * 数据id
     */
    @ApiModelProperty(value = "数据id, 当数据类型为部门, 则为部门id;当数据类型为人员, 则为人员id;")
    private Long dataId;

    /**
     * 数据类型
     *
     * @see AddressBookAndPersonDTO#DATA_TYPE_DEPT
     */
    @ApiModelProperty(value = "数据类型, 部门:1; 人员:2;")
    private Integer dataType;

    /**
     * 头像地址
     */
    @ApiModelProperty(value = "头像地址, 为空时需要移动端自动生成")
    private String headPicture;

    /**
     * 名称描述
     */
    @ApiModelProperty(value = "名称描述, 当数据类型为部门, 则为部门名称;当数据类型为人员, 则为人员名称;")
    private String nameDesc;

    /**
     * 人员部门名称
     */
    @ApiModelProperty(value = "人员部门名称, 当数据类型为人员时有值")
    private String personDeptName;

    /**
     * 人员岗位名称
     */
    @ApiModelProperty(value = "人员岗位名称, 当数据类型为人员时有值")
    private String personJobName;

    /**
     * 员工id
     */
    @ApiModelProperty(value = "员工id")
    private String employeeId;
}
