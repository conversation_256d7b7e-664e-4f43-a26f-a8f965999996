package com.pttl.mobile.manager.mobile.controller;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.MD5;
import com.google.common.net.HttpHeaders;
import com.pttl.mobile.manager.domain.dto.UserDTO;
import com.pttl.mobile.manager.domain.entity.UserDO;
import com.pttl.mobile.manager.lib.ResponseMessage;
import com.pttl.mobile.manager.mobile.dto.LoginResultDTO;
import com.pttl.mobile.manager.mobile.dto.UserToken;
import com.pttl.mobile.manager.mobile.util.JwtUtil;
import com.pttl.mobile.manager.mobile.vo.MobileUserLoginVO;
import com.pttl.mobile.manager.service.UserService;
import com.pttl.mobile.manager.util.RSAUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.Date;
import java.util.Objects;

/**
 * 移动端登录
 *
 * <AUTHOR>
 * @date 2022/10/21
 **/


@RestController
@RequestMapping("/mobile")
@Api(tags = "移动端登录")
public class LoginController {

    /**
     * 用户服务层
     */
    @Resource
    private UserService userService;

    /**
     * jwt工具
     */
    @Resource
    private JwtUtil jwtUtil;


    @PostMapping("/login")
    @ApiOperation(value = "移动端登录", notes = "移动端登录")
    public ResponseMessage<Object> login(HttpServletRequest request,
                                         @RequestBody MobileUserLoginVO mobileUserLoginVO) {
        // 校验参数
        if (Objects.isNull(mobileUserLoginVO) || StrUtil.isEmpty(mobileUserLoginVO.getUserName())
                || StrUtil.isEmpty(mobileUserLoginVO.getPassword())) {
            return ResponseMessage.error("请输入用户名或密码");
        }

        // 获取用户
        String userName = new String(Base64.getDecoder().decode(mobileUserLoginVO.getUserName()), StandardCharsets.UTF_8);
        UserDTO userDTO = userService.selectByLoginName(userName);
        if (Objects.isNull(userDTO)) {
            return ResponseMessage.error("请检查用户名");
        }

        // 检查用户
        if (UserDO.DISABLED_STATUS_DISABLED == userDTO.getDisabledStatus()) {
            return ResponseMessage.error("用户已被禁用");
        }

        try {
            // 解密密码 这里和manage采取一样的方式
            String password = new RSAUtil().decrypt(mobileUserLoginVO.getPassword());
            // 使用明文md5后的hash串作为密码
            String passwordDigestHex = MD5.create().digestHex(password);
            if (!passwordDigestHex.equalsIgnoreCase(userDTO.getPassword())) {
                return ResponseMessage.error("请核实密码");
            }
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseMessage.error("请核实秘钥");
        }

        // 检查用户是否激活  登陆成功则 自动激活
        userService.userCheckAndActivate(userDTO.getId());

        // 创建用户信息
        UserToken userToken = createUserToken(request, userDTO);

        // 创建token
        String jwtToken = jwtUtil.createToken(userToken);

        return ResponseMessage.ok(new LoginResultDTO(jwtToken));
    }

    /**
     * 为缓存创建userToken
     *
     * @param request
     * @param userDTO
     * @return
     */
    private UserToken createUserToken(HttpServletRequest request, UserDTO userDTO) {
        // 客户端ip（如果是反向代理，要根据情况获取实际的ip）
        String ip = request.getRemoteAddr();
        String userAgent = request.getHeader(HttpHeaders.USER_AGENT);

        // 登录时间
        Date issuedAt = DateUtil.date();


        UserToken userToken = new UserToken();
        // 随机生成uuid，作为token的id
        userToken.setId(IdUtil.simpleUUID());
        userToken.setUserId(userDTO.getId());
        userToken.setIssuedAt(issuedAt);
        userToken.setUserAgent(userAgent);
        userToken.setIp(ip);
        // 新增用户名
        userToken.setName(userDTO.getName());
        return userToken;
    }
}
