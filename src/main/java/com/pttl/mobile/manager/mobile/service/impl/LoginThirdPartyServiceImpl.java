package com.pttl.mobile.manager.mobile.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.ContentType;
import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.pttl.mobile.manager.config.ApplicationDefinedConfiguration;
import com.pttl.mobile.manager.constant.CommonConstants;
import com.pttl.mobile.manager.constant.MobileThirdPartyLoginEnum;
import com.pttl.mobile.manager.lib.ResponseMessage;
import com.pttl.mobile.manager.mobile.dto.BpmCookieInfoDTO;
import com.pttl.mobile.manager.mobile.encrypt.WebServiceUtils;
import com.pttl.mobile.manager.mobile.service.LoginThirdPartyService;
import com.pttl.mobile.manager.mobile.vo.*;
import com.pttl.mobile.manager.mobile.ws.util.SSLUtils;
import com.pttl.mobile.manager.util.AESUtil;
import com.pttl.mobile.manager.util.HttpClientUtil;
import com.pttl.mobile.manager.util.SM2Utils;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.Header;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.util.EntityUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.*;

/**
 * 第三方登陆服务实现层
 *
 * <AUTHOR>
 * @date 2023/5/29
 **/
@Slf4j
@Service
public class LoginThirdPartyServiceImpl implements LoginThirdPartyService {

    /**
     * 默认成功状态码
     */
    private static final int DEFAULT_STATUS_CODE_SUCCESS = 200;

    /**
     * vcrm获取code字段名
     */
    private static final String FIELD_NAME_CODE = "code";
    private static final String FIELD_NAME_BODY = "body";

    /**
     * 系统配置参数
     */
    @Resource
    private ApplicationDefinedConfiguration applicationDefinedConfiguration;

    @Override
    public List<String> getBpmCookie(MobileThirdPartyLoginVO partyLoginVO) {
        ApplicationDefinedConfiguration.BpmCookieClient bpmCookieClient = applicationDefinedConfiguration.getBpmCookieClient();

        // 获取bpm cookie
        Map<String, String> userNameAndPwdParam = new HashMap<>(2);
        userNameAndPwdParam.put("userId", partyLoginVO.getUserName());
        try {
            userNameAndPwdParam.put("password", AESUtil.appointEncrypt(partyLoginVO.getPassword(),
                    bpmCookieClient.getAesKey(), bpmCookieClient.getAesIv()));
        } catch (Exception e) {
            e.printStackTrace();
        }

        return getBpmCookie(userNameAndPwdParam, partyLoginVO.isHarmony() ? bpmCookieClient.getUrlAddressHm() : bpmCookieClient.getUrlAddress(),
                bpmCookieClient.getHeaderMap(), bpmCookieClient.getSuccessStatusCode(), bpmCookieClient.getCookieName());
    }

    @Override
    public List<String> getVcrmCookieOrInformCookie(MobileThirdPartyLoginVO partyLoginVO) {
        String urlAddress;
        ApplicationDefinedConfiguration.VcrmCookieClient vcrmCookieClient = applicationDefinedConfiguration.getVcrmCookieClient();
        // 通知登陆和vcrm登陆大同小异 只有地址不一样
        if (MobileThirdPartyLoginEnum.INFORM.getType() == partyLoginVO.getSysType()) {
            ApplicationDefinedConfiguration.InformCookieClient informCookieClient = applicationDefinedConfiguration.getInformCookieClient();
            urlAddress = informCookieClient.getInformUrlAddress();
        } else {
            urlAddress = partyLoginVO.isHarmony() ? vcrmCookieClient.getUrlAddressHm() : vcrmCookieClient.getUrlAddress();
        }

        // 获取公钥 用来加密密码
        String vcrmPublicKey = getVcrmPublicKey(vcrmCookieClient.getVcrmPublicKeyUrl());
        if (StrUtil.isEmpty(vcrmPublicKey)) {
            return null;
        }

        // 获取vcrm cookie
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("loginName", partyLoginVO.getUserName());
        jsonObject.put("loginPassword", SM2Utils.encryptByPublicKey(partyLoginVO.getPassword(), vcrmPublicKey));

        return getVcrmCookie(urlAddress, jsonObject.toString(), vcrmCookieClient.getCookieName());
    }

    /**
     * 获取vcrm公钥
     *
     * @param vcrmPublicKeyUrl 地址
     * @return 公钥
     */
    private String getVcrmPublicKey(String vcrmPublicKeyUrl) {
        // 获取公钥
        String vcrmPublicKeyResult = HttpClientUtil.doGet(vcrmPublicKeyUrl);
        log.info("getVcrmPublicKey result: {}", vcrmPublicKeyResult);
        if (StrUtil.isEmpty(vcrmPublicKeyResult)) {
            return null;
        }

        JSONObject jsonObject = JSON.parseObject(vcrmPublicKeyResult);
        if (jsonObject.getInteger(FIELD_NAME_CODE).equals(DEFAULT_STATUS_CODE_SUCCESS)) {
            return jsonObject.getString(FIELD_NAME_BODY);
        }

        return null;
    }

    @Override
    public Object getSysCookieBySysType(MobileThirdPartyLoginVO partyLoginVO) {

        // bpm系统
        if (MobileThirdPartyLoginEnum.BPM.getType() == partyLoginVO.getSysType()) {
            // 获取cookie
            List<String> bpmCookie = getBpmCookie(partyLoginVO);
            // 为空则为失败
            if (CollUtil.isEmpty(bpmCookie)) {
                return null;
            }

            BpmCookieInfoDTO bpmCookieInfoDTO = new BpmCookieInfoDTO();
            bpmCookieInfoDTO.setCookieList(bpmCookie);

            // 检查rpa权限
            String rpaUrlAddress = applicationDefinedConfiguration.getBpmCookieClient().getRpaUrlAddress();
            Boolean checkRpaAuthority = WebServiceUtils.checkRpaAuthority(rpaUrlAddress, partyLoginVO.getUserName());
            bpmCookieInfoDTO.setHasRpaAuthority(checkRpaAuthority);

            String menuNameAuthority = getMenuNameAuthority(bpmCookie);
            // 检查 人员项目管理 权限
            bpmCookieInfoDTO.setHasMenuNameAuthority(menuNameAuthority.contains("人员项目管理"));
            // 20241012 新增 RPA流程执行查询 权限校验
            bpmCookieInfoDTO.setHasRpaProcessExecutionPermission(menuNameAuthority.contains("RPA流程执行查询"));
            return bpmCookieInfoDTO;
        }

        // vcrm 系统
        if (MobileThirdPartyLoginEnum.VCRM.getType() == partyLoginVO.getSysType()) {
            return getVcrmCookieOrInformCookie(partyLoginVO);
        }

        // 通知登陆
        if (MobileThirdPartyLoginEnum.INFORM.getType() == partyLoginVO.getSysType()) {
            return getVcrmCookieOrInformCookie(partyLoginVO);
        }

        return null;
    }

    /**
     * 检查菜单权限
     *
     * @param bpmCookie bpm key
     * @return 是否有
     */
    private String getMenuNameAuthority(List<String> bpmCookie) {
        // 检查 人员项目管理 权限
        String menuNameUrlAddress = applicationDefinedConfiguration.getBpmCookieClient().getMenuNameUrlAddress();
        String cookieString = String.join(";", bpmCookie);
        // 链式构建请求
        HttpRequest httpRequest = HttpRequest.post(menuNameUrlAddress);

        // https 忽略证书
        httpRequest.setSSLSocketFactory(SSLUtils.getSSLSocketFactory());

        return httpRequest
                // 头信息，多个头信息多次调用此方法即可
                .header(cn.hutool.http.Header.CONTENT_TYPE, ContentType.JSON.getValue())
                .header(CommonConstants.COOKIE_NAME, cookieString)
                // 超时，毫秒
                .timeout(20000)
                .execute()
                .body();
    }

    @Override
    public String bindAuroraPush(BindAuroralPushVO bindAuroralPushVO) {
        ApplicationDefinedConfiguration.InformCookieClient informCookieClient = applicationDefinedConfiguration.getInformCookieClient();

        // 请求参数
        Map<String, String> param = new HashMap<>(1);
        param.put("deviceToken", bindAuroralPushVO.getDeviceToken());

        // 请求头
        Map<String, String> headerParam = new HashMap<>(1);
        headerParam.put("Cookie", bindAuroralPushVO.getCookie());
        CloseableHttpResponse closeableHttpResponse = HttpClientUtil.doPost(informCookieClient.getAuroraUrlAddress(), param, headerParam);

        // 获取响应体
        return getResponseBody("bindAuroraPush", closeableHttpResponse);
    }

    @Override
    public String getNotificationType(MobileInformBaseVO param) {
        ApplicationDefinedConfiguration.InformCookieClient informCookieClient = applicationDefinedConfiguration.getInformCookieClient();

        // 请求头
        Map<String, String> headerParam = new HashMap<>(1);
        headerParam.put("Cookie", param.getCookie());
        CloseableHttpResponse closeableHttpResponse = HttpClientUtil.doGetOfHeaderParam(informCookieClient.getTypeUrlAddress(), null, headerParam);

        // 获取响应体
        return getResponseBody("getNotificationType", closeableHttpResponse);
    }

    @Override
    public String getNotificationList(GetNotificationListVO param) {
        ApplicationDefinedConfiguration.InformCookieClient informCookieClient = applicationDefinedConfiguration.getInformCookieClient();

        // 请求头
        Map<String, String> headerParam = new HashMap<>(1);
        headerParam.put("Cookie", param.getCookie());

        // 请求参数
        Map<String, String> paramMap = new HashMap<>(1);
        paramMap.put("noticeTypeId", param.getNoticeTypeId().toString());
        if (Objects.nonNull(param.getLastNoticeUserId())) {
            paramMap.put("lastNoticeUserId", param.getLastNoticeUserId().toString());
        }
        paramMap.put("pageSize", param.getPageSize().toString());

        CloseableHttpResponse closeableHttpResponse = HttpClientUtil.doGetOfHeaderParam(informCookieClient.getListUrlAddress(), paramMap, headerParam);

        // 获取响应体
        return getResponseBody("getNotificationList", closeableHttpResponse);
    }

    @Override
    public String oneClickRead(OneClickReadVO param) {
        ApplicationDefinedConfiguration.InformCookieClient informCookieClient = applicationDefinedConfiguration.
                getInformCookieClient();

        // 请求参数
        Map<String, String> paramMap = new HashMap<>(1);
        paramMap.put("noticeTypeId", param.getNoticeTypeId().toString());

        // 请求头
        Map<String, String> headerParam = new HashMap<>(1);
        headerParam.put("Cookie", param.getCookie());

        CloseableHttpResponse closeableHttpResponse = HttpClientUtil.doPost(informCookieClient.getOnceReadUrlAddress(),
                paramMap, headerParam);

        // 获取响应体
        return getResponseBody("oneClickRead", closeableHttpResponse);
    }

    @Override
    public String processRead(ReadVO param) {
        ApplicationDefinedConfiguration.InformCookieClient informCookieClient = applicationDefinedConfiguration.
                getInformCookieClient();

        // 请求参数
        Map<String, String> paramMap = new HashMap<>(1);
        paramMap.put("noticeUserId", param.getNoticeUserId().toString());

        // 请求头
        Map<String, String> headerParam = new HashMap<>(1);
        headerParam.put("Cookie", param.getCookie());

        CloseableHttpResponse closeableHttpResponse = HttpClientUtil.doPost(informCookieClient.getReadUrlAddress(),
                paramMap, headerParam);

        // 获取响应体
        return getResponseBody("processRead", closeableHttpResponse);
    }

    @Override
    public String bpmIsDisplayedWithRedDots(BpmRedDotsVO param) {
        ApplicationDefinedConfiguration.InformCookieClient informCookieClient = applicationDefinedConfiguration.
                getInformCookieClient();

        try {
            String bpmRedDotBody = getBpmRedDotBody(param.getLoginName());

            // 设置请求头
            Map<String, String> headerParam = new HashMap<>(1);
            headerParam.put("X-TLSI-APPKEY", informCookieClient.getRedDotHeaderValue());

            return HttpClientUtil.doPostJsonAndHeader(informCookieClient.getRedDotUrlAddress(), bpmRedDotBody, headerParam);
        } catch (Exception e) {
            log.error("bpmIsDisplayedWithRedDots error: {}", e.getMessage());
            e.printStackTrace();
        }

        return null;
    }

    @Override
    public String getNotificationSearch(GetNotificationSearchVO param) {
        ApplicationDefinedConfiguration.InformCookieClient informCookieClient = applicationDefinedConfiguration.
                getInformCookieClient();

        // 请求头
        Map<String, String> headerParam = new HashMap<>(1);
        headerParam.put("Cookie", param.getCookie());

        // 请求参数
        Map<String, String> paramMap = new HashMap<>(1);
        paramMap.put("keyword", param.getKeyword());
        paramMap.put("pageNum", param.getPageNum().toString());
        paramMap.put("pageSize", param.getPageSize().toString());

        CloseableHttpResponse closeableHttpResponse = HttpClientUtil.doGetOfHeaderParam(informCookieClient.getSearchUrlAddress(), paramMap, headerParam);

        // 获取响应体
        return getResponseBody("getNotificationSearch", closeableHttpResponse);
    }

    /**
     * 获取响应体
     *
     * @param method                方法名
     * @param closeableHttpResponse closeableHttpResponse
     * @return 响应体
     */
    private String getResponseBody(String method, CloseableHttpResponse closeableHttpResponse) {
        // 检查状态码
        int statusCode = closeableHttpResponse.getStatusLine().getStatusCode();
        // token过期等情况
        if (statusCode != DEFAULT_STATUS_CODE_SUCCESS) {
            return JSONObject.toJSONString(ResponseMessage.error(statusCode, "请求异常 请参考状态码"));
        }

        // 获取响应体
        try {
            return EntityUtils.toString(closeableHttpResponse.getEntity(), "utf-8");
        } catch (IOException e) {
            log.error("{} error: {}", method, e.getMessage());
            e.printStackTrace();
        } finally {
            try {
                closeableHttpResponse.close();
            } catch (IOException e) {
                log.error("请求关闭异常：", e);
            }
        }

        return null;
    }


    /**
     * 移动端获取bpm系统cookie
     *
     * @param userNameAndPwdParam 用户参数
     * @param url                 地址
     * @param headerParam         头部参数
     * @param successStatusCode   成功时状态码
     * @param cookieName          要获取的cookie名称
     * @return cookies值
     */
    private List<String> getBpmCookie(Map<String, String> userNameAndPwdParam, String url,
                                      Map<String, String> headerParam, Integer successStatusCode, String cookieName) {
        // 给定默认成功状态码 200
        if (Objects.isNull(successStatusCode)) {
            successStatusCode = DEFAULT_STATUS_CODE_SUCCESS;
        }

        CloseableHttpResponse closeableHttpResponse = HttpClientUtil.doPost(url, userNameAndPwdParam, headerParam);

        // 检查状态码
        int statusCode = closeableHttpResponse.getStatusLine().getStatusCode();
        log.info("getBpmCookie response code: {}", statusCode);
        if (statusCode != successStatusCode) {
            return null;
        }

        return getCookieList(closeableHttpResponse, cookieName);
    }


    /**
     * 获取vcrm cookie
     *
     * @param url        请求地址
     * @param json       参数
     * @param cookieName cookie名称
     * @return cookie
     */
    private List<String> getVcrmCookie(String url, String json, String cookieName) {
        CloseableHttpResponse closeableHttpResponse = HttpClientUtil.doPostJson2(url, json);
        // 检查状态码
        int statusCode = closeableHttpResponse.getStatusLine().getStatusCode();

        log.info("getVcrmCookie response code: {}", statusCode);

        if (statusCode != DEFAULT_STATUS_CODE_SUCCESS) {
            return null;
        }

        // 校验请求体状态码
        try {
            String resultString = EntityUtils.toString(closeableHttpResponse.getEntity(), "utf-8");
            log.info("getVcrmCookie response: {}", resultString);
            JSONObject resultJsonObject = JSON.parseObject(resultString);
            Integer code = resultJsonObject.getInteger("code");
            // 状态为200 成功
            if (Objects.nonNull(code) && (code == DEFAULT_STATUS_CODE_SUCCESS)) {
                // 获取cookie
                return getCookieList(closeableHttpResponse, cookieName);
            }
        } catch (IOException e) {
            log.error("getVcrmCookie 请求异常：", e);
            e.printStackTrace();
        } finally {
            try {
                closeableHttpResponse.close();
            } catch (IOException e) {
                log.error("请求关闭异常：", e);
            }
        }

        return null;
    }

    /**
     * 响应header中获取cookie
     *
     * @param closeableHttpResponse 响应对象
     * @param cookieName            cookieName
     * @return cookie
     */
    private List<String> getCookieList(CloseableHttpResponse closeableHttpResponse, String cookieName) {
        Header[] headers = closeableHttpResponse.getHeaders(cookieName);
        log.info("header cookie size: {}", headers.length);
        List<String> cookieList = new ArrayList<>(headers.length);
        for (Header header : headers) {
            String value = header.getValue();
            cookieList.add(value);
        }

        // 关闭响应
        try {
            closeableHttpResponse.close();
        } catch (IOException e) {
            log.error("请求关闭异常：", e);
        }

        return cookieList;
    }

    /**
     * BPM红点显示 请求所需token
     *
     * @return token
     * @throws Exception 异常
     */
    private static String getBpmRedDotToken(long currentTimeMillis) throws Exception {
        String str = "YDD" + "e9!12893@Y^$B3%^uj6u" + currentTimeMillis;
        InputStream in = new ByteArrayInputStream(str.getBytes(StandardCharsets.UTF_8));
        MessageDigest messageDigest = MessageDigest.getInstance("MD5");
        byte[] buffer = new byte[1024];
        int n = -1;
        while (-1 != (n = in.read(buffer))) {
            messageDigest.update(buffer, 0, n);
        }
        in.close();
        byte[] bytes = messageDigest.digest();
        char[] hexChars = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'};
        int len = bytes.length;
        StringBuilder sb = new StringBuilder();
        for (byte aByte : bytes) {
            char c0 = hexChars[(aByte & 0xf0) >> 4];
            char c1 = hexChars[aByte & 0x0f];
            sb.append(c0).append(c1);
        }
        String md5 = sb.toString();
        return new String(Base64.getEncoder().encode(md5.getBytes()));
    }

    /**
     * 获取请求参数body
     * 20231214更新 参数请求体修改(请求参数移至header)
     *
     * @param loginName BPM用户名
     * @return 请求body体
     * @throws Exception 异常
     */
    private String getBpmRedDotBody(String loginName) {
        JSONObject bodyJsonObject = new JSONObject();

        JSONObject contentBodyJsonObject = new JSONObject();
        // BPM用户名
        contentBodyJsonObject.put("LoginName", loginName);

        // content body参数
        bodyJsonObject.put("Body", contentBodyJsonObject);
        return bodyJsonObject.toJSONString();
    }
}