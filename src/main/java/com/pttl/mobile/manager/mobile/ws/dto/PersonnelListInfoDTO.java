package com.pttl.mobile.manager.mobile.ws.dto;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

/**
 * 人员信息 数据列表dto
 *
 * <AUTHOR>
 * @date 2022/12/29
 **/
@Data
public class PersonnelListInfoDTO implements Serializable {

    /**
     * company
     * 机构ID
     */
    @JSONField(name = "COMPANY")
    private String company;
    /**
     * email Addr
     * 邮箱地址
     */
    @JSONField(name = "EMAIL_ADDR")
    private String emailAddress;
    /**
     * name
     * 用户姓名
     */
    @JSONField(name = "NAME")
    private String name;
    /**
     * birthdate
     * 出生日期
     */
    @JSONField(name = "BIRTHDATE")
    private String birthdate;
    /**
     * deptDescr
     * 所属部门
     */
    @JSONField(name = "DEPT_DESCR")
    private String deptAbbreviation;
    /**
     * posnDescr
     * 用户岗位
     */
    @JSONField(name = "POSN_DESCR")
    private String personnelDept;
    /**
     * personCount
     */
    @JSONField(name = "PERSON_COUNT")
    private Integer personCount;
    /**
     * positionNbr
     * 岗位ID
     */
    @JSONField(name = "POSITION_NBR")
    private String positionNbr;
    /**
     * phone1
     * 座机号码
     */
    @JSONField(name = "PHONE1")
    private String phone1;
    /**
     * phone
     * 联系电话
     */
    @JSONField(name = "PHONE")
    private String phone;
    /**
     * emplRcd
     * 员工记录号
     */
    @JSONField(name = "EMPL_RCD")
    private Integer employeeRcd;
    /**
     * hrStatus
     * 用户状态
     */
    @JSONField(name = "HR_STATUS")
    private String hrStatus;
    /**
     * hpsJobLevel
     * 员工职级ID
     */
    @JSONField(name = "HPS_JOB_LEVEL")
    private String jobLevel;
    /**
     * hpsPunchType
     * 考勤类型
     */
    @JSONField(name = "HPS_PUNCH_TYPE")
    private String punchType;
    /**
     * hpsAdress
     */
    @JSONField(name = "HPS_ADRESS")
    private String address;
    /**
     * sex 性别
     */
    @JSONField(name = "SEX")
    private String sex;
    /**
     * emplid 员工ID
     */
    @JSONField(name = "EMPLID")
    private String employeeId;
    /**
     * rownumber
     */
    @JSONField(name = "rownumber")
    private Integer rowNumber;
    /**
     * deptid 所属部门id
     */
    @JSONField(name = "DEPTID")
    private String deptId;
    /**
     * companyDescr 用户所属机构
     */
    @JSONField(name = "COMPANY_DESCR")
    private String companyDescription;
}
