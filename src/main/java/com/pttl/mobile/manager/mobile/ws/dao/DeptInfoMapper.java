package com.pttl.mobile.manager.mobile.ws.dao;

import com.pttl.mobile.manager.mobile.ws.entity.DeptInfoDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface DeptInfoMapper {
    /**
     * delete by primary key
     *
     * @param id primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long id);

    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(DeptInfoDO record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(DeptInfoDO record);

    /**
     * select by primary key
     *
     * @param id primary key
     * @return object by primary key
     */
    DeptInfoDO selectByPrimaryKey(Long id);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(DeptInfoDO record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(DeptInfoDO record);

    int updateBatch(List<DeptInfoDO> list);

    int batchInsert(@Param("list") List<DeptInfoDO> list);

    /**
     * 清空表数据
     */
    void cleanTable();

}