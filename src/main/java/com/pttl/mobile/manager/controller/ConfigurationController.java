package com.pttl.mobile.manager.controller;

import com.pttl.mobile.manager.lib.ResponseMessage;
import com.pttl.mobile.manager.constant.UserConstant;
import com.pttl.mobile.manager.constant.EnvEnum;
import com.pttl.mobile.manager.constant.SystemPlatformEnum;
import com.pttl.mobile.manager.domain.request.EnvUpdateRequest;
import com.pttl.mobile.manager.domain.dto.ConfigurationDTO;
import com.pttl.mobile.manager.domain.dto.OnlinePreviewDTO;
import com.pttl.mobile.manager.domain.bo.MobileManagerUser;
import com.pttl.mobile.manager.service.ConfigurationService;
import com.pttl.mobile.manager.util.RedisUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/configuration")
@Api(tags = "配置相关")
public class ConfigurationController {

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private ConfigurationService configurationService;

    @ApiOperation(value = "获取在线预览配置", notes = "获取在线预览配置")
    @GetMapping("/all")
    public ResponseMessage<List<ConfigurationDTO<Object>>> getConfiguration() {
        return configurationService.getConfiguration();
    }

    @ApiOperation(value = "获取在线预览配置", notes = "获取在线预览配置")
    @GetMapping("/onlinepreview")
    public ResponseMessage<OnlinePreviewDTO> getOnlinePreview() {
        return ResponseMessage.ok(configurationService.getConfigurationByName("onlinePreview", OnlinePreviewDTO.class).getValue());
    }

    @ApiOperation(value = "修改配置", notes = "修改配置")
    @PatchMapping()
    public ResponseMessage<Boolean> insertOrUpdate(@RequestBody ConfigurationDTO<Object> setting) {
        return configurationService.insertOrUpdate(setting);
    }

    @ApiOperation(value = "设置系统环境配置(Redis)(影响客户端API调用)", notes = "设置系统环境配置(Redis)(影响客户端API调用)")
    @PatchMapping("/env")
    public ResponseMessage<Boolean> updateEnv(Integer currentEnv, HttpServletRequest request) {
        try {
            String key = UserConstant.MOBILE_MANAGER_ENV_ONLINEOFFICE_REDIS_KEY;
            if (MobileManagerUser.getCurrentUser().getSystemPlatform() == SystemPlatformEnum.MALL.getSystemPlatform()) {
                key = UserConstant.MOBILE_MANAGER_ENV_MALL_REDIS_KEY;
            }

            Integer value = null;
            if (currentEnv != null) {
                if (currentEnv == EnvEnum.PRD.getEnv()) {
                    value = EnvEnum.PRD.getEnv();
                } else if (currentEnv == EnvEnum.PRE.getEnv()) {
                    value = EnvEnum.PRE.getEnv();
                } else {
                    return ResponseMessage.error("currentEnv值错误");
                }
            }

            redisUtil.set(key, value, -1L);

            return ResponseMessage.ok(true);
        } catch (Exception ex) {
            return ResponseMessage.error("设置系统环境变量失败");
        }
    }

    @ApiOperation(value = "获取系统环境配置(Redis)", notes = "获取系统环境配置(Redis)")
    @GetMapping("/env")
    public ResponseMessage<Integer> getEnv(HttpServletRequest request) {
        return ResponseMessage.ok(EnvUtil.getCurrentEnv());
    }

    @ApiOperation(value = "设置Session中的环境配置(与客户端API无关)", notes = "设置Session中的环境配置(与客户端API无关)")
    @PatchMapping("/settingForSession")
    public ResponseMessage<Boolean> updateSettingForSession(@RequestBody EnvUpdateRequest parm, HttpServletRequest request) {
        try {
            HttpSession session = request.getSession();
            if (parm.getCurrentEnv() != null) {
                if (parm.getCurrentEnv() == EnvEnum.PRD.getEnv()) {
                    session.setAttribute(MobileManagerUser.ATTR_CURRENT_ENV, EnvEnum.PRD.getEnv());
                } else if (parm.getCurrentEnv() == EnvEnum.PRE.getEnv()) {
                    session.setAttribute(MobileManagerUser.ATTR_CURRENT_ENV, EnvEnum.PRE.getEnv());
                } else {
                    return ResponseMessage.error("currentEnv值错误");
                }
            }

            if (parm.getSystemPlatform() != null) {
                if (parm.getSystemPlatform() == SystemPlatformEnum.MALL.getSystemPlatform()) {
                    session.setAttribute(MobileManagerUser.ATTR_SYSTEM_PLAT_FORM, SystemPlatformEnum.MALL.getSystemPlatform());
                } else if (parm.getSystemPlatform() == SystemPlatformEnum.ONLINEOFFICE.getSystemPlatform()) {
                    session.setAttribute(MobileManagerUser.ATTR_SYSTEM_PLAT_FORM, SystemPlatformEnum.ONLINEOFFICE.getSystemPlatform());
                } else {
                    return ResponseMessage.error("systemPlatform值错误");
                }
            }

            return ResponseMessage.ok(true);
        } catch (Exception ex) {
            return ResponseMessage.error("设置Session环境变量失败");
        }
    }

    @ApiOperation(value = "获取Session中的环境配置", notes = "获取Session中的环境配置")
    @GetMapping("/settingForSession")
    public ResponseMessage<Map<String, Object>> getSettingForSession() {
        Map<String, Object> map = new HashMap<>();

        MobileManagerUser currentUser = MobileManagerUser.getCurrentUser();
        map.put("userName", currentUser.getUserName());
        map.put("currentEnv", currentUser.getCurrentEnv());
        map.put("systemPlatform", currentUser.getSystemPlatform());

        return ResponseMessage.ok(map);
    }
}
