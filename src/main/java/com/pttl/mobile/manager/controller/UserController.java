package com.pttl.mobile.manager.controller;

import cn.hutool.captcha.CaptchaUtil;
import cn.hutool.captcha.CircleCaptcha;
import cn.hutool.captcha.generator.RandomGenerator;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.read.builder.ExcelReaderBuilder;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.github.pagehelper.PageInfo;
import com.pttl.mobile.manager.config.ApplicationDefinedConfiguration;
import com.pttl.mobile.manager.constant.CommonConstants;
import com.pttl.mobile.manager.domain.bo.MobileManagerUser;
import com.pttl.mobile.manager.domain.dto.*;
import com.pttl.mobile.manager.domain.entity.SystemUserDO;
import com.pttl.mobile.manager.domain.request.UserPageRequest;
import com.pttl.mobile.manager.domain.request.UserPasswordRequest;
import com.pttl.mobile.manager.domain.request.UserSaveRequest;
import com.pttl.mobile.manager.domain.request.UserUpdateRequest;
import com.pttl.mobile.manager.excel.dto.ExportDepartmentDataDTO;
import com.pttl.mobile.manager.excel.dto.ImportUserDataDTO;
import com.pttl.mobile.manager.excel.exception.ExcelErrorResultException;
import com.pttl.mobile.manager.excel.listener.ImportUserDataListener;
import com.pttl.mobile.manager.excel.util.ExportExcelFileUtil;
import com.pttl.mobile.manager.lib.ResponseMessage;
import com.pttl.mobile.manager.lib.SmsUtil;
import com.pttl.mobile.manager.mobile.exception.ErrorMessageException;
import com.pttl.mobile.manager.service.*;
import com.pttl.mobile.manager.util.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
@RestController
@Slf4j
@RequestMapping("/user")
@Api(tags = "manager用户登录及管理")
public class UserController {
    private static Base64.Decoder decoder = Base64.getDecoder();

    private static final String USER_LOCK_KEY = "USER_LOCK:";

    /**
     * 手机号正则
     */
    private static final String PHONE_REGEX = "1[3-9]\\d{9}";

    @Value("${admin.userName}")
    private String defaultUserName;

    @Value("${admin.password}")
    private String defaultPassword;

    /**
     * 用户导入Excel模板
     */
    @Value("${excel-file.user-template-path}")
    private String excelTemplatePath;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private ApplicationService applicationService;

    /**
     * 策略服务层
     */
    @Resource
    private Strategy2Service strategy2Service;

    /**
     * 用户服务层
     */
    @Resource
    private UserService userService;

    /**
     * 部门服务 层
     */
    @Autowired
    private Department2Service department2Service;

    /**
     * 管理端用户
     */
    @Resource
    private SystemUserService systemUserService;

    /**
     * 短信网关
     */
    @Resource
    private SmsUtil smsUtil;


    /**
     * 属性配置参数
     */
    @Resource
    private ApplicationDefinedConfiguration applicationDefinedConfiguration;

    @ApiOperation(value = "Manager后台登录", notes = "Manager后台登录")
    @PostMapping("/login")
    public ResponseMessage login(@RequestBody MobileManagerUser userBean, HttpServletRequest request) {
        // 参数校验
        ResponseMessage validUserLoginData = isValidUserLoginData(userBean);
        if (!validUserLoginData.isOk()) {
            return validUserLoginData;
        }

        // userName使用base64加密
        String orgUserName;
        // password使用sm2加密
        String orgPassword;
        try {
            orgUserName = new String(decoder.decode(userBean.getUserName()), StandardCharsets.UTF_8);
            orgPassword = SM2Utils.decryptByPrivateKey(userBean.getPassword(), SM2Utils.PRIVATE_KEY);
        } catch (Exception e) {
            return ResponseMessage.error("账号密码不匹配");
        }

        String key = USER_LOCK_KEY + orgUserName;
        Integer errorCount = redisUtil.getInt(key);
        if (errorCount != null && errorCount > 2) {
            long minutes = redisUtil.getExpire(key, TimeUnit.MINUTES);
            minutes = minutes == 0 ? 1 : minutes;
            return ResponseMessage.error("账号已锁定,请" + minutes + "分钟之后再试!");
        }

        boolean isLoginSuccess = !StringUtil.isEmpty(orgUserName) &&
                orgUserName.equalsIgnoreCase(defaultUserName) &&
                !StringUtil.isEmpty(orgPassword) &&
                orgPassword.equalsIgnoreCase(defaultPassword);

        // 20230330 增加 校验短信验证 增加开关验证
        if (applicationDefinedConfiguration.getSystemManager().getLoginSmsSwitch()) {
            Object codeObject = redisUtil.get(String.format(CommonConstants.SMS_CODE_PREFIX, orgUserName));
            if (Objects.isNull(codeObject) || !userBean.getVerifyCode().equals(codeObject.toString())) {
                return ResponseMessage.error("登录失败,验证码错误!");
            }
        }

        // 登陆成功
        if (isLoginSuccess) {
            HttpSession session = request.getSession();
            session.setAttribute(MobileManagerUser.ATTR_USER_NAME, orgUserName);
            // 第一次登录默认显示在线办公 1：在线办公 2：太力商城
            session.setAttribute(MobileManagerUser.ATTR_SYSTEM_PLAT_FORM, 1);
            session.setAttribute(MobileManagerUser.ATTR_CURRENT_ENV, EnvUtil.getCurrentEnv());

            redisUtil.del(key);

            return ResponseMessage.status(ResponseMessage.SUCCESS, "登录成功!", true);
        }

        errorCount = errorCount == null ? 1 : errorCount + 1;
        redisUtil.set(key, errorCount, 3600L);

        return ResponseMessage.error("登录失败,用户名密码错误!");
    }

    /**
     * 验证用户信息
     *
     * @param userBean
     * @return
     */
    private ResponseMessage isValidUserLoginData(MobileManagerUser userBean) {
        // 参数校验
        if (StringUtil.isEmpty(userBean.getUserName())) {
            return ResponseMessage.status(ResponseMessage.ERROR, "用戶名不能为空!", false);
        }

        if (StringUtil.isEmpty(userBean.getPassword())) {
            return ResponseMessage.status(ResponseMessage.ERROR, "密码不能为空!", false);
        }

        if (applicationDefinedConfiguration.getSystemManager().getLoginSmsSwitch() && StringUtil.isEmpty(userBean.getVerifyCode())) {
            return ResponseMessage.status(ResponseMessage.ERROR, "验证码不能为空!", false);
        }

        // xss过滤
        String userName = XssUtil.filterScript(userBean.getUserName());
        // 验证名称
        if (!isValidComment(userName)) {
            return ResponseMessage.status(ResponseMessage.ERROR, "只允许字母、数字和常见标点符号", false);
        }

        userBean.setUserName(userName);

        String password = XssUtil.filterScript(userBean.getPassword());
        userBean.setPassword(password);

        String verifyCode = XssUtil.filterScript(userBean.getVerifyCode());
        if (applicationDefinedConfiguration.getSystemManager().getLoginSmsSwitch() && !isSixDigit(verifyCode)) {
            return ResponseMessage.status(ResponseMessage.ERROR, "验证码为6位数字", false);
        }
        userBean.setVerifyCode(verifyCode);

        return ResponseMessage.ok();
    }

    /**
     * 使用正则表达式可以有效地验证和过滤用户输入，以确保其满足预期的格式和内容
     * * 使用正则表达式来验证评论内容是否符合只允许字母、数字和常见标点符号的要求
     * * 只允许包含字母、数字和常见标点符号
     *
     * @param commentText
     * @return
     */
    public static boolean isValidComment(String commentText) {
        // 定义允许的模式：字母、数字和常见标点符号
        String allowedPattern = "^[a-zA-Z0-9.,!?=\\s]+$";

        Pattern pattern = Pattern.compile(allowedPattern);
        Matcher matcher = pattern.matcher(commentText);

        return matcher.matches();
    }

    /**
     * * 只允许6位数字
     *
     * @param input
     * @return
     */
    public static boolean isSixDigit(String input) {
        // 定义只允许6位数字的正则表达式
        String sixDigitPattern = "^[0-9]{6}$";

        Pattern pattern = Pattern.compile(sixDigitPattern);
        Matcher matcher = pattern.matcher(input);

        return matcher.matches();
    }

    @ApiOperation(value = "发送短信验证码", notes = "短信验证码")
    @PostMapping(value = "/getSmsVerificationCode")
    public ResponseMessage<String> getSmsVerificationCode(@RequestBody ManagerSystemUserSmsDTO requestDTO) throws IOException {
        if (Objects.isNull(requestDTO) || StrUtil.isEmpty(requestDTO.getUserName())) {
            return ResponseMessage.error(CommonConstants.ERROR_MESSAGE_PARAMETER_IS_NULL);
        }

        String userName = new String(decoder.decode(requestDTO.getUserName()), StandardCharsets.UTF_8);
        SystemUserDO user = systemUserService.getByLoginName(userName);

        if (Objects.isNull(user) || StrUtil.isEmpty(user.getMobile())) {
            return ResponseMessage.error(CommonConstants.ERROR_MESSAGE_DATA_NOT_EXIST);
        }

        // 先查询缓存 中取
        Object codeObject = redisUtil.get(String.format(CommonConstants.SMS_CODE_PREFIX, user.getLoginName()));

        // 发送短信
        String code = Objects.nonNull(codeObject) ? codeObject.toString() : new RandomGenerator("0123456789", 6).generate();
        String codeSmsStr = String.format(SmsUtil.MESSAGE_CONTENT_TEMPLATE, code);
        log.info("phone number：{}, sms code string: {}", user.getMobile(), codeSmsStr);
        boolean success = smsUtil.sendSmsMessage(user.getMobile(), codeSmsStr);

        if (!success) {
            return ResponseMessage.error("验证码发送失败");
        }

        // 放入缓存  8小时过期
        redisUtil.set(String.format(CommonConstants.SMS_CODE_PREFIX, user.getLoginName()), code, 60L * 60L * 8L);

        return ResponseMessage.ok();
    }

    @ApiOperation(value = "修改用户关联手机号", notes = "修改用户关联手机号, 用于接收短信验证码")
    @PostMapping(value = "/changeMobilePhoneNumber")
    public ResponseMessage<String> changeMobilePhoneNumber(@RequestBody ManagerSystemUserSmsUpdateDTO requestDTO, HttpSession session) throws IOException {
        if (Objects.isNull(requestDTO) || StrUtil.isEmpty(requestDTO.getPhoneNumber())) {
            return ResponseMessage.error(CommonConstants.ERROR_MESSAGE_PARAMETER_IS_NULL);
        }

        SystemUserDO user = systemUserService.getByLoginName(session.getAttribute(MobileManagerUser.ATTR_USER_NAME).toString());

        if (Objects.isNull(user) || StrUtil.isEmpty(user.getMobile())) {
            return ResponseMessage.error(CommonConstants.ERROR_MESSAGE_DATA_NOT_EXIST);
        }

        // 更新绑定
        if (requestDTO.getPhoneNumber().equals(user.getMobile())) {
            return ResponseMessage.error("请勿重复绑定");
        }

        if (!requestDTO.getPhoneNumber().matches(PHONE_REGEX)) {
            return ResponseMessage.error("请核验手机号");
        }

        user.setMobile(requestDTO.getPhoneNumber());
        systemUserService.updateByPrimaryKey(user);

        return ResponseMessage.ok();
    }

    @ApiOperation(value = "Manager后台登出", notes = "Manager后台登出")
    @PostMapping(value = "/logout")
    public ResponseMessage<Boolean> logout(HttpSession session) {
        session.invalidate();
        return ResponseMessage.ok(true);
    }

    @ApiOperation(value = "跳转页面", notes = "跳转页面")
    @GetMapping(value = "/redirect")
    public void redirectToHome(HttpServletRequest request, HttpServletResponse response) throws IOException {
        MobileManagerUser currentUser = MobileManagerUser.getCurrentUser();
        String host = request.getHeader("host");
        if (StringUtil.isEmpty(currentUser.getUserName())) {
            response.sendRedirect(host);
        } else {
            response.sendRedirect(String.format("%s://%s/home?page=application", request.getScheme(), host));
        }

    }


    @ApiOperation(value = "图片验证码", notes = "图片验证码, 一般用于登陆验证,响应图片流")
    @GetMapping(value = "/getCaptcha")
    public void captchaForWeb(HttpServletRequest request, HttpServletResponse response) throws IOException {
        ServletOutputStream outputStream = null;
        try {
            // 定义图形验证码的长、宽、验证码字符数、干扰线宽度
            CircleCaptcha circleCaptcha = CaptchaUtil.createCircleCaptcha(250, 100, 4, 22);
            outputStream = response.getOutputStream();
            circleCaptcha.write(outputStream);
            String captchaCode = circleCaptcha.getCode();
            // 放入缓存  两分钟过期
            redisUtil.set(String.format(CommonConstants.CAPTCHA_CODE_PREFIX, captchaCode), captchaCode, 120L);
        } finally {
            if (Objects.nonNull(outputStream)) {
                outputStream.close();
            }
        }

    }


    //----------------------------------------------<AUTHOR> 20221020----------------------------------------------------

    @ApiOperation(value = "获取分页用户信息", notes = "移动端用户-获取分页用户信息")
    @PostMapping("/pageInfo")
    public ResponseMessage<PageInfo<UserPageDTO>> pageInfoList(@RequestBody UserPageRequest pageRequest) {
        PageInfo<UserPageDTO> pageInfo = userService.pageInfo(pageRequest);
        return ResponseMessage.ok(pageInfo);
    }


    @ApiOperation(value = "获取用户详情信息", notes = "移动端用户-获取用户详情信息")
    @GetMapping("/userDetails/{userId}")
    public ResponseMessage<UserDetailsDTO> userDetails(@PathVariable("userId") Long userId) {
        // 判空
        if (Objects.isNull(userId)) {
            return ResponseMessage.error(CommonConstants.ERROR_MESSAGE_PARAMETER_IS_NULL);
        }

        // 根据id获取用户详情  需要处理部门全路径
        UserDetailsDTO userDetailsDTO = userService.getUserDetails(userId);
        return ResponseMessage.ok(userDetailsDTO);
    }


    @ApiOperation(value = "禁用/恢复用户", notes = "移动端用户-禁用/恢复用户")
    @GetMapping("/userDisabled/{userId}")
    public ResponseMessage<String> userDisabled(@PathVariable("userId") Long userId) {
        // 判空
        if (Objects.isNull(userId)) {
            return ResponseMessage.error(CommonConstants.ERROR_MESSAGE_PARAMETER_IS_NULL);
        }

        // 根据id处理禁用/恢复用户
        userService.userDisabled(userId);
        return ResponseMessage.ok();
    }

    @ApiOperation(value = "删除用户(真实删除)", notes = "移动端用户-删除用户(真实删除)")
    @PostMapping("/userRemove")
    public ResponseMessage<String> userRemove(@RequestBody List<Long> ids) {
        // 判空
        if (CollUtil.isEmpty(ids)) {
            return ResponseMessage.error(CommonConstants.ERROR_MESSAGE_PARAMETER_IS_NULL);
        }

        // 根据id处理 删除用户(真实删除)
        userService.userRemove(ids);
        return ResponseMessage.ok();
    }

    @ApiOperation(value = "删除用户(真实删除)", notes = "移动端用户-根据单个ID删除用户(真实删除)")
    @GetMapping("/userRemoveById/{id}")
    public ResponseMessage<String> userRemoveById(@PathVariable Long id) {
        // 判空
        if (Objects.isNull(id)) {
            return ResponseMessage.error(CommonConstants.ERROR_MESSAGE_PARAMETER_IS_NULL);
        }

        // 根据单个ID 删除用户(真实删除)
        userService.deleteByPrimaryKey(id);
        return ResponseMessage.ok();
    }


    @ApiOperation(value = "创建用户", notes = "移动端用户-创建用户")
    @PostMapping("/createUser")
    public ResponseMessage<String> createUser(@RequestBody UserSaveRequest userSaveRequest) {
        // 判空
        if (Objects.isNull(userSaveRequest)) {
            return ResponseMessage.error(CommonConstants.ERROR_MESSAGE_PARAMETER_IS_NULL);
        }

        try {
            userService.createUser(userSaveRequest);
            return ResponseMessage.ok();
        } catch (ErrorMessageException errorMessageException) {
            return ResponseMessage.error(errorMessageException.getMessage());
        }
    }

    @ApiOperation(value = "修改用户", notes = "移动端用户-修改用户")
    @PostMapping("/updateUser")
    public ResponseMessage<String> updateUser(@RequestBody UserUpdateRequest userUpdateRequest) {
        // 判空
        if (Objects.isNull(userUpdateRequest)) {
            return ResponseMessage.error("参数不能为空");
        }

        // 校验用户登录名称是否重复
        UserDTO userDTO = userService.selectByLoginName(userUpdateRequest.getLoginName());
        if (Objects.nonNull(userDTO) && !Objects.equals(userDTO.getId(), userUpdateRequest.getId())) {
            return ResponseMessage.error("登录名称重复");
        }

        userService.updateUser(userUpdateRequest);
        return ResponseMessage.ok();
    }

    @ApiOperation(value = "修改用户密码", notes = "移动端用户-修改用户密码")
    @PostMapping("/updatePassword")
    public ResponseMessage<String> updatePassword(@RequestBody UserPasswordRequest userPasswordRequest) {
        // 判空
        if (Objects.isNull(userPasswordRequest)) {
            return ResponseMessage.error("参数不能为空");
        }

        // 解密
        try {
            String decrypt = new RSAUtil().decrypt(userPasswordRequest.getPassword());
            userService.updatePassword(userPasswordRequest.getId(), decrypt);
            return ResponseMessage.ok();
        } catch (Exception e) {
            e.printStackTrace();
            return ResponseMessage.error("解析失败");
        }

    }


    @ApiOperation(value = "下载用户导入模板", notes = "移动端用户-下载用户导入模板")
    @GetMapping("/downloadUserTemplate")
    public void downloadUserTemplate(HttpServletResponse response) {
        FileDownloadUtil fileDownloadUtil = new FileDownloadUtil();
        fileDownloadUtil.download(excelTemplatePath, response);
    }

    @ApiOperation(value = "部门ID数据下载", notes = "移动端用户-部门ID数据下载")
    @GetMapping("/downloadDepartmentData")
    public void downloadDepartmentData(HttpServletResponse response) throws IOException {
        List<ExportDepartmentDataDTO> exportDepartmentDataList = department2Service.exportDepartmentListAll();

        // 数据导出到文件并相应
        ExportExcelFileUtil.dataExport("部门ID数据", "部门数据",
                exportDepartmentDataList, ExportDepartmentDataDTO.class, response);
    }

    /**
     * 上传数据
     *
     * @param file 支持.xlsx
     * @return 提示消息
     */
    @PostMapping("/importUsers")
    @ApiOperation(value = "导入用户", notes = "移动端用户-导入用户, 支持xls/xlsx/csv格式")
    public ResponseMessage<Object> importUsers(@RequestParam(name = "file") MultipartFile file) {
        // 判空
        if (Objects.isNull(file)) {
            return ResponseMessage.error("请选择文件");
        }

        try {
            //文件流
            ExcelReaderBuilder excelReaderBuilder = EasyExcelFactory.read(file.getInputStream(),
                    //实体类
                    ImportUserDataDTO.class,
                    //监听器
                    new ImportUserDataListener(this.userService, this.department2Service));

            // 3.0版本开始 兼容csv 默认只支持xls/xlsx
            if (file.getOriginalFilename().contains(ExcelTypeEnum.CSV.getValue())) {
                excelReaderBuilder.excelType(ExcelTypeEnum.CSV);
            }

            excelReaderBuilder.sheet().doRead();
        } catch (IOException ioException) {
            return ResponseMessage.error("文件解析错误");
        } catch (ExcelErrorResultException excelErrorResultException) {
            return new ResponseMessage<>(ResponseMessage.ERROR, excelErrorResultException.getMessage(),
                    excelErrorResultException.getObject());
        }

        return ResponseMessage.ok();
    }

    @ApiOperation(value = "获取用户已关联策略", notes = "获取用户已关联所有策略信息")
    @GetMapping("/userStrategy/{userId}")
    public ResponseMessage<List<UserStrategyInfoDTO>> userStrategyByUserId(@PathVariable("userId") Long userId) {
        // 判空
        if (Objects.isNull(userId)) {
            return ResponseMessage.error(CommonConstants.ERROR_MESSAGE_PARAMETER_IS_NULL);
        }

        List<UserStrategyInfoDTO> strategyPageDTO = strategy2Service.userStrategyByUserId(userId);
        return ResponseMessage.ok(strategyPageDTO);
    }

    @ApiOperation(value = "获取用户已关联应用", notes = "获取用户已关联所有应用信息")
    @GetMapping("/userApplication/{userId}")
    public ResponseMessage<List<ApplicationInfoForUserDTO>> userApplicationByUserId(@PathVariable("userId") Long userId) {
        // 判空
        if (Objects.isNull(userId)) {
            return ResponseMessage.error(CommonConstants.ERROR_MESSAGE_PARAMETER_IS_NULL);
        }

        List<ApplicationInfoForUserDTO> infoList = applicationService.userApplicationByUserId(userId);
        return ResponseMessage.ok(infoList);
    }
}
