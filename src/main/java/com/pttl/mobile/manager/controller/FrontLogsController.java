package com.pttl.mobile.manager.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.pttl.mobile.manager.constant.CommonConstants;
import com.pttl.mobile.manager.domain.dto.FrontLogsQueryDTO;
import com.pttl.mobile.manager.domain.dto.FrontLogsResultDTO;
import com.pttl.mobile.manager.domain.entity.FrontLogs;
import com.pttl.mobile.manager.excel.dto.ExportFrontLogsDataDTO;
import com.pttl.mobile.manager.excel.util.ExportExcelFileUtil;
import com.pttl.mobile.manager.lib.ResponseMessage;
import com.pttl.mobile.manager.service.FrontLogsService;
import com.pttl.mobile.manager.service.MobileLogsService;
import com.pttl.mobile.manager.util.BeanMapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 前端日志控制层
 *
 * <AUTHOR>
 * @date 2024/5/8
 */
@Slf4j
@RestController
@RequestMapping("/frontLogs")
@Api(tags = "日志数据管理")
public class FrontLogsController {

    /**
    * 前端日志服务对象
    */
    @Resource
    private FrontLogsService frontLogsService;

    /**
     * 移动端日志服务对象
     */
    @Resource
    private MobileLogsService mobileLogsService;

    /**
    * 通过主键查询单条数据
    *
    * @param id 主键
    * @return 单条数据
    */
    @ApiOperation(value = "获取详情数据信息", notes = "获取详情数据信息")
    @GetMapping("/getById")
    public ResponseMessage<FrontLogs> selectOne(Long id) {

        return ResponseMessage.ok(frontLogsService.selectByPrimaryKey(id));
    }



    @ApiOperation(value = "获取分页数据信息", notes = "获取分页数据信息")
    @PostMapping("/pageInfo")
    public ResponseMessage<PageInfo<FrontLogsResultDTO>> page(@RequestBody FrontLogsQueryDTO frontLogsQuery) {

        if (Objects.nonNull(frontLogsQuery) && Objects.nonNull(frontLogsQuery.getStartLoginTime())) {
            frontLogsQuery.setStartLoginTimeLong(frontLogsQuery.getStartLoginTime().getTime());
        }

        if (Objects.nonNull(frontLogsQuery) && Objects.nonNull(frontLogsQuery.getEndLoginTime())) {
            frontLogsQuery.setEndLoginTimeLong(frontLogsQuery.getEndLoginTime().getTime());
        }

        PageInfo<FrontLogsResultDTO> result = frontLogsService.pageInfo(frontLogsQuery);

        return ResponseMessage.ok(result);
    }


    @ApiOperation(value = "前端数据导出", notes = "前端数据导出")
    @PostMapping("/exportLogs")
    public void exportLogs(
            @RequestBody FrontLogsQueryDTO queryVO, HttpServletResponse response) throws IOException {
        if (Objects.nonNull(queryVO) && Objects.nonNull(queryVO.getStartLoginTime())) {
            queryVO.setStartLoginTimeLong(queryVO.getStartLoginTime().getTime());
        }

        if (Objects.nonNull(queryVO) && Objects.nonNull(queryVO.getEndLoginTime())) {
            queryVO.setEndLoginTimeLong(queryVO.getEndLoginTime().getTime());
        }

        // 分页 取数据最大 即符合条件的全部导出
        queryVO.setPageNum(1);
        queryVO.setPageSize(30000);

        // 查询 导出
        PageInfo<FrontLogs> results = frontLogsService.exportPageInfoFrontLogs(queryVO);
        List<ExportFrontLogsDataDTO> personDataList = dataHandle(results.getList());
        ExportExcelFileUtil.dataExport("前端详情日志", "前端详情日志列表", personDataList, ExportFrontLogsDataDTO.class, response);
    }

    /**
     * 数据处理
     * @param dataList
     * @return
     */
    private List<ExportFrontLogsDataDTO> dataHandle(List<FrontLogs> dataList) {
        List<ExportFrontLogsDataDTO> personDataList = new ArrayList<>();
        if (CollUtil.isNotEmpty(dataList)){
            for (FrontLogs frontLogs : dataList) {
                ExportFrontLogsDataDTO exportFrontLogsDataDTO = BeanMapper.map(frontLogs, ExportFrontLogsDataDTO.class);
                jsonHandle(exportFrontLogsDataDTO, frontLogs);
                personDataList.add(exportFrontLogsDataDTO);
            }
        }
        return personDataList;
    }

    /**
     * 处理json数据
     * @param exportFrontLogsDataDTO
     * @param frontLogs
     */
    private void jsonHandle(ExportFrontLogsDataDTO exportFrontLogsDataDTO, FrontLogs frontLogs) {
        if (Objects.nonNull(frontLogs) && StrUtil.isNotBlank(frontLogs.getLogDetail())) {
            JSONObject logDetailJson = JSON.parseObject(frontLogs.getLogDetail());
            exportFrontLogsDataDTO.setModuleName(logDetailJson.getString(CommonConstants.JSON_MODULE_NAME));
            exportFrontLogsDataDTO.setPageLoadTime(logDetailJson.getString(CommonConstants.JSON_PAGE_LOAD_TIME));

            JSONObject apiJson = logDetailJson.getJSONObject(CommonConstants.JSON_PAGE_LOG).getJSONObject(CommonConstants.JSON_APIS);
            if (Objects.isNull(apiJson)) {
                return;
            }
            JSONArray apiJsonJSONArray = apiJson.getJSONArray(CommonConstants.JSON_LISTS);
            if (Objects.isNull(apiJsonJSONArray)) {
                return;
            }
            exportFrontLogsDataDTO.setApisCount(apiJsonJSONArray.size());

            Object totalTimeObject = apiJson.get(CommonConstants.JSON_TOTAL_TIME);
            double totalTime = getTotalTime(totalTimeObject);
            exportFrontLogsDataDTO.setApisTimes(totalTime);

            if (Objects.nonNull(frontLogs.getLogDate())) {
                exportFrontLogsDataDTO.setLogDate(DateUtil.format(DateUtil.date(frontLogs.getLogDate()), DatePattern.NORM_DATETIME_PATTERN));
            }

            // 避免长度过长 导致异常
            String truncatedText = frontLogs.getLogDetail().length() > 32767 ?
                    frontLogs.getLogDetail().substring(0, 32767) : frontLogs.getLogDetail();
            exportFrontLogsDataDTO.setLogDetail(truncatedText);
        }
    }

    private static double getTotalTime(Object totalTimeObject) {
        double totalTime = 0;
        if (totalTimeObject instanceof Double) {
            // 如果 totalTime 是浮点数，直接赋值
            totalTime = (Double) totalTimeObject;
        } else if (totalTimeObject instanceof Integer) {
            // 如果 totalTime 是整数，将其转换为浮点数（毫秒转秒）
            totalTime = ((Integer) totalTimeObject).doubleValue() / 1000.0;
        } else if (totalTimeObject instanceof BigDecimal) {
            // 如果 totalTime 是整数，将其转换为浮点数（毫秒转秒）
            BigDecimal totalTimeObject1 = (BigDecimal) totalTimeObject;
            totalTime = totalTimeObject1.doubleValue();
        }
        return totalTime;
    }
}
