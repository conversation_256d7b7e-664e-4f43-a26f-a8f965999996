package com.pttl.mobile.manager.controller;

import com.pttl.mobile.manager.lib.ResponseMessage;
import com.pttl.mobile.manager.domain.po.SystemPlatform;
import com.pttl.mobile.manager.service.SystemPlatformService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/systemplatform")
@Api(tags = "系统平台相关")
public class SystemPlatformController {

    @Autowired
    private SystemPlatformService systemPlatformService;

    @ApiOperation(value = "获取系统平台数据", notes = "获取系统平台数据")
    @GetMapping()
    public ResponseMessage<List<SystemPlatform>> getSystemPlatform() {
        return systemPlatformService.getSystemPlatform();
    }
}
