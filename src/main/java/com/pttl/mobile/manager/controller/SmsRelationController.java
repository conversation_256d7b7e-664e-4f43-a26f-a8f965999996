package com.pttl.mobile.manager.controller;

import com.pttl.mobile.manager.domain.dto.SmsRelationDTO;
import com.pttl.mobile.manager.domain.entity.SmsRelationDO;
import com.pttl.mobile.manager.service.SmsRelationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
* 短信告警关联关系控制层
*
* <AUTHOR>
*/
@RestController
@RequestMapping("/sms/relation")
public class SmsRelationController {
    /**
    * 服务对象
    */
    @Autowired
    private SmsRelationService smsRelationService;

    /**
    * 通过主键查询单条数据
    *
    * @param id 主键
    * @return 单条数据
    */
    @GetMapping("getById")
    public SmsRelationDO selectOne(Long id) {
        return smsRelationService.selectByPrimaryKey(id);
    }

    @PostMapping("addOrUpdate")
    public int insert(@RequestBody SmsRelationDTO record) {
        return smsRelationService.saveOrUpdate(record);
    }
}
