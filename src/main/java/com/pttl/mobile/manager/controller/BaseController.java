package com.pttl.mobile.manager.controller;

import com.alibaba.fastjson.JSON;
import com.pttl.mobile.manager.lib.ResponseMessage;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * controller基础类
 *
 * <AUTHOR>
 * @date 2022/12/23
 **/

public class BaseController {

    private BaseController() {
    }

    /**
     * 重定义响应错误消息
     *
     * @param response response
     * @param message  消息
     * @throws IOException 异常
     */
    protected static void responseErrorMessage(HttpServletResponse response, String message) throws IOException {
        response.setContentType("application/json");
        response.setCharacterEncoding("utf-8");
        ResponseMessage<Object> responseMessage = ResponseMessage.error(message);
        response.getWriter().println(JSON.toJSONString(responseMessage));
    }
}
