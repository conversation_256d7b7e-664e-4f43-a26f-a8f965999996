package com.pttl.mobile.manager.controller;

import cn.hutool.core.util.StrUtil;
import com.pttl.mobile.manager.domain.dto.SmsModelDTO;
import com.pttl.mobile.manager.domain.dto.SmsModelResponseDTO;
import com.pttl.mobile.manager.domain.entity.SmsModelDO;
import com.pttl.mobile.manager.lib.ResponseMessage;
import com.pttl.mobile.manager.service.SmsModelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
* 告警短信模板控制层
*
* <AUTHOR>
*/
@RestController
@RequestMapping("/sms/model")
public class SmsModelController {
    /**
    * 服务对象
    */
    @Autowired
    private SmsModelService smsModelService;

    /**
    * 通过主键查询单条数据
    *
    * @param id 主键
    * @return 单条数据
    */
    @GetMapping("selectOne")
    public SmsModelDO selectOne(Long id) {
        return smsModelService.selectByPrimaryKey(id);
    }

    @PostMapping("add")
    public ResponseMessage insert(@RequestBody SmsModelDTO record) {
        if (Objects.isNull(record)) {
            return ResponseMessage.error("请填写模板信息");
        }

        if (StrUtil.isEmpty(record.getModelName())) {
            return ResponseMessage.error("模板名称必填");
        }

        if (StrUtil.isEmpty(record.getModelContent())) {
            return ResponseMessage.error("模板内容必填");
        }

        smsModelService.saveOrUpdate(record);

        return ResponseMessage.ok();
    }

    @GetMapping("list")
    public ResponseMessage<List<SmsModelResponseDTO>> page() {
        return ResponseMessage.ok(smsModelService.list());
    }

    @GetMapping("deleteById")
    public ResponseMessage deleteById(Long id) {
        if (Objects.isNull(id)) {
            return ResponseMessage.error("id必填");
        }

        smsModelService.deleteByPrimaryKey(id);
        return ResponseMessage.ok();
    }
}
