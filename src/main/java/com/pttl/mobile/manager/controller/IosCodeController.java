package com.pttl.mobile.manager.controller;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.qrcode.QrCodeUtil;
import cn.hutool.extra.qrcode.QrConfig;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.read.builder.ExcelReaderBuilder;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.github.pagehelper.PageInfo;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;
import com.pttl.mobile.manager.config.ApplicationDefinedConfiguration;
import com.pttl.mobile.manager.config.IosCodeTypeMapConfiguration;
import com.pttl.mobile.manager.constant.CommonConstants;
import com.pttl.mobile.manager.domain.dto.*;
import com.pttl.mobile.manager.domain.request.IosCodePageRequest;
import com.pttl.mobile.manager.excel.dto.ImportIosCodeDataDTO;
import com.pttl.mobile.manager.excel.exception.ExcelErrorResultException;
import com.pttl.mobile.manager.excel.listener.ImportIosCodeDataListener;
import com.pttl.mobile.manager.lib.ResponseMessage;
import com.pttl.mobile.manager.service.IosCodeService;
import com.pttl.mobile.manager.util.FileDownloadUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@RestController
@Slf4j
@RequestMapping("/ios/code")
@Api(tags = "ios-code二维码数据管理")
public class IosCodeController {

    /**
     * 标准img base64串前缀
     */
    private static final String STANDARD_BASE64_IMG_PREFIX = "data:image/jpeg;base64,";

    /**
     * 用户导入Excel模板
     */
    @Value("${excel-file.ios-code-template-path}")
    private String excelTemplatePath;

    /**
     * ios code服务 层
     */
    @Resource
    private IosCodeService iosCodeService;

    /**
     * 动态配置
     */
    @Resource
    private ApplicationDefinedConfiguration applicationDefinedConfiguration;

    /**
     * redis客户端
     */
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    /**
     * ios code码 分类标识map配置
     */
    @Resource
    private IosCodeTypeMapConfiguration iosCodeTypeMapConfiguration;

    @ApiOperation(value = "获取分页数据信息", notes = "获取分页数据信息")
    @PostMapping("/pageInfo")
    public ResponseMessage<PageInfo<IosCodePageDTO>> pageInfoList(@RequestBody IosCodePageRequest pageRequest) {

        PageInfo<IosCodePageDTO> pageInfo = iosCodeService.pageInfo(pageRequest);
        return ResponseMessage.ok(pageInfo);
    }


    @ApiOperation(value = "删除ios-code(真实删除)", notes = "删除ios-code(真实删除)(批量删除)")
    @PostMapping("/iosCodeRemove")
    public ResponseMessage<String> iosCodeRemove(@RequestBody List<Long> ids) {
        // 判空
        if (CollUtil.isEmpty(ids)) {
            return ResponseMessage.error(CommonConstants.ERROR_MESSAGE_PARAMETER_IS_NULL);
        }

        // 根据id处理 删除用户(真实删除)
        iosCodeService.iosCodeRemove(ids);
        return ResponseMessage.ok();
    }

    @ApiOperation(value = "获取一个ios-code链接二维码", notes = "获取一个ios-code链接二维码,响应为标准图片base64串")
    @GetMapping("/getQrCode/{id}")
    public ResponseMessage<String> getQrCode(@PathVariable String id) {
        log.info("enter into getQrCode, parameter: {}", id);
        // 检查参数
        if (StrUtil.isEmpty(id)) {
            return ResponseMessage.error(CommonConstants.ERROR_MESSAGE_PARAMETER_IS_NULL);
        }

        // 增加校验 已经获取过直接返回
        String qrCodeImageBase64 = attemptToAcquireQrCode(id);
        // 以标准图片base64开头 则说明已经获取过,并在有效内, 不再重新获取
        if (StrUtil.isNotEmpty(qrCodeImageBase64) && qrCodeImageBase64.startsWith(STANDARD_BASE64_IMG_PREFIX)) {
            log.info("have obtained, cache fetch, parameter: {}, qrCodeImageBase64: {}", id, qrCodeImageBase64);
            return ResponseMessage.ok(qrCodeImageBase64);
        }

        // 获取应用类型
        LinkedHashMap<String, Integer> iosCodeType = iosCodeTypeMapConfiguration.getIosCodeType();

        // 去库里取 电科太力对应应用类型为1
        String iosCodeQrUrl = iosCodeService.getIosCodeQrUrl(iosCodeType.get("电科太力"));
        if (StrUtil.isEmpty(iosCodeQrUrl)) {
            log.info("二维码已用完...");
            return ResponseMessage.error("二维码已用完");
        }

        // 成功获取到  响应二维码图片流
        String qrCodeBase64 = addCacheAndQrCode(iosCodeQrUrl, id);
        log.info("mysql acquire, parameter: {}, qrCodeImageBase64: {}", id, qrCodeBase64);
        return ResponseMessage.ok(qrCodeBase64);
    }

    /**
     * 尝试获取已经获取过的qrcode 已经获取过直接返回
     *
     * @param id 用户id
     */
    private String attemptToAcquireQrCode(String id) {
        // 检查是否获取过
        String qrCodeUrl = stringRedisTemplate.opsForValue().get(String.format(CommonConstants.QRCODE_USER_ID_KEY, id));

        // 存在则说明获取过, 并在有效期内, 直接返回 二维码图片流
        if (StrUtil.isNotEmpty(qrCodeUrl)) {
            // 响应二维码
            return qrCodeImgBase64(qrCodeUrl);
        }

        return qrCodeUrl;
    }

    /**
     * 记录缓存 并响应二维码
     *
     * @param url      url
     * @param id       用户id
     */
    private String addCacheAndQrCode(String url, String id) {
        // 序列化qrcode url 对象到Redis
        stringRedisTemplate.opsForValue().set(String.format(CommonConstants.QRCODE_USER_ID_KEY, id),
                url, applicationDefinedConfiguration.getIosCode().getValidTime(), TimeUnit.MINUTES);

        // 响应二维码
        return qrCodeImgBase64(url);
    }

    /**
     * 响应二维码
     *
     * @param url      url
     * @param response 响应
     */
    private void qrCode(String url, HttpServletResponse response) {
        QrConfig config = new QrConfig();
        // 高纠错级别
        config.setErrorCorrection(ErrorCorrectionLevel.H);

        try {
            // img为png
            QrCodeUtil.generate(url, config, "png", response.getOutputStream());
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 根据链接生成二维码 并转化为标准图片base64形式
     *
     * @param url 链接
     * @return 标准图片base64串
     */
    private static String qrCodeImgBase64(String url) {
        QrConfig config = new QrConfig();
        // 高纠错级别
        config.setErrorCorrection(ErrorCorrectionLevel.H);
        //  buffer 二维码生成转base64
        BufferedImage image = QrCodeUtil.generate(url, config);

        // 输出流
        ByteArrayOutputStream stream = new ByteArrayOutputStream();
        try {
            ImageIO.write(image, "png", stream);
        } catch (IOException e) {
            e.printStackTrace();
        }

        // 标准图片base64格式
        String imgBase64 = Base64.encode(stream.toByteArray());
        // 拼接为标准图片base64格式
        return STANDARD_BASE64_IMG_PREFIX + imgBase64;
    }


    @ApiOperation(value = "下载导入模板", notes = "下载ios-code二维码数据导入模板")
    @GetMapping("/downloadTemplate")
    public void downloadTemplate(HttpServletResponse response) {
        FileDownloadUtil fileDownloadUtil = new FileDownloadUtil();
        fileDownloadUtil.download(excelTemplatePath, response);
    }

    @ApiOperation(value = "统计用户使用情况", notes = "统计用户使用情况")
    @GetMapping("/statistics")
    public ResponseMessage<List<UserUsageStatisticsDTO>> getCount(@RequestParam("amount") Boolean amount) {

        log.info("enter into method  getCount [statistics], amount: {}", amount);

        List<UserUsageStatisticsDTO> results = null;

        // 使用情况
        if (Objects.isNull(amount) || !amount) {
            results = iosCodeService.getCount();
            log.info("statistics user use results: {}", results);
            return ResponseMessage.ok(results);
        }

        // 未使用情况
        results = iosCodeService.unusedQuantity();
        log.info("statistics residue results: {}", results);

        return ResponseMessage.ok(results);
    }

    /**
     * 上传数据
     *
     * @param file 支持.xlsx
     * @return 提示消息
     */
    @PostMapping("/importData")
    @ApiOperation(value = "导入ios-code二维码数据", notes = "导入ios-code二维码数据, 支持xls/xlsx/csv格式")
    public ResponseMessage<Object> importUsers(@RequestParam(name = "file") MultipartFile file) {
        // 判空
        if (Objects.isNull(file)) {
            log.info("enter into method importUsers, file is null");
            return ResponseMessage.error("请选择文件");
        }

        log.info("enter into method importUsers, file-name: {}", file.getOriginalFilename());
        // 获取应用类型
        LinkedHashMap<String, Integer> iosCodeType = iosCodeTypeMapConfiguration.getIosCodeType();

        try {
            // 文件流
            ExcelReaderBuilder excelReaderBuilder = EasyExcelFactory.read(file.getInputStream(),
                    //实体类
                    ImportIosCodeDataDTO.class,
                    //监听器
                    new ImportIosCodeDataListener(this.iosCodeService, iosCodeType));
            // 3.0版本开始 兼容csv 默认只支持xls/xlsx
            if (file.getOriginalFilename().contains(ExcelTypeEnum.CSV.getValue())) {
                excelReaderBuilder.excelType(ExcelTypeEnum.CSV);
            }
            excelReaderBuilder.sheet().doRead();
        } catch (IOException ioException) {
            return ResponseMessage.error("文件解析错误");
        } catch (ExcelErrorResultException excelErrorResultException) {
            return new ResponseMessage<>(ResponseMessage.SUCCESS, ResponseMessage.SUCCESS_MESSAGE,
                    excelErrorResultException.getObject());
        }

        return ResponseMessage.ok();
    }

    //--------------------------------------20230321新增-----------------------------------------------------------------

    @ApiOperation(value = "兑换统计", notes = "统计用户使用情况统计")
    @GetMapping("/acquisitionExchangeStatistics")
    public ResponseMessage<ExchangeStatisticsDTO> acquisitionExchangeStatistics(@RequestParam(name = "appType",
            required = false, defaultValue = "1") Integer appType) {

        log.info("enter into method  acquisitionExchangeStatistics");

        ExchangeStatisticsDTO exchangeStatistics = iosCodeService.getExchangeStatistics(appType);

        log.info("acquisitionExchangeStatistics results: {}", exchangeStatistics);

        return ResponseMessage.ok(exchangeStatistics);
    }

    @ApiOperation(value = "兑换详情", notes = "兑换详情, dataType: 1今日 2近一周 3近一个月 4近一年 5所有")
    @GetMapping("/obtainExchangeDetails")
    public ResponseMessage<List<ExchangeDetailsDTO>> obtainExchangeDetails(@RequestParam("dataType") Integer dataType, @RequestParam(name = "appType",
            required = false, defaultValue = "1") Integer appType) {

        log.info("enter into method  obtainExchangeDetails");

        List<ExchangeDetailsDTO> results = iosCodeService.getExchangeDetails(dataType, appType);

        log.info("obtainExchangeDetails results: {}", results);

        return ResponseMessage.ok(results);
    }

    @ApiOperation(value = "获取应用类型", notes = "获取app type应用类型(map)")
    @GetMapping("/getAppTypeMap")
    public ResponseMessage<List<IosCodeAppTypeDTO>> getAppTypeMap() {

        log.info("enter into method  getAppTypeMap");

        LinkedHashMap<String, Integer> iosCodeType = iosCodeTypeMapConfiguration.getIosCodeType();
        List<IosCodeAppTypeDTO> collect = iosCodeType.entrySet().stream().map(stringIntegerEntry -> new IosCodeAppTypeDTO(stringIntegerEntry.getKey(),
                stringIntegerEntry.getValue())).collect(Collectors.toList());
        log.info("getAppTypeMap results: {}", iosCodeType);

        return ResponseMessage.ok(collect);
    }

    @ApiOperation(value = "获取一个ios-code链接二维码", notes = "获取一个ios-code链接二维码,响应为标准图片base64串")
    @GetMapping("/getQC/{appType}/{id}")
    public ResponseMessage<String> getQC(@PathVariable("appType") Integer appType, @PathVariable("id") String id) {
        log.info("enter into getQC, appType: {}, id: {}", appType, id);
        // 检查参数
        if (CharSequenceUtil.isEmpty(id)) {
            return ResponseMessage.error(CommonConstants.ERROR_MESSAGE_PARAMETER_IS_NULL);
        }
        // 应用类型校验
        if (Objects.isNull(appType)) {
            return ResponseMessage.error(CommonConstants.ERROR_MESSAGE_PARAMETER_IS_NULL);
        }

        // 获取应用类型 校验应用类型是否在已知类型中
        LinkedHashMap<String, Integer> iosCodeType = iosCodeTypeMapConfiguration.getIosCodeType();
        if (!iosCodeType.containsValue(appType)) {
            return ResponseMessage.error(CommonConstants.ERROR_MESSAGE_DATA_NOT_EXIST);
        }

        // 增加校验 已经获取过直接返回
        String qrCodeImageBase64 = attemptToAcquireQrCode(id);
        // 以标准图片base64开头 则说明已经获取过,并在有效内, 不再重新获取
        if (CharSequenceUtil.isNotEmpty(qrCodeImageBase64) && qrCodeImageBase64.startsWith(STANDARD_BASE64_IMG_PREFIX)) {
            log.info("have obtained, cache fetch, parameter: {}, qrCodeImageBase64: {}", id, qrCodeImageBase64);
            return ResponseMessage.ok(qrCodeImageBase64);
        }


        // 去库里取 电科太力对应应用类型为1
        String iosCodeQrUrl = iosCodeService.getIosCodeQrUrl(appType);
        if (CharSequenceUtil.isEmpty(iosCodeQrUrl)) {
            log.info("应用类型: {}, 二维码已用完...", appType);
            return ResponseMessage.error("该类型二维码已用完");
        }

        // 成功获取到  响应二维码图片流
        String qrCodeBase64 = addCacheAndQrCode(iosCodeQrUrl, id);
        log.info("mysql acquire, parameter: {}, qrCodeImageBase64: {}", id, qrCodeBase64);
        return ResponseMessage.ok(qrCodeBase64);
    }
}
