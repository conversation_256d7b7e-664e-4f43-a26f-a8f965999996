package com.pttl.mobile.manager.controller;

import com.github.pagehelper.PageInfo;
import com.pttl.mobile.manager.constant.CommonConstants;
import com.pttl.mobile.manager.domain.dto.ManagerOperationLogDetailsDTO;
import com.pttl.mobile.manager.domain.dto.ManagerOperationLogPageResultDTO;
import com.pttl.mobile.manager.domain.entity.ManagerOperationLogDO;
import com.pttl.mobile.manager.domain.request.ManagerOperationLogPageRequest;
import com.pttl.mobile.manager.lib.ResponseMessage;
import com.pttl.mobile.manager.service.ManagerOperationLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 管理端系统操作日志 控制层
 *
 * <AUTHOR>
 * @date 2022/10/25
 **/

@Slf4j
@RestController
@RequestMapping("/managerOperationLog")
@Api(tags = "管理端系统操作日志")
public class ManagerOperationLogController {

    /**
     * 管理端操作日志服务层
     */
    @Resource
    private ManagerOperationLogService managerOperationLogService;


    @ApiOperation(value = "获取分页数据信息", notes = "获取分页数据信息")
    @PostMapping("/pageInfo")
    public ResponseMessage<PageInfo<ManagerOperationLogPageResultDTO>> pageInfoList(@RequestBody ManagerOperationLogPageRequest pageRequest) {
        // 日志类型给定默认 为 1 成功
        if (Objects.isNull(pageRequest.getOperationStatus())) {
            pageRequest.setOperationStatus(ManagerOperationLogDO.OPERATION_STATUS_SUCCESS);
        }

        PageInfo<ManagerOperationLogPageResultDTO> pageInfo = managerOperationLogService.pageInfo(pageRequest);
        return ResponseMessage.ok(pageInfo);
    }

    @ApiOperation(value = "获取详情信息", notes = "获取详情信息")
    @GetMapping("/dataDetails/{id}")
    public ResponseMessage<ManagerOperationLogDetailsDTO> dataDetails(@PathVariable("id") Long id) {
        // 判空
        if (Objects.isNull(id)) {
            return ResponseMessage.error(CommonConstants.ERROR_MESSAGE_PARAMETER_IS_NULL);
        }

        // 根据id获取详情
        ManagerOperationLogDetailsDTO detailsDTO = managerOperationLogService.getDetailsById(id);
        return ResponseMessage.ok(detailsDTO);
    }


}
