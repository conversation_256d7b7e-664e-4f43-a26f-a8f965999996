package com.pttl.mobile.manager.controller;

import cn.hutool.core.collection.CollUtil;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.pttl.mobile.manager.constant.CommonConstants;
import com.pttl.mobile.manager.domain.dto.ApplicationDTO;
import com.pttl.mobile.manager.domain.dto.PolicyUserAndDepartmentConfigurationDTO;
import com.pttl.mobile.manager.domain.dto.StrategyDetailDTO;
import com.pttl.mobile.manager.domain.dto.StrategyPageDTO;
import com.pttl.mobile.manager.domain.request.*;
import com.pttl.mobile.manager.lib.ResponseMessage;
import com.pttl.mobile.manager.mobile.exception.ErrorMessageException;
import com.pttl.mobile.manager.service.Strategy2Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 策略管理控制层
 *
 * <AUTHOR>
 * @date 2022/10/17
 **/
@Slf4j
@RestController
@RequestMapping("/strategy")
@Api(tags = "策略管理")
public class StrategyController {

    /**
     * 策略服务层
     */
    @Resource
    private Strategy2Service strategy2Service;

    @ApiOperation(value = "获取分页策略信息", notes = "获取分页策略信息")
    @PostMapping("/pageInfo")
    public ResponseMessage<PageInfo<StrategyPageDTO>> pageInfoList(@RequestBody StrategyPageRequest pageRequest) {
        PageInfo<StrategyPageDTO> pageInfo = strategy2Service.pageInfo(pageRequest);
        return ResponseMessage.ok(pageInfo);
    }


    @ApiOperation(value = "获取策略详情信息", notes = "获取策略详情信息")
    @GetMapping("/strategyDetails/{id}")
    public ResponseMessage<StrategyDetailDTO> dataDetails(@PathVariable("id") Long id) {
        // 判空
        if (Objects.isNull(id)) {
            return ResponseMessage.error(CommonConstants.ERROR_MESSAGE_PARAMETER_IS_NULL);
        }

        // 根据id获取详情
        StrategyDetailDTO detailsDTO = strategy2Service.getStrategyDetails(id);
        return ResponseMessage.ok(detailsDTO);
    }

    @ApiOperation(value = "删除策略信息", notes = "删除策略信息")
    @PostMapping("/strategyRemove")
    public ResponseMessage<String> removeById(@RequestBody List<Long> ids) {
        // 判空
        if (CollUtil.isEmpty(ids)) {
            return ResponseMessage.error(CommonConstants.ERROR_MESSAGE_PARAMETER_IS_NULL);
        }

        // 这里只做状态修改  不做物理删除
        strategy2Service.removeByIds(ids);
        return ResponseMessage.ok();
    }

    @ApiOperation(value = "删除策略信息", notes = "根据单个id删除策略信息")
    @GetMapping("/strategyRemoveById/{id}")
    public ResponseMessage<String> strategyRemoveById(@PathVariable("id") Long id) {
        // 判空
        if (Objects.isNull(id)) {
            return ResponseMessage.error(CommonConstants.ERROR_MESSAGE_PARAMETER_IS_NULL);
        }

        // 这里只做状态修改  不做物理删除
        ArrayList<Long> list = Lists.newArrayList(id);
        strategy2Service.removeByIds(list);
        return ResponseMessage.ok();
    }

    @ApiOperation(value = "克隆策略", notes = "克隆策略")
    @GetMapping("/strategyClone/{id}")
    public ResponseMessage<String> cloneStrategy(@PathVariable("id") Long id) {
        // 判空
        if (Objects.isNull(id)) {
            return ResponseMessage.error(CommonConstants.ERROR_MESSAGE_PARAMETER_IS_NULL);
        }

        strategy2Service.cloneById(id);
        return ResponseMessage.ok();
    }

    @ApiOperation(value = "更新策略基础信息", notes = "更新策略基础信息")
    @PostMapping("/updateStrategy")
    public ResponseMessage<String> updateStrategy(@RequestBody StrategyUpdateRequest dataRequest) {
        // 判空
        if (Objects.isNull(dataRequest)) {
            return ResponseMessage.error(CommonConstants.ERROR_MESSAGE_PARAMETER_IS_NULL);
        }

        try {
            strategy2Service.updateStrategy(dataRequest);
            return ResponseMessage.ok();
        } catch (ErrorMessageException errorMessageException) {
            return ResponseMessage.error(errorMessageException.getMessage());
        }
    }


    @ApiOperation(value = "根据策略id获取关联应用", notes = "根据策略id获取关联应用")
    @GetMapping("/getApplicationByStrategyId/{id}")
    public ResponseMessage<List<ApplicationDTO>> getApplicationByStrategyId(@PathVariable("id") Long id) {
        // 判空
        if (Objects.isNull(id)) {
            return ResponseMessage.error(CommonConstants.ERROR_MESSAGE_PARAMETER_IS_NULL);
        }

        List<ApplicationDTO> dataList = strategy2Service.getApplicationByStrategyId(id);
        return ResponseMessage.ok(dataList);
    }

    @ApiOperation(value = "根据策略id获取关联用户和组织部门", notes = "根据策略id获取关联用户和组织部门")
    @GetMapping("/getUserAndDepartmentByStrategyId/{id}")
    public ResponseMessage<List<PolicyUserAndDepartmentConfigurationDTO>> getUserByStrategyId(@PathVariable("id") Long id) {
        // 判空
        if (Objects.isNull(id)) {
            return ResponseMessage.error(CommonConstants.ERROR_MESSAGE_PARAMETER_IS_NULL);
        }

        List<PolicyUserAndDepartmentConfigurationDTO> dataList = strategy2Service.getUserByStrategyId(id);
        return ResponseMessage.ok(dataList);
    }


    @ApiOperation(value = "创建策略及配置", notes = "创建策略及配置")
    @PostMapping("/createStrategy")
    public ResponseMessage<String> createStrategy(@RequestBody StrategyCreateRequest dataRequest) {
        // 判空
        if (Objects.isNull(dataRequest)) {
            return ResponseMessage.error(CommonConstants.ERROR_MESSAGE_PARAMETER_IS_NULL);
        }

        try {
            strategy2Service.createStrategy(dataRequest);
            return ResponseMessage.ok();
        } catch (ErrorMessageException errorMessageException) {
            return ResponseMessage.error(errorMessageException.getMessage());
        }
    }

    @ApiOperation(value = "更新策略application配置", notes = "更新策略application配置")
    @PostMapping("/updateStrategyAppConfiguration")
    public ResponseMessage<String> updateStrategyAppConfiguration(@RequestBody StrategyUpdateAppConfigurationRequest dataRequest) {
        // 判空
        if (Objects.isNull(dataRequest)) {
            return ResponseMessage.error(CommonConstants.ERROR_MESSAGE_PARAMETER_IS_NULL);
        }

        strategy2Service.updateStrategyAppConfiguration(dataRequest);
        return ResponseMessage.ok();
    }

    @ApiOperation(value = "更新策略用户及部门配置", notes = "更新策略用户及部门配置")
    @PostMapping("/updateStrategyUserConfiguration")
    public ResponseMessage<String> updateStrategyUserConfiguration(@RequestBody StrategyUpdateUserConfigurationRequest dataRequest) {
        // 判空
        if (Objects.isNull(dataRequest)) {
            return ResponseMessage.error(CommonConstants.ERROR_MESSAGE_PARAMETER_IS_NULL);
        }

        strategy2Service.updateStrategyUserConfiguration(dataRequest);
        return ResponseMessage.ok();
    }
}
