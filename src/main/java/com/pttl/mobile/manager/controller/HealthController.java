package com.pttl.mobile.manager.controller;

import com.pttl.mobile.manager.lib.ResponseMessage;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@RestController
public class HealthController {

    /**
     * 健康监测接口
     *
     * @return
     */
    // @OrdinaryRequest
    @GetMapping(path = "/health")
    public ResponseMessage<Void> health() {
        return ResponseMessage.ok();
    }

}
