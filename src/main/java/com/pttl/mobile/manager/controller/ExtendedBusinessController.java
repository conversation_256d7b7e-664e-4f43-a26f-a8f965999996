package com.pttl.mobile.manager.controller;

import com.pttl.mobile.manager.constant.ExtendedBusinessEnum;
import com.pttl.mobile.manager.domain.dto.VrSwitchDTO;
import com.pttl.mobile.manager.domain.entity.ExtendedBusiness;
import com.pttl.mobile.manager.lib.ResponseMessage;
import com.pttl.mobile.manager.service.ExtendedBusinessService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
* 扩展业务数据存储, 比如增加个开关/配置等(mm_extended_business)表控制层
*
* <AUTHOR>
*/
@RestController
@RequestMapping("extended")
public class ExtendedBusinessController {

    /**
    * 服务对象
    */
    @Autowired
    private ExtendedBusinessService extendedBusinessService;

    /**
    * 通过主键查询单条数据
    *
    * @param id 主键
    * @return 单条数据
    */
    @GetMapping("selectOne")
    public ExtendedBusiness selectOne(Long id) {
        return extendedBusinessService.selectByPrimaryKey(id);
    }

    @GetMapping("vrSwitch")
    @ApiOperation(value = "ios vr 开关更新", notes = "ios vr 开关更新")
    public ResponseMessage vrSwitch() {

        extendedBusinessService.updateVrSwitch();

        return ResponseMessage.ok();
    }

    @GetMapping("vrSwitchStatus")
    @ApiOperation(value = "ios vr 开关状态", notes = "ios vr 开关当前状态")
    public ResponseMessage<VrSwitchDTO> vrSwitchStatus() {

        VrSwitchDTO data = extendedBusinessService.getSwitchStatusByKey(ExtendedBusinessEnum.IOS_VR_SWITCH_ON);

        return ResponseMessage.ok(data);
    }

}
