package com.pttl.mobile.manager.controller;

import com.pttl.mobile.manager.lib.ResponseMessage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.RandomStringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.Date;

/**
 * <AUTHOR>
 * @description: 文件服务
 * @date 2022/1/24 16:18
 */
@Slf4j
@RestController
@RequestMapping("/file")
@Api(tags = "文件服务")
public class FileController {
    @Value("${file.path.shareRootPath}")
    private String shareRootPath;

    @Value("${file.path.uploadPath}")
    private String uploadPath;

    @Value("${file.path.fileSize}")
    private Long fileSize;

    @Value("${file.path.validFileSuffixes}")
    private String validFileSuffixes;

    @ApiOperation(value = "文件上传", notes = "文件上传")
    @PostMapping(value = "/upload")
    public ResponseEntity<ResponseMessage> upload(MultipartFile file) {
        // 最大上传文件限制
        Long maxFileSize = fileSize * 1024 * 1024;
        Long uploadFileSize = file.getSize();
        String uploadFileName = file.getOriginalFilename();

        if (uploadFileSize > maxFileSize) {
            return ResponseEntity.ok(ResponseMessage.error("上传文件超过[" + fileSize + "]M,上传失败"));
        }

        int idx = uploadFileName.lastIndexOf(".", uploadFileName.length());
        if (idx < 0) {
            return ResponseEntity.ok(ResponseMessage.error("上传文件格式不支持"));
        }
        String uploadFileSuffix = uploadFileName.substring(idx + 1);
        if (!validFileSuffixes.contains(uploadFileSuffix.toLowerCase())) {
            return ResponseEntity.ok(ResponseMessage.error("请上传" + validFileSuffixes + "格式的文件"));
        }

        String filePath = shareRootPath + File.separator + uploadPath;
        File fileDirectory = new File(filePath);
        if (!fileDirectory.exists()) {
            fileDirectory.mkdirs();
        }

        String newFileName = RandomStringUtils.randomNumeric(6) + "_" + new Date().getTime() + "." + uploadFileSuffix;
        String newFilePath = filePath + File.separator + newFileName;
        try {
            file.transferTo(new File(newFilePath));
            String os = System.getProperty("os.name");
            if (!os.toLowerCase().startsWith("win")) {
                String command = "chmod 777 " + newFilePath;
                Runtime.getRuntime().exec(command);
            }
        } catch (Exception e) {
            log.error("附件上传失败", e);
            return ResponseEntity.ok(ResponseMessage.error("附件上传失败"));
        }

        return ResponseEntity.ok(ResponseMessage.ok(newFilePath));
    }
}
