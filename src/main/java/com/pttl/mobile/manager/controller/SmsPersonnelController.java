package com.pttl.mobile.manager.controller;

import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageInfo;
import com.pttl.mobile.manager.domain.dto.SmsPersonnelDTO;
import com.pttl.mobile.manager.domain.dto.SmsPersonnelDetailsResponseDTO;
import com.pttl.mobile.manager.domain.dto.SmsPersonnelRequestDTO;
import com.pttl.mobile.manager.domain.dto.SmsPersonnelResponseDTO;
import com.pttl.mobile.manager.domain.entity.SmsPersonnelDO;
import com.pttl.mobile.manager.lib.ResponseMessage;
import com.pttl.mobile.manager.service.SmsPersonnelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

/**
* 短信告警人员信息控制层
*
* <AUTHOR>
*/
@RestController
@RequestMapping("/sms/personnel")
public class SmsPersonnelController {
    /**
    * 服务对象
    */
    @Autowired
    private SmsPersonnelService smsPersonnelService;


    /**
    * 通过主键查询单条数据
    *
    * @param id 主键
    * @return 单条数据
    */
    @GetMapping("getById")
    public SmsPersonnelDO selectOne(Long id) {
        return smsPersonnelService.selectByPrimaryKey(id);
    }

    @PostMapping("add")
    public ResponseMessage insert(@RequestBody SmsPersonnelDTO record) {

        if (Objects.isNull(record)) {
            return ResponseMessage.error("人员信息必填");
        }

        if (StrUtil.isBlank(record.getPhoneNum())) {
            return ResponseMessage.error("手机号必填");
        }

        if (StrUtil.isBlank(record.getName())) {
            return ResponseMessage.error("姓名必填");
        }

        if (Objects.isNull(record.getModelId())) {
            return ResponseMessage.error("模板ID必填");
        }

        if (StrUtil.isBlank(record.getSmsCondition())) {
            return ResponseMessage.error("阀值必填");
        }

        smsPersonnelService.saveOrUpdate(record);

        return ResponseMessage.ok();
    }

    @PostMapping("list")
    public ResponseMessage<PageInfo<SmsPersonnelResponseDTO>> page(@RequestBody SmsPersonnelRequestDTO smsPersonnelRequestDTO) {
        PageInfo<SmsPersonnelResponseDTO> pageInfo = smsPersonnelService.pageInfo(smsPersonnelRequestDTO);
        return ResponseMessage.ok(pageInfo);
    }

    @GetMapping("deleteById")
    public ResponseMessage deleteById(Long id) {
        if (Objects.isNull(id)) {
            return ResponseMessage.error("id必填");
        }
        smsPersonnelService.deleteById(id);
        return ResponseMessage.ok();
    }

    // 新增一个 获取人员详情附带模板详情, 用于修改回显

    @GetMapping("details")
    public ResponseMessage<SmsPersonnelDetailsResponseDTO> details(Long id) {
        if (Objects.isNull(id)) {
            return ResponseMessage.error("id必填");
        }
        SmsPersonnelDetailsResponseDTO data = smsPersonnelService.detailsById(id);
        return ResponseMessage.ok(data);
    }
}
