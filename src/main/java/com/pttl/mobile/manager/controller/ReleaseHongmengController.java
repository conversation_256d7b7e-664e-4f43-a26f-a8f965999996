package com.pttl.mobile.manager.controller;

import com.pttl.mobile.manager.domain.dto.ReleaseExtendDTO;
import com.pttl.mobile.manager.domain.po.Release;
import com.pttl.mobile.manager.domain.po.ReleaseType;
import com.pttl.mobile.manager.lib.ResponseMessage;
import com.pttl.mobile.manager.service.ReleaseHongmengService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

/**
* 发布版本控制层
*
* <AUTHOR>
*/
@RestController
@RequestMapping("/releaseHongmeng")
public class ReleaseHongmengController {
/**
* 服务对象
*/
    @Autowired
    private ReleaseHongmengService releaseHongmengService;

    @ApiOperation(value = "获取发版类型数据", notes = "获取发版类型数据")
    @GetMapping("/type")
    public ResponseMessage<List<ReleaseType>> getReleaseType() {
        return releaseHongmengService.getReleaseType();
    }

    @ApiOperation(value = "获取发版历史数据", notes = "获取发版历史数据")
    @GetMapping()
    public ResponseMessage<List<ReleaseExtendDTO>> getReleaseHistory(Integer type, String version, String keyword, Date startDate, Date endDate) {
        return releaseHongmengService.getReleaseHistory(type, version, keyword, startDate, endDate);
    }

    @ApiOperation(value = "创建新的发版数据", notes = "创建新的发版数据")
    @PostMapping()
    public ResponseMessage<Boolean> createRelease(@RequestBody Release release) {
        return releaseHongmengService.createRelease(release);
    }

    @ApiOperation(value = "删除发版记录", notes = "删除发版记录")
    @DeleteMapping()
    public ResponseMessage<Boolean> deleteReleaseByIds(@RequestBody List<String> ids) {
        return releaseHongmengService.deleteReleaseByIds(ids);
    }

}
