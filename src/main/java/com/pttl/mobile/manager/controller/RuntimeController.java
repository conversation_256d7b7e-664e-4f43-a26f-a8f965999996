package com.pttl.mobile.manager.controller;

import com.pttl.mobile.manager.lib.ResponseMessage;
import com.pttl.mobile.manager.domain.po.Runtime;
import com.pttl.mobile.manager.service.RuntimeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/runtime")
@Api(tags = "运行时相关")
public class RuntimeController {

    @Autowired
    private RuntimeService runtimeService;

    @ApiOperation(value = "创建新的运行时", notes = "创建新的运行时")
    @PostMapping()
    public ResponseMessage<Boolean> createRuntime(@RequestBody Runtime runtime) {
        return runtimeService.createRuntime(runtime);
    }

    @ApiOperation(value = "获取所有的运行时列表", notes = "获取所有的运行时列表")
    @GetMapping()
    public ResponseMessage<List<Runtime>> getRuntime() {
        return runtimeService.getRuntime();
    }

    @ApiOperation(value = "删除运行时", notes = "删除运行时")
    @DeleteMapping()
    public ResponseMessage<Boolean> deleteRuntimeByIds(@RequestBody List<String> ids) {
        return runtimeService.deleteRuntimeByIds(ids);
    }
}
