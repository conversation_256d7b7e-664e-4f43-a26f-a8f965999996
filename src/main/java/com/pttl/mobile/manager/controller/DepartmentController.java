package com.pttl.mobile.manager.controller;

import cn.hutool.core.collection.CollUtil;
import com.github.pagehelper.PageInfo;
import com.pttl.mobile.manager.constant.CommonConstants;
import com.pttl.mobile.manager.domain.dto.DepartmentQueryDTO;
import com.pttl.mobile.manager.domain.dto.DepartmentTreeDTO;
import com.pttl.mobile.manager.domain.dto.PolicyDepartmentConfigurationDTO;
import com.pttl.mobile.manager.domain.dto.SameLevelDepartmentDTO;
import com.pttl.mobile.manager.domain.request.DepartmentPageRequest;
import com.pttl.mobile.manager.domain.request.DepartmentSaveRequest;
import com.pttl.mobile.manager.domain.request.DepartmentUpdateRequest;
import com.pttl.mobile.manager.lib.ResponseMessage;
import com.pttl.mobile.manager.mobile.exception.ErrorMessageException;
import com.pttl.mobile.manager.service.Department2Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Slf4j
@RestController
@RequestMapping("/department")
@Api(tags = "部门管理")
public class DepartmentController {

    /**
     * 部门服务
     */
    @Resource
    private Department2Service department2Service;

    @ApiOperation(value = "获取所有部门-树", notes = "获取所有部门")
    @GetMapping()
    public ResponseMessage<List<DepartmentTreeDTO>> getDepartment() {
        List<DepartmentTreeDTO> departmentTreeList = department2Service.listTree();
        return ResponseMessage.ok(departmentTreeList);
    }


    @ApiOperation(value = "根据id获取部门信息", notes = "根据id获取部门信息")
    @GetMapping("/getDepartment/{departmentId}")
    public ResponseMessage<DepartmentQueryDTO> getOneDepartment(@PathVariable Long departmentId) {
        DepartmentQueryDTO departmentQueryDTO = department2Service.getByDepartmentId(departmentId);
        return ResponseMessage.ok(departmentQueryDTO);
    }

    @ApiOperation(value = "创建部门", notes = "创建部门")
    @PostMapping()
    public ResponseMessage<Boolean> createDepartment(@RequestBody DepartmentSaveRequest request) {
        // 判空
        if (Objects.isNull(request)) {
            return ResponseMessage.error(CommonConstants.ERROR_MESSAGE_PARAMETER_IS_NULL);
        }

        // 入库
        try {
            department2Service.saveDepartment(request);
        } catch (ErrorMessageException errorMessageException) {
            return ResponseMessage.error(errorMessageException.getMessage());
        }

        return ResponseMessage.ok();
    }

    @ApiOperation(value = "修改部门名称 描述", notes = "修改部门名称 描述")
    @PatchMapping()
    public ResponseMessage<Boolean> updateDepartment(@RequestBody DepartmentUpdateRequest request) {
        // 判空
        if (Objects.isNull(request) || Objects.isNull(request.getId())) {
            return ResponseMessage.error(CommonConstants.ERROR_MESSAGE_PARAMETER_IS_NULL);
        }

        // 更新部门部分信息
        try {
            department2Service.updateDepartment(request);
        } catch (ErrorMessageException errorMessageException) {
            return ResponseMessage.error(errorMessageException.getMessage());
        }

        return ResponseMessage.ok();
    }

    @ApiOperation(value = "删除部门", notes = "删除部门")
    @DeleteMapping()
    public ResponseMessage<Boolean> deleteDepartmentByIds(@RequestBody List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return ResponseMessage.error("参数不能为空");
        }

        // 批量删除
        department2Service.batchRemoveDepartment(ids);
        return ResponseMessage.ok();
    }

    @ApiOperation(value = "根据部门id获取子部门列表及人数", notes = "根据部门id获取子部门列表及人数")
    @PostMapping("/getDepartmentByDepartmentId")
    public ResponseMessage<PageInfo<PolicyDepartmentConfigurationDTO>> getDepartmentByParentDepartmentId(@RequestBody DepartmentPageRequest request) {
        // 判空
        if (Objects.isNull(request)) {
            return ResponseMessage.error("参数不能为空");
        }

        PageInfo<PolicyDepartmentConfigurationDTO> departmentList = department2Service
                .pageListByParentDepartmentId(request);
        return ResponseMessage.ok(departmentList);
    }


    @GetMapping("/getSameLevelDepartment")
    @ApiOperation(value = "获取当前部门的同级部门列表", notes = "获取部门的同级部门列表 只跨两级, 当前部门->父级部门->祖父级部门(只查当前的同级父级)")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "departmentId", value = "当前部门id", required = true, paramType = "query", dataType = "Long")
    })
    public ResponseMessage<List<SameLevelDepartmentDTO>> getSameLevelDepartment(@RequestParam("departmentId") Long departmentId) {
        List<SameLevelDepartmentDTO> departmentTreeList = department2Service.listSameLevelDepartment(departmentId);
        return ResponseMessage.ok(departmentTreeList);
    }

}
