package com.pttl.mobile.manager.controller;

import com.pttl.mobile.manager.domain.request.ApplicationListAllRequest;
import com.pttl.mobile.manager.lib.ResponseMessage;
import com.pttl.mobile.manager.domain.request.ApplicationCreateRequest;
import com.pttl.mobile.manager.domain.dto.ApplicationExtendDTO;
import com.pttl.mobile.manager.service.ApplicationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/application")
@Api(tags = "应用管理")
public class ApplicationController {

    @Autowired
    private ApplicationService applicationService;

    @ApiOperation(value = "创建新的应用", notes = "创建新的应用")
    @PostMapping()
    public ResponseMessage<Boolean> createApplication(@RequestBody ApplicationCreateRequest ac) {
        return applicationService.createApplication(ac);
    }

    @ApiOperation(value = "获取所有应用", notes = "获取所有应用")
    @GetMapping()
    public ResponseMessage<List<ApplicationExtendDTO>> getApplication() {
        return applicationService.getApplication();
    }

    @ApiOperation(value = "通过ID获取应用", notes = "通过ID获取应用")
    @GetMapping("/{id}")
    public ResponseMessage<ApplicationExtendDTO> getApplicationById(@PathVariable("id") String id) {
        return applicationService.getApplicationById(id);
    }

    @ApiOperation(value = "通过ID删除应用", notes = "通过ID删除应用")
    @DeleteMapping()
    public ResponseMessage<Boolean> deleteApplicationByIds(@RequestBody List<String> ids) {
        return applicationService.deleteApplicationByIds(ids);
    }

    @ApiOperation(value = "通过ID修改应用", notes = "通过ID修改应用")
    @PatchMapping()
    public ResponseMessage<Boolean> updateApplication(@RequestBody ApplicationCreateRequest ac) {
        return applicationService.updateApplication(ac);
    }


    // ---------------------------------------------------@Mr.xiBel-----------------------------------------------------

    @ApiOperation(value = "获取所有应用-20221018", notes = "获取所有应用")
    @PostMapping("/listDataAllApplication")
    public ResponseMessage<List<ApplicationExtendDTO>> listDataAllApplication(@RequestBody ApplicationListAllRequest request) {
        List<ApplicationExtendDTO> applicationExtendDTOS = applicationService.listAllApplication(request.getName(),
                request.getApplicationGroupId());
        return ResponseMessage.ok(applicationExtendDTOS);
    }

    @ApiOperation(value = "更新应用权重", notes = "通过ID获取应用")
    @GetMapping("/updateApplicationWeight")
    public ResponseMessage<Boolean> updateApplicationWeight(@RequestParam("id") String id, @RequestParam("weight") Integer applicationWeight) {
        applicationService.updateApplicationWeightById(id, applicationWeight);
        return ResponseMessage.ok(true);
    }
}
