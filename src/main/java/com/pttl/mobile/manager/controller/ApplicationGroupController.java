package com.pttl.mobile.manager.controller;

import com.pttl.mobile.manager.lib.ResponseMessage;
import com.pttl.mobile.manager.domain.po.ApplicationGroup;
import com.pttl.mobile.manager.domain.request.ApplicationGroupUpdateRequest;
import com.pttl.mobile.manager.service.ApplicationGroupService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/applicationgroup")
@Api(tags = "应用分组管理")
public class ApplicationGroupController {
    @Autowired
    private ApplicationGroupService applicationGroupService;

    @ApiOperation(value = "创建新的应用组", notes = "创建新的应用组")
    @PostMapping()
    public ResponseMessage<Boolean> createApplicationGroup(String name) {
        return applicationGroupService.createApplicationGroup(name);
    }

    @ApiOperation(value = "获取所有应用组", notes = "获取所有应用组")
    @GetMapping()
    public ResponseMessage<List<ApplicationGroup>> getApplicationGroup() {
        return applicationGroupService.getApplicationGroup();
    }

    @ApiOperation(value = "修改应用组", notes = "修改应用组")
    @PatchMapping()
    public ResponseMessage<Boolean> updateApplicationGroup(@RequestBody ApplicationGroupUpdateRequest agup) {
        return applicationGroupService.updateApplicationGroup(agup.getId(), agup.getName());
    }

    @ApiOperation(value = "删除应用组", notes = "删除应用组")
    @DeleteMapping()
    public ResponseMessage<Boolean> deleteApplicationGroupByIds(@RequestBody List<String> ids) {
        return applicationGroupService.deleteApplicationGroupByIds(ids);
    }

    @ApiOperation(value = "修改应用组权重", notes = "修改应用组权重")
    @PatchMapping("/weight")
    public ResponseMessage<Boolean> updateApplicationGroupWeight(@RequestBody List<String> groupIds) {
        return applicationGroupService.updateApplicationGroupWeight(groupIds);
    }
}
