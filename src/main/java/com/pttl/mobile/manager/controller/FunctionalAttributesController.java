package com.pttl.mobile.manager.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageInfo;
import com.pttl.mobile.manager.constant.CommonConstants;
import com.pttl.mobile.manager.domain.dto.FunctionalAttributesPageInfoDTO;
import com.pttl.mobile.manager.domain.request.FunctionalAttributesCheckIndexRequest;
import com.pttl.mobile.manager.domain.request.FunctionalAttributesCreateRequest;
import com.pttl.mobile.manager.domain.request.FunctionalAttributesPageRequest;
import com.pttl.mobile.manager.domain.request.FunctionalAttributesUpdateRequest;
import com.pttl.mobile.manager.lib.ResponseMessage;
import com.pttl.mobile.manager.service.FunctionalAttributesService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * 系统参数属性配置控制层
 *
 * <AUTHOR>
 * @date 2022/10/25
 **/

@Slf4j
@RestController
@RequestMapping("/functionalAttributes")
@Api(tags = "系统参数属性配置管理")
public class FunctionalAttributesController {

    /**
     * 系统参数属性配置 服务层
     */
    @Resource
    private FunctionalAttributesService functionalAttributesService;


    @ApiOperation(value = "获取分页数据信息", notes = "获取分页数据信息")
    @PostMapping("/pageInfo")
    public ResponseMessage<PageInfo<FunctionalAttributesPageInfoDTO>> pageInfoList(@RequestBody FunctionalAttributesPageRequest pageRequest) {
        PageInfo<FunctionalAttributesPageInfoDTO> pageInfo = functionalAttributesService.pageInfo(pageRequest);
        return ResponseMessage.ok(pageInfo);
    }

    @ApiOperation(value = "获取详情信息", notes = "获取详情信息")
    @GetMapping("/dataDetails/{id}")
    public ResponseMessage<FunctionalAttributesPageInfoDTO> dataDetails(@PathVariable("id") Long id) {
        // 判空
        if (Objects.isNull(id)) {
            return ResponseMessage.error(CommonConstants.ERROR_MESSAGE_PARAMETER_IS_NULL);
        }

        // 根据id获取详情
        FunctionalAttributesPageInfoDTO detailsDTO = functionalAttributesService.getDetailsById(id);
        return ResponseMessage.ok(detailsDTO);
    }

    @ApiOperation(value = "删除数据(物理删除)", notes = "删除数据")
    @PostMapping("/dataRemove")
    public ResponseMessage<String> removeById(@RequestBody List<Long> ids) {
        // 判空
        if (CollUtil.isEmpty(ids)) {
            return ResponseMessage.error(CommonConstants.ERROR_MESSAGE_PARAMETER_IS_NULL);
        }

        // 做物理删除
        functionalAttributesService.removeByIds(ids);
        return ResponseMessage.ok();
    }

    @ApiOperation(value = "创建新增数据", notes = "创建新增数据")
    @PostMapping("/createData")
    public ResponseMessage<String> createData(@RequestBody FunctionalAttributesCreateRequest dataRequest) {
        // 判空
        if (Objects.isNull(dataRequest) || Objects.isNull(dataRequest.getDataType())
                || Objects.isNull(dataRequest.getOperatingSystem()) || Objects.isNull(dataRequest.getFunctionalAttributes())) {
            return ResponseMessage.error(CommonConstants.ERROR_MESSAGE_PARAMETER_IS_NULL);
        }

        // 增加属性id 唯一性校验
        boolean isPass = functionalAttributesService.checkIdOnly(null, dataRequest.getFunctionalAttributes());
        if (!isPass) {
            return ResponseMessage.error(CommonConstants.ERROR_MESSAGE_DATA_ALREADY_EXIST);
        }

        functionalAttributesService.createData(dataRequest);
        return ResponseMessage.ok();
    }

    @ApiOperation(value = "更新数据", notes = "更新数据")
    @PostMapping("/updateData")
    public ResponseMessage<String> updateData(@RequestBody FunctionalAttributesUpdateRequest dataRequest) {
        // 判空
        if (Objects.isNull(dataRequest) || Objects.isNull(dataRequest.getId())) {
            return ResponseMessage.error(CommonConstants.ERROR_MESSAGE_PARAMETER_IS_NULL);
        }

        // 增加属性id 唯一性校验
        boolean isPass = functionalAttributesService.checkIdOnly(dataRequest.getId(), dataRequest.getFunctionalAttributes());
        if (!isPass) {
            return ResponseMessage.error(CommonConstants.ERROR_MESSAGE_DATA_ALREADY_EXIST);
        }

        String result = functionalAttributesService.updateData(dataRequest);

        if (StrUtil.isNotEmpty(result)) {
            return ResponseMessage.error(result);
        }

        return ResponseMessage.ok();
    }

    @ApiOperation(value = "检查索引唯一性", notes = "检查索引唯一性, 属性配置索引需要全局唯一")
    @PostMapping("/checkIdUniqueness")
    public ResponseMessage<String> checkIdUniqueness(@RequestBody FunctionalAttributesCheckIndexRequest request) {
        // 判空
        if (Objects.isNull(request) || Objects.isNull(request.getId())
                || StrUtil.isEmpty(request.getFunctionalAttributes())) {
            return ResponseMessage.error(CommonConstants.ERROR_MESSAGE_PARAMETER_IS_NULL);
        }

        // 根据id获取详情
        boolean checkIdOnly = functionalAttributesService.checkIdOnly(request.getId(), request.getFunctionalAttributes());
        if (!checkIdOnly) {
            return ResponseMessage.error(CommonConstants.ERROR_MESSAGE_DATA_ALREADY_EXIST_ID);
        }
        return ResponseMessage.ok(CommonConstants.ERROR_MESSAGE_CHECK_PASS);
    }
}
