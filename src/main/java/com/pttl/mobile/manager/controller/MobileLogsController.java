package com.pttl.mobile.manager.controller;

import com.pttl.mobile.manager.domain.entity.MobileLogs;
import com.pttl.mobile.manager.lib.ResponseMessage;
import com.pttl.mobile.manager.service.MobileLogsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 移动端日志控制层
 *
 * <AUTHOR>
 * @date 2024/5/8
 */
@RestController
@RequestMapping("/mobileLogs")
public class MobileLogsController {

    /**
    * 服务对象
    */
    @Autowired
    private MobileLogsService mobileLogsService;

    /**
    * 通过主键查询单条数据
    *
    * @param id 主键
    * @return 单条数据
    */
    @GetMapping("/getById")
    public ResponseMessage<MobileLogs> selectOne(Long id) {
        return ResponseMessage.ok(mobileLogsService.selectByPrimaryKey(id));
    }

}
