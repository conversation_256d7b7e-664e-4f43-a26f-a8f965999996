package com.pttl.mobile.manager.controller;

import com.pttl.mobile.manager.constant.UserConstant;
import com.pttl.mobile.manager.constant.EnvEnum;
import com.pttl.mobile.manager.constant.SystemPlatformEnum;
import com.pttl.mobile.manager.domain.bo.MobileManagerUser;
import com.pttl.mobile.manager.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @description: 切换环境帮助类
 * @date 2022/1/24 14:48
 */
@Slf4j
@Component
public class EnvUtil {
    private static RedisUtil redisUtil;

    @Autowired
    public EnvUtil(RedisUtil redisUtil) {
        EnvUtil.redisUtil = redisUtil;
    }

    public static Integer getCurrentEnv(Integer systemPlatform) {
        String key = UserConstant.MOBILE_MANAGER_ENV_ONLINEOFFICE_REDIS_KEY;
        if (systemPlatform == SystemPlatformEnum.MALL.getSystemPlatform()) {
            key = UserConstant.MOBILE_MANAGER_ENV_MALL_REDIS_KEY;
        }

        return getEnvFromRedis(key);
    }

    public static Integer getCurrentEnv() {
        String key = UserConstant.MOBILE_MANAGER_ENV_ONLINEOFFICE_REDIS_KEY;
        if (MobileManagerUser.getCurrentUser().getSystemPlatform() == SystemPlatformEnum.MALL.getSystemPlatform()) {
            key = UserConstant.MOBILE_MANAGER_ENV_MALL_REDIS_KEY;
        }

        return getEnvFromRedis(key);
    }

    private static Integer getEnvFromRedis(String key) {
        Integer env = redisUtil.getInt(key);
        if (env == null) {
            redisUtil.set(key, EnvEnum.PRD.getEnv(), -1L);
            return EnvEnum.PRD.getEnv();
        } else {
            return env;
        }
    }
}
