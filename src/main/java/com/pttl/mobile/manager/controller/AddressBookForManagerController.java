/*
package com.pttl.mobile.manager.controller;

import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageInfo;
import com.pttl.mobile.manager.config.ApplicationDefinedConfiguration;
import com.pttl.mobile.manager.constant.CommonConstants;
import com.pttl.mobile.manager.domain.dto.AddressBookOfDeptExtendDTO;
import com.pttl.mobile.manager.domain.dto.AddressBookOfPersonnelRankingDTO;
import com.pttl.mobile.manager.excel.dto.ExportAddressBookPersonDataDTO;
import com.pttl.mobile.manager.excel.util.ExportExcelFileUtil;
import com.pttl.mobile.manager.lib.ResponseMessage;
import com.pttl.mobile.manager.mobile.dto.OrganizationalStructureInfoDTO;
import com.pttl.mobile.manager.mobile.dto.OrganizationalStructureQueryDTO;
import com.pttl.mobile.manager.mobile.dto.PersonInfoDTO;
import com.pttl.mobile.manager.mobile.dto.PersonInfoOfQueryDTO;
import com.pttl.mobile.manager.mobile.ws.dto.MobilAddressBookExtendDTO;
import com.pttl.mobile.manager.mobile.ws.entity.PersonInfoDO;
import com.pttl.mobile.manager.mobile.ws.service.MobilAddressBookExtendService;
import com.pttl.mobile.manager.mobile.ws.service.OrganizationalStructureInfoService;
import com.pttl.mobile.manager.mobile.ws.service.PersonInfoService;
import com.pttl.mobile.manager.mobile.ws.service.PersonSortInfoService;
import com.pttl.mobile.manager.util.BeanMapper;
import com.pttl.mobile.manager.util.StringUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Objects;

*/
/**
 * 管理端端 获取通讯录相关接口
 *
 * <AUTHOR>
 * @date 2023/1/10
 **//*

@Slf4j
@RestController
@RequestMapping("/addressBook")
@Api(tags = "管理端-通讯录接口")
public class AddressBookForManagerController {

    */
/**
     * 人员信息
     *//*

    @Resource
    private PersonInfoService personInfoService;

    */
/**
     * 组织架构
     *//*

    @Resource
    private OrganizationalStructureInfoService organizationalStructureInfoService;

    */
/**
     * 通讯录扩展字段
     *//*

    @Resource
    private MobilAddressBookExtendService mobilAddressBookExtendService;

    */
/**
     * 动态配置
     *//*

    @Resource
    private ApplicationDefinedConfiguration applicationDefinedConfiguration;


    */
/**
     * 通讯录 自定义部门下人员排序 服务层
     *//*

    @Resource
    private PersonSortInfoService personSortInfoService;

    @ApiOperation(value = "获取人员详情", notes = "获取人员详情信息")
    @GetMapping("/getUserDetail/{id}")
    public ResponseMessage<PersonInfoDO> getUserDetail(@PathVariable Long id) {
        // 判空
        if (Objects.isNull(id)) {
            return ResponseMessage.error(CommonConstants.ERROR_MESSAGE_PARAMETER_IS_NULL);
        }

        PersonInfoDO personInfoDO = personInfoService.selectByPrimaryKey(id);
        return ResponseMessage.ok(personInfoDO);
    }

    @ApiOperation(value = "通讯录人员列表及条件筛选", notes = "获取录人员列表,通过部门组织id和姓名/邮箱/手机号条件检索功能接口, 默认查询全部人员")
    @PostMapping("/getPersonInfoList")
    public ResponseMessage<PageInfo<PersonInfoDTO>> getPersonListInfo(
            @RequestBody PersonInfoOfQueryDTO queryVO) {

        PageInfo<PersonInfoDTO> results = personInfoService.listByNameAndDeptId(queryVO);
        return ResponseMessage.ok(results);
    }

    @ApiOperation(value = "通讯录人员信息导出", notes = "通过部门组织id和姓名/邮箱/手机号条件检索后, 默认全部人员, 通讯录人员信息导出")
    @PostMapping("/exportingUserInformation")
    public void exportingUserInformation(
            @RequestBody PersonInfoOfQueryDTO queryVO, HttpServletResponse response) throws IOException {
        // 分页 取数据最大 即符合条件的全部导出
        queryVO.setPageNum(1);
        queryVO.setPageSize(30000);

        // 查询 导出
        PageInfo<PersonInfoDTO> results = personInfoService.listByNameAndDeptId(queryVO);
        List<ExportAddressBookPersonDataDTO> personDataList = BeanMapper.mapList(results.getList(), ExportAddressBookPersonDataDTO.class);
        ExportExcelFileUtil.dataExport("通讯录人员信息", "人员列表", personDataList, ExportAddressBookPersonDataDTO.class, response);
    }


    @ApiOperation(value = "获取组织架构", notes = "根据部门id(dept_id) 获取组织架构, 默认获取一级部门")
    @PostMapping("/getOrganizationalStructureList")
    public ResponseMessage<List<OrganizationalStructureInfoDTO>> getOrganizationalStructureList(@RequestBody OrganizationalStructureQueryDTO queryDTO) {

        List<OrganizationalStructureInfoDTO> results = organizationalStructureInfoService
                .listOrganizationalStructureInfo(queryDTO.getDeptId(), queryDTO.getNameDesc());
        return ResponseMessage.ok(results);
    }


    @ApiOperation(value = "修改部门头像", notes = "根据部门id修改头像")
    @PostMapping("/updateDeptExtend")
    public ResponseMessage<String> updateDeptExtend(
            @RequestBody AddressBookOfDeptExtendDTO queryVO) {
        // 判空
        if (Objects.isNull(queryVO) || StrUtil.isEmpty(queryVO.getDeptId())) {
            return ResponseMessage.error(CommonConstants.ERROR_MESSAGE_PARAMETER_IS_NULL);
        }

        // 处理扩展头像
        processCustomAvatarExtend(queryVO);

        String accessAddress = StringUtil.getApplicationIconUr(queryVO.getCustomAvatar(),
                applicationDefinedConfiguration.getSystemManager().getApplicationIconUrlPrefix());
        return ResponseMessage.ok(accessAddress);
    }

    */
/**
     * 处理部门扩展头像
     *
     * @param queryVO 参数
     *//*

    private void processCustomAvatarExtend(AddressBookOfDeptExtendDTO queryVO) {
        MobilAddressBookExtendDTO mobilAddressBookExtendDTO = new MobilAddressBookExtendDTO();
        // 这里传为部门头像  需求说的晚 直接使用了
        mobilAddressBookExtendDTO.setPersonInfoId(queryVO.getDeptId());
        mobilAddressBookExtendDTO.setCustomAvatar(queryVO.getCustomAvatar());
        mobilAddressBookExtendService.updateAddressBookExtend(mobilAddressBookExtendDTO);
    }

    @ApiOperation(value = "通讯录开关", notes = "通讯录开关")
    @GetMapping("/contactsSwitch")
    public ResponseMessage<String> contactsSwitch(@RequestParam("employeeId") String employeeId) {
        log.info("into contactsSwitch {}...", employeeId);
        if (StrUtil.isEmpty(employeeId)){
            return ResponseMessage.error(CommonConstants.ERROR_MESSAGE_PARAMETER_IS_NULL);
        }

        mobilAddressBookExtendService.updateContactsSwitch(employeeId);

        return ResponseMessage.ok();
    }


    */
/*@ApiOperation(value = "通讯录列表及条件筛选", notes = "获取录列表,通过名称和部门组织id条件检索功能接口")
    @PostMapping("/getAddressBookList")
    public ResponseMessage<List<AddressBookAndPersonDTO>> getAddressBookListAndPersonListInfo(
            @RequestBody AddressBookInfoOfQueryDTO queryVO) {

        List<AddressBookAndPersonDTO> results = organizationalStructureInfoService.listAddressBookAndPersonInfo(queryVO);
        return ResponseMessage.ok(results);
    }*//*



    @ApiOperation(value = "手动同步", notes = "手动同步, 会立即同步人员和部门信息")
    @GetMapping("/manualSynchronization")
    public ResponseMessage<String> manualSynchronization() {

        try {
            log.info("start manualSynchronization personInfo ...");
            personInfoService.automaticSynchronization2Database();
            log.info("end manualSynchronization personInfo success ...");

            log.info("start manualSynchronization organizationalStructureInfo ...");
            organizationalStructureInfoService.automaticSynchronization2Database();
            log.info("end organizationalStructureInfo success ...");
        } catch (Exception e) {
            return ResponseMessage.error("同步失败");
        }
        return ResponseMessage.ok();
    }


    @ApiOperation(value = "部门人员排序-查询指定部门下人员", notes = "部门人员排序-通过部门组织id查询指定部门下人员")
    @GetMapping("/queryAllPersonnelInDept")
    public ResponseMessage<List<PersonInfoDTO>> queryAllPersonnelInDept(
            @RequestParam("deptId") Integer deptId) {
        // 判空
        if (Objects.isNull(deptId)) {
            return ResponseMessage.error(CommonConstants.ERROR_MESSAGE_PARAMETER_IS_NULL);
        }

        // 分页 取数据最大
        PersonInfoOfQueryDTO queryVO = new PersonInfoOfQueryDTO();
        queryVO.setPageNum(1);
        queryVO.setPageSize(30000);
        queryVO.setDeptId(deptId);

        // 查询
        PageInfo<PersonInfoDTO> results = personInfoService.listByNameAndDeptId(queryVO);
        List<PersonInfoDTO> list = personSortInfoService.sortPersonInfoDTO(results);
        return ResponseMessage.ok(list);
    }


    @ApiOperation(value = "部门人员排序-更新修改, 交换指定人员id的排序", notes = "根据部门id修改, 该部门下的人员显示顺序")
    @PostMapping("/updateDeptPersonSort")
    public ResponseMessage<String> updateDeptPersonSort(
            @RequestBody AddressBookOfPersonnelRankingDTO queryVO) {
        // 判空
        if (Objects.isNull(queryVO) || Objects.isNull(queryVO.getDeptId())
                || StrUtil.isEmpty(queryVO.getEmployeeId1())
                || StrUtil.isEmpty(queryVO.getEmployeeId2())) {
            return ResponseMessage.error(CommonConstants.ERROR_MESSAGE_PARAMETER_IS_NULL);
        }

        // 交换 员工id排序
        personSortInfoService.exchangeEmployeeSort(queryVO);

        return ResponseMessage.ok();
    }
}
*/
