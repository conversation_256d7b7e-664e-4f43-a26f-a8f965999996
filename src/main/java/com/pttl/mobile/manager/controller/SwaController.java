package com.pttl.mobile.manager.controller;

import com.pttl.mobile.manager.domain.dto.SwaExtendDTO;
import com.pttl.mobile.manager.domain.request.SwaCreateRequest;
import com.pttl.mobile.manager.lib.ResponseMessage;
import com.pttl.mobile.manager.service.SwaService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/swa")
@Api(tags = "密码管家相关")
public class SwaController {
    @Autowired
    private SwaService swaService;

    @ApiOperation(value = "获取密码管家", notes = "获取密码管家")
    @GetMapping()
    public ResponseMessage<List<SwaExtendDTO>> getSwa() {
        return swaService.getSwa();
    }

    @ApiOperation(value = "创建密码管家", notes = "创建密码管家")
    @PostMapping()
    public ResponseMessage<Boolean> createSwa(@RequestBody SwaCreateRequest swaCreateParam) {
        return swaService.createSwa(swaCreateParam);
    }

    @ApiOperation(value = "修改密码管家", notes = "修改密码管家")
    @PatchMapping()
    public ResponseMessage<Boolean> updateSwa(@RequestBody SwaCreateRequest scp) {
        return swaService.updateSwa(scp);
    }

    @ApiOperation(value = "删除密码管家", notes = "删除密码管家")
    @DeleteMapping()
    public ResponseMessage<Boolean> deleteSwaByIds(@RequestBody List<String> ids) {
        return swaService.deleteSwaByIds(ids);
    }
}
