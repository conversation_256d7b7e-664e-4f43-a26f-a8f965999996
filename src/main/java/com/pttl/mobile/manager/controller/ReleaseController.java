package com.pttl.mobile.manager.controller;

import com.pttl.mobile.manager.lib.ResponseMessage;
import com.pttl.mobile.manager.domain.po.Release;
import com.pttl.mobile.manager.domain.po.ReleaseType;
import com.pttl.mobile.manager.domain.dto.ReleaseExtendDTO;
import com.pttl.mobile.manager.service.ReleaseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/release")
@Api(tags = "发版管理")
public class ReleaseController {
    @Autowired
    private ReleaseService releaseService;

    @ApiOperation(value = "获取发版类型数据", notes = "获取发版类型数据")
    @GetMapping("/type")
    public ResponseMessage<List<ReleaseType>> getReleaseType() {
        return releaseService.getReleaseType();
    }

    @ApiOperation(value = "获取发版历史数据", notes = "获取发版历史数据")
    @GetMapping()
    public ResponseMessage<List<ReleaseExtendDTO>> getReleaseHistory(Integer type, String version, String keyword, Date startDate, Date endDate) {
        return releaseService.getReleaseHistory(type, version, keyword, startDate, endDate);
    }

    @ApiOperation(value = "创建新的发版数据", notes = "创建新的发版数据")
    @PostMapping()
    public ResponseMessage<Boolean> createRelease(@RequestBody Release release) {
        return releaseService.createRelease(release);
    }

    @ApiOperation(value = "删除发版记录", notes = "删除发版记录")
    @DeleteMapping()
    public ResponseMessage<Boolean> deleteReleaseByIds(@RequestBody List<String> ids) {
        return releaseService.deleteReleaseByIds(ids);
    }
}
