package com.pttl.mobile.manager.controller;

import com.pttl.mobile.manager.domain.dto.*;
import com.pttl.mobile.manager.lib.ResponseMessage;
import com.pttl.mobile.manager.service.ClientService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/client")
@Api(tags = "客户端(IOS/安卓)调用API")
public class ClientController {
    @Autowired
    private ClientService clientService;

    @ApiOperation(value = "获取最新的发版信息", notes = "获取最新的发版信息")
    @GetMapping("/release/lastversion")
    public ResponseMessage<ReleaseClientDTO> getReleaseLastversion(@RequestParam("type") Integer type, @RequestParam("operatingSystem") Integer operatingSystem) {
        return clientService.getReleaseLastversion(type, operatingSystem);
    }

    @ApiOperation(value = "获取客户端需要的配置", notes = "获取客户端需要的配置")
    @GetMapping(value = "/configuration")
    public ResponseMessage<ConfigurationClientDTO> getConfiguration() {
        return clientService.getConfiguration();
    }

    @ApiOperation(value = "获取应用列表", notes = "获取应用列表")
    @GetMapping(value = "/application")
    public ResponseMessage<List<ApplicationClientDTO>> getApplication() {
        return clientService.getApplication();
    }

    @ApiOperation(value = "获取应用说明数据", notes = "获取应用说明数据")
    @PostMapping(value = "/application/manifest")
    public ResponseMessage<List<ApplicationAdapterClientDTO>> getApplicationManifest(@RequestBody List<String> ids) {
        return clientService.getApplicationManifest(ids);
    }

    @ApiOperation(value = "获取密码管家列表", notes = "获取密码管家列表")
    @GetMapping(value = "/swa")
    public ResponseMessage<List<SwaClientDTO>> swa() {
        return clientService.getSwa();
    }
}
