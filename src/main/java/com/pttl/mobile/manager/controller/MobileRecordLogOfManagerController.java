package com.pttl.mobile.manager.controller;

import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageInfo;
import com.pttl.mobile.manager.constant.CommonConstants;
import com.pttl.mobile.manager.domain.dto.MobileRecordLogDetailsDTO;
import com.pttl.mobile.manager.domain.entity.MobileRecordLogDO;
import com.pttl.mobile.manager.domain.request.MobileRecordLogDetailsRequest;
import com.pttl.mobile.manager.domain.request.MobileRecordLogPageRequest;
import com.pttl.mobile.manager.lib.ResponseMessage;
import com.pttl.mobile.manager.mobile.exception.ErrorMessageException;
import com.pttl.mobile.manager.mobile.util.TableNameDateUtil;
import com.pttl.mobile.manager.service.MobileRecordLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * 移动上报操作日志 控制层
 *
 * <AUTHOR>
 * @date 2022/11/11
 **/

@Slf4j
@RestController
@RequestMapping("/mobileRecordLog")
@Api(tags = "移动端业务操作日志")
public class MobileRecordLogOfManagerController {

    /**
     * 移动端业务操作记录日志 mysql Service
     */
    @Resource
    private MobileRecordLogService mobileRecordLogService;

    /**
     * 日志表保存月数 例如3, 表示保留近三个月的;
     */
    @Value("${schedule.delete.quantity}")
    private Integer logQuantity;


    @ApiOperation(value = "获取分页数据信息", notes = "获取分页数据信息")
    @PostMapping("/pageInfo")
    public ResponseMessage<PageInfo<MobileRecordLogDO>> pageInfoList(@RequestBody MobileRecordLogPageRequest pageRequest) {
        try {
            // 默认查本月份 只能查近三个月的
            pageRequest.setYearMonth(TableNameDateUtil.getYearMonthFromDate(pageRequest.getStartTime(), logQuantity));
        } catch (ErrorMessageException errorMessageException) {
            return ResponseMessage.error(errorMessageException.getMessage());
        }

        PageInfo<MobileRecordLogDO> pageInfo = mobileRecordLogService.pageInfo(pageRequest);
        return ResponseMessage.ok(pageInfo);
    }

    @ApiOperation(value = "获取详情信息", notes = "获取详情信息")
    @PostMapping("/dataDetails")
    public ResponseMessage<MobileRecordLogDetailsDTO> dataDetails(@RequestBody MobileRecordLogDetailsRequest request) {
        // 判空
        if (Objects.isNull(request) || Objects.isNull(request.getId())) {
            return ResponseMessage.error(CommonConstants.ERROR_MESSAGE_PARAMETER_IS_NULL);
        }

        // 默认查本月份
        if (StrUtil.isEmpty(request.getYearMonth())) {
            request.setYearMonth(TableNameDateUtil.getCurrentYearMonth());
        }

        // 根据id获取详情
        MobileRecordLogDetailsDTO detailsDTO = mobileRecordLogService.getDetailsById(request);
        return ResponseMessage.ok(detailsDTO);
    }


}
