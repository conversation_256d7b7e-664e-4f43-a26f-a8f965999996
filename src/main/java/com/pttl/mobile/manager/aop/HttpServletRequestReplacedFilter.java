package com.pttl.mobile.manager.aop;

import org.springframework.context.annotation.Configuration;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

/**
 * Filter 中将 ServletRequest 替换为 MyRequestWrapper
 *
 * <AUTHOR>
 * @date 2022/11/4
 **/
@Configuration
public class HttpServletRequestReplacedFilter implements Filter {

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        // not do
    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
        ServletRequest servletWrapper = null;
        if (servletRequest instanceof HttpServletRequest) {
            servletWrapper = new RequestWrapper((HttpServletRequest) servletRequest);
        }
        if (servletRequest == null) {
            filterChain.doFilter(null, servletResponse);
        } else {
            try {
                filterChain.doFilter(servletWrapper, servletResponse);
            } catch (IOException | ServletException e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    public void destroy() {
        // not do
    }
}
