package com.pttl.mobile.manager.aop;

import cn.hutool.extra.servlet.ServletUtil;
import com.alibaba.fastjson.JSON;
import com.pttl.mobile.manager.domain.bo.MobileManagerUser;
import com.pttl.mobile.manager.domain.dto.ManagerOperationLogDTO;
import com.pttl.mobile.manager.domain.entity.ManagerOperationLogDO;
import com.pttl.mobile.manager.lib.ResponseMessage;
import com.pttl.mobile.manager.service.ManagerOperationLogService;
import com.pttl.mobile.manager.util.AspectSupportUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.util.Objects;
import java.util.StringJoiner;

/**
 * 管理端操作日志point
 *
 * <AUTHOR>
 * @date 2022/11/3
 **/
@Slf4j
@Aspect
@Component
public class ManagerOperationLogAspect {

    /**
     * 操作日志服务层
     */
    @Resource
    private ManagerOperationLogService managerOperationLogService;


    /**
     * 设置操作日志切入点 记录操作日志 扫描所有controller包下操作
     */
    @Pointcut("execution(* com.pttl.mobile.manager.controller..*.*(..))")
    public void operationLogPointCut() {
        // not do
    }

    /**
     * 设置操作异常切入点记录异常日志 扫描所有controller包下操作
     */
    @Pointcut("execution(* com.pttl.mobile.manager.controller..*.*(..))")
    public void operationExceptionLogPointCut() {
        // not do
    }

    @AfterReturning(value = "operationLogPointCut()", returning = "keys")
    public void processManageOperateLog(JoinPoint joinPoint, Object keys) {
        String methodName = AspectSupportUtil.getMethodName(joinPoint);
        log.info("current method: {}, request success...", methodName);

        // 日志数据抽取
        ManagerOperationLogDTO managerOperationLogDTO = logDataExtract(joinPoint, ManagerOperationLogDO.OPERATION_STATUS_SUCCESS);

        // 返回结果
        String responseData = JSON.toJSONString(keys);
        managerOperationLogDTO.setOperationResponseParam(responseData);

        // 方便查询这里记录日志
        log.info("current response params: {}", responseData);

        // 入库
        managerOperationLogService.saveManagerOperationLog(managerOperationLogDTO);

        log.info("current method: {}, request success  finish...", methodName);
    }

    @AfterThrowing(value = "operationExceptionLogPointCut()", throwing = "e")
    public void processManageOperateExceptionLog(JoinPoint joinPoint, Throwable e) {
        String methodName = AspectSupportUtil.getMethodName(joinPoint);
        log.info("current  method: {},request exception...", methodName);

        // 日志数据抽取
        ManagerOperationLogDTO managerOperationLogDTO = logDataExtract(joinPoint, ManagerOperationLogDO.OPERATION_STATUS_EXCEPTION);

        // 异常信息
        String stackTraceString = AspectSupportUtil.stackTraceToString(e.getClass().getName(), e.getMessage(), e.getStackTrace());
        managerOperationLogDTO.setExceptionMessage(stackTraceString);
        managerOperationLogDTO.setExceptionName(e.getClass().getName());

        // 方便查询这里记录日志
        log.info("current exception: {}, {}", e.getClass().getName(), stackTraceString);

        // 异常时 捕捉的默认返回异常信息
        String errorMag = JSON.toJSONString(ResponseMessage.error("系统异常 请稍后再试"));
        managerOperationLogDTO.setOperationResponseParam(errorMag);

        // 方便查询这里记录日志
        log.info("current response params: {}", errorMag);

        // 入库
        managerOperationLogService.saveManagerOperationLog(managerOperationLogDTO);

        log.info("current  method: {},request exception finish...", methodName);
    }

    /**
     * 日志数据抽取
     *
     * @param joinPoint       点
     * @param operationStatus 日志状态
     * @return 1
     */
    private ManagerOperationLogDTO logDataExtract(JoinPoint joinPoint, int operationStatus) {
        // 获取RequestAttributes
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        // 从获取RequestAttributes中获取HttpServletRequest的信息
        HttpServletRequest request = (HttpServletRequest) requestAttributes
                .resolveReference(RequestAttributes.REFERENCE_REQUEST);

        ManagerOperationLogDTO managerOperationLogDTO = new ManagerOperationLogDTO();
        managerOperationLogDTO.setOperationStatus(operationStatus);

        // 从切面织入点处通过反射机制获取织入点处的方法
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        // 切入点类注解
        Api apiAnnotation = joinPoint.getTarget().getClass().getAnnotation(Api.class);
        // 模块
        StringJoiner consoleNoteJoiner = new StringJoiner("->");
        if (Objects.nonNull(apiAnnotation)) {
            managerOperationLogDTO.setOperationModule(apiAnnotation.tags()[0]);
            consoleNoteJoiner.add(apiAnnotation.tags()[0]);
        }

        // 获取切入点所在的方法 获取操作
        Method method = signature.getMethod();
        ApiOperation apiOperationAnnotation = method.getAnnotation(ApiOperation.class);
        // 操作类型
        if (Objects.nonNull(apiOperationAnnotation)) {
            managerOperationLogDTO.setOperationType(apiOperationAnnotation.value());
            consoleNoteJoiner.add(apiOperationAnnotation.value());
            managerOperationLogDTO.setOperationDescription(apiOperationAnnotation.notes());
            consoleNoteJoiner.add(apiOperationAnnotation.notes());
        }

        // 请求的参数
        String body = AspectSupportUtil.getRequestBody(request);
        managerOperationLogDTO.setOperationRequestParam(body);

        // 方便查询这里记录日志
        log.info("current request url: {}, notes: {}, params: {}", request.getRequestURI(), consoleNoteJoiner, body);

        managerOperationLogDTO.setOperationMethod(AspectSupportUtil.getMethodName(joinPoint));

        // 请求URI
        managerOperationLogDTO.setOperationUri(request.getRequestURI());

        // 操作员名称  目前没有用户id
        Object userName = request.getSession().getAttribute(MobileManagerUser.ATTR_USER_NAME);
        // 考虑不登陆的情况 防止NPE
        if (Objects.nonNull(userName)) {
            managerOperationLogDTO.setOperationUserName(userName.toString());
        }

        // ip
        String clientIp = ServletUtil.getClientIP(request, "");
        managerOperationLogDTO.setOperationIp(clientIp);

        return managerOperationLogDTO;
    }

}
