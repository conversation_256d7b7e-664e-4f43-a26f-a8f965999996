package com.pttl.mobile.manager.lib;

import cn.hutool.core.io.IoUtil;
import com.pttl.mobile.manager.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.Consts;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import javax.net.ssl.SSLContext;
import java.net.URI;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 短信验证工具
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@Component
public class SmsUtil {

    @Value("${sms.smswsdl}")
    private String smswsdl;

    @Value("${sms.operID}")
    private String operID;

    @Value("${sms.operLogin}")
    private String operLogin;

    @Value("${sms.operPass}")
    private String operPass;

    @Value("${sms.maxSize}")
    private Integer maxSize;

    @Value("${sms.smsValidHour}")
    private Integer smsValidHour;

    /**
     * 消息模板
     */
    public static final String MESSAGE_CONTENT_TEMPLATE = "后台管理登录验证码: %s，验证码8小时内有效";

    /**
     * 短信发送
     *
     * @param phoneNumber 目标手机号
     * @param content     内容  例:【电科太力】移动端后台登录验证码: ****
     * @return 状态
     */
    public boolean sendSmsMessage(String phoneNumber, String content) {
        try {
            String requestBody = getRequestBody(phoneNumber, content);
            String result = sendRquest(requestBody);

            return result != null && result.contains("<ns1:Count>1</ns1:Count>");
        } catch (Exception e) {
            log.error(e.getMessage());

            return false;
        }
    }

    /**
     * 免证书
     */
    private CloseableHttpClient getSSLCloseableHttpClient() {
        try {
            SSLContext sslContext = new SSLContextBuilder().loadTrustMaterial(null, (chain, authType) -> true).build();
            SSLConnectionSocketFactory sslSf = new SSLConnectionSocketFactory(sslContext, new String[]{"TLSv1.2"}, null, NoopHostnameVerifier.INSTANCE);

            return HttpClients.custom().setSSLSocketFactory(sslSf).build();
        } catch (KeyManagementException | NoSuchAlgorithmException | KeyStoreException e) {
            log.error(e.getMessage());
        }

        return null;
    }

    private String sendRquest(String requestBody) {
        HttpPost httpPost;
        CloseableHttpResponse response = null;
        CloseableHttpClient httpclient = getSSLCloseableHttpClient();
        if (httpclient == null) {
            log.error("getSSLCloseableHttpClient错误!");

            return null;
        }

        try {
            log.info(String.format("短信接口发送数据: %s", requestBody));
            httpPost = new HttpPost(new URI(smswsdl));
            httpPost.setHeader("Accept-Encoding", "identity");
            httpPost.setHeader("Content-Type", "text/xml;charset=UTF-8");
            httpPost.setEntity(new StringEntity(requestBody, ContentType.create("text/xml", Consts.UTF_8)));
            response = httpclient.execute(httpPost);

            String result = EntityUtils.toString(response.getEntity());
            log.info(String.format("短信接口返回数据: %s", result));

            return result;
        } catch (Exception e) {
            log.error(e.getMessage());
        } finally {
            IoUtil.close(response);
            IoUtil.close(httpclient);
        }

        return null;
    }

    private String getRequestBody(String phoneNumber, String content) {
        String timeStamp = new SimpleDateFormat("MMddHHmmss").format(new Date());
        String pass = new MD5().getMD5ofStr(operID + operPass + timeStamp);
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("tem:CorpID", Long.valueOf(operID));
        dataMap.put("tem:LoginName", operLogin);
        dataMap.put("tem:Password", pass);
        dataMap.put("tem:TimeStamp", timeStamp);
        dataMap.put("tem:AddNum", StringUtil.EMPTY);
        dataMap.put("tem:Timer", StringUtil.EMPTY);
        dataMap.put("tem:LongSms", 0L);
        dataMap.put("tem:Content", content);
        Map<String, Object> mobileListMap = new HashMap<>();
        dataMap.put("tem:MobileList", mobileListMap);
        Map<String, Object> mobileListGroupMap = new HashMap<>();
        mobileListMap.put("tem:MobileListGroup", mobileListGroupMap);
        mobileListGroupMap.put("tem:Mobile", phoneNumber);
        mobileListGroupMap.put("tem:MsgID", StringUtil.EMPTY);

        StringBuilder wsXml = new StringBuilder();
        wsXml.append("<soapenv:Envelope xmlns:soapenv=\"http://schemas.xmlsoap.org/soap/envelope/\" xmlns:tem=\"http://tempuri.org/\">");
        wsXml.append("<soapenv:Header/>");
        wsXml.append("<soapenv:Body>");
        wsXml.append("<tem:Sms_Send>");
        generateMapElementXml(wsXml, dataMap);
        wsXml.append("</tem:Sms_Send>");
        wsXml.append("</soapenv:Body>");
        wsXml.append("</soapenv:Envelope>");

        return wsXml.toString();
    }

    @SuppressWarnings("unchecked")
    private static void generateMapElementXml(StringBuilder wsXml, Map<String, Object> dataMap) {
        for (String key : dataMap.keySet()) {
            if (!StringUtil.isEmpty(key)) {
                Object obj = dataMap.get(key);
                if (obj instanceof Map) {
                    wsXml.append(String.format("<%s>", key));
                    generateMapElementXml(wsXml, (Map<String, Object>) obj);
                    wsXml.append(String.format("</%s>", key));
                } else if (obj instanceof List) {
                    List<Map<String, Object>> list = (List<Map<String, Object>>) obj;
                    if (list.size() > 0) {
                        for (Map<String, Object> m : list) {
                            if (null == m || m.keySet().size() <= 0) {
                                continue;
                            }
                            wsXml.append(String.format("<%s>", key));
                            generateMapElementXml(wsXml, m);
                            wsXml.append(String.format("</%s>", key));
                        }
                    }
                } else {
                    wsXml.append(String.format("<%s>", key));
                    wsXml.append(null != obj ? obj.toString() : "");
                    wsXml.append(String.format("</%s>", key));
                }
            }
        }
    }
}
