/* Copyright 2002-${Year} the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License. */
package com.pttl.mobile.manager.lib;

import java.io.Serializable;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
public class ResponseMessage<T> implements Serializable {

    private static final long serialVersionUID = -6593586397458844802L;

    public static final int SUCCESS = 200;
    public static final String SUCCESS_MESSAGE = "成功";

    /**
     * 重复操作，且上一次操作已经成功
     */
    @Deprecated
    public static final int ALREADY_OK = 201;

    public static final int ERROR = 500;

    /**
     * 需要登录
     */
    public static final int NEED_LOGIN = 401;

    /**
     * 不存在，或没权限查看
     */
    public static final int NOT_FOUND = 404;

    /**
     * fallback
     */
    public static final int FALL_BACK = 600;

    private int code;

    private String message;

    private T body;

    private Map<String, Object> validateError;

    public ResponseMessage() {
    }


    public ResponseMessage(int code, T body) {
        this.code = code;
        this.body = body;
    }

    public ResponseMessage(int code, String message, T body) {
        this.code = code;
        this.message = message;
        this.body = body;
    }

    public boolean isOk() {
        return getCode() == SUCCESS;
    }

    public boolean isFallback() {
        return getCode() == FALL_BACK;
    }

    public static <T> ResponseMessage<T> ok() {
        return new ResponseMessage(SUCCESS, SUCCESS_MESSAGE, null);
    }

    public static <T> ResponseMessage<T> ok(T body) {
        return new ResponseMessage(SUCCESS, SUCCESS_MESSAGE, body);
    }

    public static <T> ResponseMessage<T> status(int statusCode, String message, T body) {
        return new ResponseMessage(statusCode, message, body);
    }

    public static <T> ResponseMessage<T> error(int code) {
        return new ResponseMessage(code, null, null);
    }

    public static <T> ResponseMessage<T> error(String message) {
        return new ResponseMessage(ERROR, message, null);
    }

    public static <T> ResponseMessage<T> error(int errorCode, String message) {
        return new ResponseMessage(errorCode, message, null);
    }

    public static <T> ResponseMessage<T> fallback() {
        return new ResponseMessage(FALL_BACK, null, null);
    }

    public static <T> ResponseMessage<T> fallback(T body) {
        return new ResponseMessage(FALL_BACK, null, body);
    }

    public void setCode(int code) {
        this.code = code;
    }

    public int getCode() {
        return code;
    }

    public void setMessage(String message) {
        this.message = message;
    }


    public String getMessage() {
        return message;
    }


    public T getBody() {
        return body;
    }

    public void setBody(T body) {
        this.body = body;
    }


    public Map<String, Object> getValidateError() {
        return validateError;
    }

    public void setValidateError(Map<String, Object> validateError) {
        this.validateError = validateError;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }

        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        ResponseMessage<?> that = (ResponseMessage<?>) o;
        return code == that.code &&
                Objects.equals(message, that.message) &&
                Objects.equals(body, that.body);
    }

    @Override
    public int hashCode() {
        return Objects.hash(code, message, body);
    }

    @Override
    public String toString() {
        return "ResponseMessage{" +
                "code=" + code +
                ", message='" + message + '\'' +
                ", body=" + body +
                '}';
    }
}
