package com.pttl.mobile.manager.lib;

import com.pttl.mobile.manager.constant.EnvEnum;
import com.pttl.mobile.manager.constant.SystemPlatformEnum;
import com.pttl.mobile.manager.domain.bo.MobileManagerUser;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;

/**
 * 上下文对象
 * 管理两个参数:所有的数据库查询都会跟这两个参数相关 所以统一管理
 * 1.systemPlatform:平台 太力商城或者是在线办公平台
 * 2.env:生产还是预生产 用于发布版本时内部测试
 */
@Component
public class ContextUtil {

    private ContextUtil() {
    }

    public static Integer systemPlatform() {
        // 后台管理请求强制登录 所以可以从session中获取到平台信息
        Integer systemPlatform = MobileManagerUser.getCurrentUser().getSystemPlatform();
        if (systemPlatform != null) {
            return systemPlatform;
        }

        // 如果session为空 说明是客户端调用 需要在attribute中获取平台信息
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        if (request != null) {
            Object headerSystemPlatform = request.getAttribute(MobileManagerUser.ATTR_SYSTEM_PLAT_FORM);
            if (headerSystemPlatform != null) {
                systemPlatform = Integer.parseInt(headerSystemPlatform.toString());
            }
        }

        if (systemPlatform != null) {
            return systemPlatform;
        }

        return SystemPlatformEnum.ONLINEOFFICE.getSystemPlatform();
    }

    public static Integer currentEnv() {
        // 后台管理请求强制登录 所以可以从session中获取到平台信息
        Integer currentEnv = MobileManagerUser.getCurrentUser().getCurrentEnv();
        if (currentEnv != null) {
            return currentEnv;
        }

        // 如果session为空 说明是客户端调用 需要在attribute中获取平台信息
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        if (request != null) {
            Object headerCurrentEnv = request.getAttribute(MobileManagerUser.ATTR_CURRENT_ENV);
            if (headerCurrentEnv != null) {
                currentEnv = Integer.parseInt(headerCurrentEnv.toString());
            }
        }

        if (currentEnv != null) {
            return currentEnv;
        }

        return EnvEnum.PRD.getEnv();
    }
}
