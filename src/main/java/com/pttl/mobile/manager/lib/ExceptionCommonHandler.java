package com.pttl.mobile.manager.lib;

import com.fasterxml.jackson.databind.JsonMappingException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.http.HttpStatus;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import javax.servlet.http.HttpServletRequest;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.stream.Collectors;

@Slf4j
@ControllerAdvice
public class ExceptionCommonHandler {
    @ExceptionHandler(Exception.class)
    @ResponseBody
    public ResponseMessage exception(HttpServletRequest request, Exception ex) {
        String msg = "系统异常 请稍后再试";
        String exceptionMsg = this.getTrace(ex);

        if (log.isErrorEnabled()) {
            log.error(exceptionMsg);
        }

        return ResponseMessage.error(msg);
    }

    /**
     * 处理移动端上报日志异常
     * 异常是JsonMappingException extends JsonProcessingException
     * 但 springmvc里抛出了HttpMessageNotReadableException
     * 在统一异常处理的地方添加对应处理逻辑，就可以友好返回给前端
     *
     * @param ex exception
     * @return 异常信息
     */
    @ExceptionHandler({HttpMessageNotReadableException.class, JsonMappingException.class,
            HttpMediaTypeNotSupportedException.class})
    @ResponseBody
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ResponseMessage<String> exceptionHandler(Exception ex) {
        try {
            if (ex instanceof HttpMessageNotReadableException
                    && ex.getMessage().contains("JSON parse error:")) {
                String message = ex.getMessage();
                int beginIndex = message.indexOf("XXXDto[\"");
                int endIndex = message.indexOf("\"])", beginIndex);
                message = "参数" + message.substring(beginIndex + 22, endIndex) + " 格式错误";
                log.error("exceptionHandler: {}", message);
                return ResponseMessage.error(400, message);
            }
            return ResponseMessage.error(400, ex.getMessage());
        } catch (Exception e) {
            log.error("HttpMessageNotReadableException/JsonMappingException/HttpMediaTypeNotSupportedException handler error", e);
            return ResponseMessage.error(400, ex.getMessage());
        }
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseBody
    public ResponseMessage validExceptionHandler(MethodArgumentNotValidException e) {
        return getResponseMessage(e.getBindingResult());
    }

    @ExceptionHandler(BindException.class)
    @ResponseBody
    public ResponseMessage validExceptionHandler(BindException e) {
        return getResponseMessage(e.getBindingResult());
    }

    /*
     * 捕获@Valid异常
     * 由于都是参数校验 并没有发送邮件和打印错误, 可以在返回的Body中看到错误
     * */
    private ResponseMessage getResponseMessage(BindingResult bindingResult) {
        // ArrayList<String> body = null;
        String body = null;
        if (bindingResult != null && bindingResult.getAllErrors() != null && !bindingResult.getAllErrors().isEmpty()) {
            /*
            body = new ArrayList<>(bindingResult.getFieldErrors().size());
            for (FieldError error : bindingResult.getFieldErrors()) {
                body.add(error.getDefaultMessage());
            }
            */

            body = bindingResult.getAllErrors().stream()
                    .map(DefaultMessageSourceResolvable::getDefaultMessage)
                    .collect(Collectors.joining(";"));
        }

        return ResponseMessage.status(HttpStatus.BAD_REQUEST.value(), "参数错误", body);
    }

    public String getTrace(Throwable t) {
        StringWriter stringWriter = new StringWriter();
        PrintWriter writer = new PrintWriter(stringWriter);
        t.printStackTrace(writer);
        StringBuffer buffer = stringWriter.getBuffer();
        return buffer.toString();
    }
}