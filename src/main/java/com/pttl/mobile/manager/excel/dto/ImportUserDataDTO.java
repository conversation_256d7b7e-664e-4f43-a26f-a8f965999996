package com.pttl.mobile.manager.excel.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 导入用户数据dto
 *
 * <AUTHOR>
 * @date 2022/10/19
 **/
@Data
public class ImportUserDataDTO implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 部门ID
     */
    @ApiModelProperty(value = "部门ID(必填)")
    @ExcelProperty(value = "部门ID(必填)", index = 0)
    private Long id;

    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称")
    @ExcelProperty(value = "部门名称", index = 1)
    private String name;

    /**
     * 用户名称(必填)
     */
    @ApiModelProperty(value = "用户名称(必填)")
    @ExcelProperty(value = "用户名称(必填)", index = 2)
    private String userName;

    /**
     * 用户邮箱(必填)
     */
    @ApiModelProperty(value = "用户邮箱(必填)")
    @ExcelProperty(value = "用户邮箱(必填)", index = 3)
    private String userEmail;

    /**
     * 用户电话号码(必填)
     */
    @ApiModelProperty(value = "用户电话号码(必填)")
    @ExcelProperty(value = "用户电话号码(必填)", index = 4)
    private String userMobile;

    /**
     * 岗位名称
     */
    @ApiModelProperty(value = "岗位名称")
    @ExcelProperty(value = "岗位名称", index = 5)
    private String positionName;

    /**
     * 登录账号(必填)
     */
    @ApiModelProperty(value = "登录账号(必填)")
    @ExcelProperty(value = "登录账号(必填)", index = 6)
    private String loginName;
}
