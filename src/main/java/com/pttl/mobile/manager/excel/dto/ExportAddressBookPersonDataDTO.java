package com.pttl.mobile.manager.excel.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 导出通讯录 人员数据dto
 *
 * <AUTHOR>
 * @date 2023/3/19
 **/
@Data
public class ExportAddressBookPersonDataDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 用户姓名
     */
    @ApiModelProperty(value = "用户姓名")
    @ExcelProperty(value = "姓名")
    private String name;

    /**
     * 员工id
     */
    @ApiModelProperty(value = "员工id")
    @ExcelProperty(value = "员工id")
    private String employeeId;

    /**
     * 用户所属机构
     */
    @ApiModelProperty(value = "用户所属机构")
    @ExcelProperty(value = "隶属机构")
    private String companyDescription;

    /**
     * 所属部门
     */
    @ApiModelProperty(value = "所属部门")
    @ExcelProperty(value = "所属部门")
    private String deptAbbreviation;

    /**
     * 用户岗位
     */
    @ApiModelProperty(value = "用户岗位")
    @ExcelProperty(value = "岗位")
    private String personnelDept;

    /**
     * 座机号码
     */
    @ApiModelProperty(value = "座机号码")
    @ExcelProperty(value = "座机")
    private String phone1;

    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话")
    @ExcelProperty(value = "电话")
    private String phone;

    /**
     * 邮箱地址
     */
    @ApiModelProperty(value = "邮箱地址")
    @ExcelProperty(value = "邮箱")
    private String emailAddress;

    @ApiModelProperty(value = "详细地址")
    @ExcelProperty(value = "地址")
    private String address;

    /**
     * 人员自定义备注
     */
    @ApiModelProperty(value = "人员备注")
    @ExcelProperty(value = "备注")
    private String userRemark;
}
