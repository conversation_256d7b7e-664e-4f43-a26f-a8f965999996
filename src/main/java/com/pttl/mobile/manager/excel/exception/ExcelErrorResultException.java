package com.pttl.mobile.manager.excel.exception;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 自定义导入时异常信息 异常类
 *
 * <AUTHOR>
 * @date 2022/10/19
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class ExcelErrorResultException extends RuntimeException {
    private static final long serialVersionUID = -3820817355610993434L;


    private final String msg;

    private final List<String> object;


    public ExcelErrorResultException(String msg, List<String> object) {
        this.msg = msg;
        this.object = object;
    }
}
