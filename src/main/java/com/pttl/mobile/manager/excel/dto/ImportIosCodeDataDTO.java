package com.pttl.mobile.manager.excel.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 导入ios code 数据dto
 *
 * <AUTHOR>
 * @date 2022/12/22
 **/
@Data
public class ImportIosCodeDataDTO implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 部门ID
     */
    @ApiModelProperty(value = "代码(必填)")
    @ExcelProperty(value = "代码(必填)", index = 0)
    private String code;

    /**
     * 部门名称
     */
    @ApiModelProperty(value = "code link(必填)")
    @ExcelProperty(value = "code link(必填)", index = 1)
    private String codeLink;

    /**
     * app类型: 配置在项目配置文件中
     */
    @ApiModelProperty(value = "app type应用类型(必填)")
    @ExcelProperty(value = "应用类型(必填)", index = 2)
    private String appTypeName;

    @ApiModelProperty(value = "app type应用类型代码(必填)")
    @ExcelIgnore
    private Integer appType;

}
