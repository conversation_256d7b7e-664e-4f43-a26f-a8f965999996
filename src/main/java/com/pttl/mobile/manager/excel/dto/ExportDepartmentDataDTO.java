package com.pttl.mobile.manager.excel.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 导出部门数据dto
 *
 * <AUTHOR>
 * @date 2022/10/19
 **/
@Data
public class ExportDepartmentDataDTO implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 部门ID
     */
    @ApiModelProperty(value = "部门ID")
    @ExcelProperty(value = "部门ID")
    private Long id;

    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称")
    @ExcelProperty(value = "部门名称")
    private String name;

    /**
     * 部门全路径(记录当前目录的所有父级ID)
     */
    @ApiModelProperty(value = "部门全路径(记录当前目录的所有父级ID)")
    @ExcelProperty(value = "部门全路径")
    private String fullDepartmentName;
}
