package com.pttl.mobile.manager.excel.listener;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.exception.ExcelAnalysisException;
import com.alibaba.excel.exception.ExcelDataConvertException;
import com.pttl.mobile.manager.domain.dto.UserDTO;
import com.pttl.mobile.manager.domain.entity.Department2DO;
import com.pttl.mobile.manager.excel.dto.ImportUserDataDTO;
import com.pttl.mobile.manager.excel.exception.ExcelErrorResultException;
import com.pttl.mobile.manager.service.Department2Service;
import com.pttl.mobile.manager.service.UserService;

import java.text.MessageFormat;
import java.util.*;

/**
 * 用户数据导入监听器
 *
 * <AUTHOR>
 * @date 2022/10/19
 **/

public class ImportUserDataListener extends AnalysisEventListener<ImportUserDataDTO> {


    /**
     * 记录Excel读取的行数 有表头故从第一行开始
     */
    private int count = 1;

    /**
     * 保存读取成功的Excel数据
     */
    private List<ImportUserDataDTO> importUserList = new ArrayList<>();

    /**
     * 保存读取失败的Excel消息
     */
    private List<String> errorMessage = new ArrayList<>();

    /**
     * 行头
     */
    private Map headMap = new HashMap<>();

    /**
     * 用户服务层
     */
    private UserService userService;

    /**
     * 部门服务 层
     */
    private Department2Service department2Service;


    public ImportUserDataListener() {
    }

    public ImportUserDataListener(UserService userService, Department2Service department2Service) {
        this.userService = userService;
        this.department2Service = department2Service;
    }

    /**
     * 获取表头名
     *
     * @param headMap
     * @param context
     */
    @Override
    public void invokeHeadMap(Map headMap, AnalysisContext context) {
        this.headMap = headMap;
    }

    /**
     * 校验数据
     *
     * @param userDataDTO
     */
    public void validate(ImportUserDataDTO userDataDTO) {
        this.count = this.count + 1;

        if (Objects.isNull(userDataDTO)) {
            throw new ExcelAnalysisException("数据为空");
        }

        if (Objects.isNull(userDataDTO.getId())) {
            throw new ExcelAnalysisException("部门ID为空");
        }

        if (Objects.isNull(userDataDTO.getUserName())) {
            throw new ExcelAnalysisException("用户名称为空");
        }

        if (Objects.isNull(userDataDTO.getUserEmail())) {
            throw new ExcelAnalysisException("用户邮箱为空");
        }

        if (Objects.isNull(userDataDTO.getUserMobile())) {
            throw new ExcelAnalysisException("用户电话为空");
        }

        if (Objects.isNull(userDataDTO.getLoginName())) {
            throw new ExcelAnalysisException("登录名称为空");
        }

        // 检查部门是否存在 不存在则抛异常
        Department2DO department2DO = department2Service.selectByPrimaryKey(userDataDTO.getId());
        if (Objects.isNull(department2DO)) {
            throw new ExcelAnalysisException("部门不存在");
        }

        // 检查登录名称是否已存在
        UserDTO userDTO = userService.selectByLoginName(userDataDTO.getLoginName());
        if (Objects.nonNull(userDTO)) {
            throw new ExcelAnalysisException("登录名称已存在");
        }
    }

    @Override
    public void onException(Exception exception, AnalysisContext context) {
        String mes;
        if (exception instanceof ExcelDataConvertException) {
            //转换格式发生的异常,不会进入invoke解析,这里要++ 把解析失败数据也加上
            this.count = this.count + 1;

            ExcelDataConvertException excelDataConvertException = (ExcelDataConvertException) exception;
            // 获取列名
            String columnName = (String) this.headMap.get(excelDataConvertException.getColumnIndex());
            // 获取行
            int row = excelDataConvertException.getRowIndex();
            // 获取出错的单元块
            String cellData = excelDataConvertException.getCellData().toString();

            mes = MessageFormat.format("第{0}行发生错误,{1}列,数据为: {2}", (row + 1), columnName, cellData);
        } else {
            // 自定义抛出的异常
            mes = MessageFormat.format("第{0}行发生错误,原因为: {1},", this.count, exception.getMessage());
        }

        // 记录异常信息
        errorMessage.add(mes);
    }

    @Override
    public void invoke(ImportUserDataDTO importUserDataDTO, AnalysisContext analysisContext) {
        // 校验数据
        validate(importUserDataDTO);

        // 校验通过 放入待入库集合
        importUserList.add(importUserDataDTO);
    }


    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        // 校验通过的数据先保存
        if (CollUtil.isNotEmpty(importUserList)) {
            userService.saveImportUserList(importUserList);
            importUserList.clear();
        }

        StringBuilder sb = new StringBuilder();
        String countMessage = MessageFormat.format(
                "一共{0}条数据，成功导入{1}条，重复数据不会插入.",
                this.count - 1, this.count - this.errorMessage.size() - 1);
        sb.append(countMessage);
        errorMessage.add(sb.toString());
        //将错误信息返回
        throw new ExcelErrorResultException("导入数据异常", errorMessage);
    }

}
