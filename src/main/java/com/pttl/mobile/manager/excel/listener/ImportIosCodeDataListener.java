package com.pttl.mobile.manager.excel.listener;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.exception.ExcelAnalysisException;
import com.alibaba.excel.exception.ExcelDataConvertException;
import com.pttl.mobile.manager.excel.dto.ImportIosCodeDataDTO;
import com.pttl.mobile.manager.excel.exception.ExcelErrorResultException;
import com.pttl.mobile.manager.service.IosCodeService;

import java.text.MessageFormat;
import java.util.*;

/**
 * ios code数据导入监听器
 *
 * <AUTHOR>
 * @date 2022/12/21
 **/

public class ImportIosCodeDataListener extends AnalysisEventListener<ImportIosCodeDataDTO> {

    /**
     * 读取五百条数据，批量插入一次。
     */
    private static final int BATCH_COUNT = 1000;

    /**
     * 记录Excel读取的行数 有表头故从第一行开始
     */
    private int count = 1;

    /**
     * 保存读取成功的Excel数据
     */
    private List<ImportIosCodeDataDTO> importIosCodeList = new ArrayList<>();

    /**
     * 保存读取失败的Excel消息
     */
    private List<String> errorMessage = new ArrayList<>();

    /**
     * 行头
     */
    private Map headMap = new HashMap<>();

    /**
     * ios code服务层
     */
    private IosCodeService iosCodeService;

    /**
     * appType类型
     */
    private Map<String, Integer> iosCodeType;


    public ImportIosCodeDataListener() {
    }

    public ImportIosCodeDataListener(IosCodeService iosCodeService, Map<String, Integer> iosCodeType) {
        this.iosCodeService = iosCodeService;
        this.iosCodeType = iosCodeType;
    }

    /**
     * 获取表头名
     *
     * @param headMap
     * @param context
     */
    @Override
    public void invokeHeadMap(Map headMap, AnalysisContext context) {
        this.headMap = headMap;
    }

    /**
     * 校验数据
     *
     * @param importIosCodeDataDTO 数据
     */
    public void validate(ImportIosCodeDataDTO importIosCodeDataDTO) {
        this.count = this.count + 1;

        if (Objects.isNull(importIosCodeDataDTO)) {
            throw new ExcelAnalysisException("数据为空");
        }

        if (Objects.isNull(importIosCodeDataDTO.getCode())) {
            throw new ExcelAnalysisException("代码为空");
        }

        if (Objects.isNull(importIosCodeDataDTO.getCodeLink())) {
            throw new ExcelAnalysisException("链接为空");
        }

        if (Objects.isNull(importIosCodeDataDTO.getAppTypeName()) || !iosCodeType.containsKey(importIosCodeDataDTO.getAppTypeName())) {
            throw new ExcelAnalysisException("应用类型为空或找不到");
        } else {
            importIosCodeDataDTO.setAppType(iosCodeType.get(importIosCodeDataDTO.getAppTypeName()));
        }
    }

    @Override
    public void onException(Exception exception, AnalysisContext context) {
        String mes;
        if (exception instanceof ExcelDataConvertException) {
            // 转换格式发生的异常,不会进入invoke解析,这里要++ 把解析失败数据也加上
            this.count = this.count + 1;

            ExcelDataConvertException excelDataConvertException = (ExcelDataConvertException) exception;
            // 获取列名
            String columnName = (String) this.headMap.get(excelDataConvertException.getColumnIndex());
            // 获取行
            int row = excelDataConvertException.getRowIndex();

            mes = MessageFormat.format("第{0}行发生错误,{1}列,数据为: {2}", (row + 1), columnName,
                    // 获取出错的单元块
                    excelDataConvertException.getCellData());
        } else {
            // 自定义抛出的异常
            mes = MessageFormat.format("第{0}行发生错误,原因为: {1},", this.count, exception.getMessage());
        }

        // 记录异常信息
        errorMessage.add(mes);
    }

    /**
     * 解析到一条数据,对每条数据进行操作
     *
     * @param importIosCodeDataDTO 数据
     * @param analysisContext      分析上下文
     */
    @Override
    public void invoke(ImportIosCodeDataDTO importIosCodeDataDTO, AnalysisContext analysisContext) {
        // 校验数据
        validate(importIosCodeDataDTO);

        // 校验通过 放入待入库集合
        importIosCodeList.add(importIosCodeDataDTO);

        // 分批插入
        if (importIosCodeList.size() >= BATCH_COUNT) {
            iosCodeService.saveImportList(importIosCodeList);
            importIosCodeList.clear();
        }
    }


    /**
     * 全部解析完成后调用
     *
     * @param analysisContext 分析上下文
     */
    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        // 校验通过的数据先保存
        if (CollUtil.isNotEmpty(importIosCodeList)) {
            iosCodeService.saveImportList(importIosCodeList);
            importIosCodeList.clear();
        }

        StringBuilder sb = new StringBuilder();
        String countMessage = MessageFormat.format(
                "共计{0}条数据，成功导入{1}条",
                this.count - 1, this.count - this.errorMessage.size() - 1);
        sb.append(countMessage);
        errorMessage.add(sb.toString());
        //将错误信息返回
        throw new ExcelErrorResultException("导入数据异常", errorMessage);
    }

}
