package com.pttl.mobile.manager.excel.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 导出前端日志数据dto
 *
 * <AUTHOR>
 * @date 2023/3/19
 **/
@Data
public class ExportFrontLogsDataDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 系统名称
     */
    @ExcelProperty(value = "系统名称")
    private String serverName;

    /**
     * 系统用户名
     */
    @ExcelProperty(value = "系统用户名")
    private String userName;

    /**
     * 日志开始时间
     */
    @ExcelProperty(value = "日志开始时间")
    private String logDate;

    /**
     * 状态
     */
    @ExcelProperty(value = "状态")
    private String status;

    /**
     * 版本
     */
    @ExcelProperty(value = "版本")
    private String appVersion;

    /**
     * 设备信息
     */
    @ExcelProperty(value = "设备信息")
    private String deviceInfo;

    /**
     * ihrName
     */
    @ExcelProperty(value = "工号")
    private String ihrName;

    /**
     * 系统类型
     */
    @ExcelProperty(value = "系统类型")
    private String systemType;

    /**
     * 版本
     */
    @ExcelProperty(value = "版本")
    private String systemVersion;

    /**
     * nwePageLoad
     */
    @ExcelProperty(value = "pageLoad")
    private String nwePageLoad;

    /**
     * logDetail
     */
    @ExcelProperty(value = "日志详情")
    private String logDetail;

    @ExcelProperty(value = "模块名称")
    private String moduleName;

    @ExcelProperty(value = "页面加载耗时/秒")
    private String pageLoadTime;

    @ExcelProperty(value = "接口数量")
    private Integer apisCount;

    @ExcelProperty(value = "接口调用耗时/秒")
    private Double apisTimes;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    private String remark;
}
