package com.pttl.mobile.manager.excel.util;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.fastjson.JSON;
import com.pttl.mobile.manager.lib.ResponseMessage;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.Collection;

/**
 * Excel文件导出工具
 *
 * <AUTHOR>
 * @date 2023/3/16
 **/

public class ExportExcelFileUtil {

    /**
     * 文件导出通用工具
     *
     * @param excelFileName    数据导出为Excel文件名称
     * @param sheetName        sheet名称
     * @param dataList         待导出数据
     * @param destinationClass 目标类
     * @param response         response
     * @param <D>              d
     * @throws IOException io异常
     */
    public static <D> void dataExport(String excelFileName, String sheetName,
                                      Collection<D> dataList, Class<D> destinationClass, HttpServletResponse response) throws IOException {
        try {
            // 这里注意 有同学反应使用swagger 会导致各种问题，请直接用浏览器或者用postman
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            // 这里URLEncoder.encode可以防止中文乱码 当然和easyexcel没有关系
            String fileName = URLEncoder.encode(excelFileName, "UTF-8").replace("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            EasyExcelFactory.write(response.getOutputStream(), destinationClass).registerWriteHandler(new AutoHeadColumnWidthStyleStrategy())
                    .sheet(sheetName).doWrite(dataList);
        } catch (Exception e) {
            // 重置response
            response.reset();
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            ResponseMessage<Object> responseMessage = ResponseMessage.error("下载失败");
            response.getWriter().println(JSON.toJSONString(responseMessage));
        }
    }
}
