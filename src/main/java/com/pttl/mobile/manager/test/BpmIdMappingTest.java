package com.pttl.mobile.manager.test;

import com.alibaba.excel.EasyExcelFactory;
import com.pttl.mobile.manager.excel.dto.ImportBpmIdMappingDataDTO;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

/**
 * BpmIdMapping功能测试
 *
 * <AUTHOR>
 * @date 2025/6/17
 */
public class BpmIdMappingTest {

    public static void main(String[] args) {
        // 测试生成Excel模板
        testGenerateTemplate();
    }

    /**
     * 测试生成Excel模板
     */
    public static void testGenerateTemplate() {
        try {
            String templatePath = "bpmIdMappingTemplate.xlsx";
            
            // 创建空的模板数据（只有表头）
            List<ImportBpmIdMappingDataDTO> templateData = new ArrayList<>();
            
            // 生成Excel模板文件
            EasyExcelFactory.write(templatePath, ImportBpmIdMappingDataDTO.class)
                    .sheet("BPM映射导入模板")
                    .doWrite(templateData);
            
            System.out.println("BPM映射导入模板已生成：" + templatePath);
            
            // 检查文件是否存在
            File file = new File(templatePath);
            if (file.exists()) {
                System.out.println("文件大小：" + file.length() + " 字节");
            }
            
        } catch (Exception e) {
            System.err.println("生成模板失败：" + e.getMessage());
            e.printStackTrace();
        }
    }
}
