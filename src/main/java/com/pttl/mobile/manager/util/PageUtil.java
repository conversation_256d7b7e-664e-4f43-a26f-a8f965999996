package com.pttl.mobile.manager.util;

import cn.hutool.core.collection.CollUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * 分页对象转化
 *
 * <AUTHOR>
 * @date 2022/10/20
 **/

public class PageUtil {

    private PageUtil() {
    }

    /**
     * 将PageInfo对象泛型中的Po对象转化为Vo对象
     *
     * @param pageInfo 分页信息
     * @param dClass   目标类
     * @param <P>      Po类型
     * @param <V>      Vo类型
     * @return 分页对象
     */
    public static <P, V> PageInfo<V> page2PageVo(PageInfo<P> pageInfo, Class<V> dClass) {
        Page<V> page = new Page<>(pageInfo.getPageNum(), pageInfo.getPageSize());
        page.setTotal(pageInfo.getTotal());

        // 判空
        if (CollUtil.isEmpty(pageInfo.getList())) {
            return new PageInfo<>(page);
        }

        List<V> mapList = BeanMapper.mapList(pageInfo.getList(), dClass);
        page.addAll(mapList);
        return new PageInfo<>(page);
    }

    /**
     * 将PageInfo对象泛型中的Po对象转化为Vo对象
     *
     * @param pageInfo
     * @param dataList
     * @param <P>
     * @param <V>
     * @return
     */
    public static <P, V> PageInfo<V> page2PageVo(PageInfo<P> pageInfo, List<V> dataList) {
        Page<V> page = new Page<>(pageInfo.getPageNum(), pageInfo.getPageSize());
        page.setTotal(pageInfo.getTotal());

        // 判空
        if (CollUtil.isEmpty(pageInfo.getList())) {
            return new PageInfo<>(page);
        }

        page.addAll(dataList);
        return new PageInfo<>(page);
    }
}

