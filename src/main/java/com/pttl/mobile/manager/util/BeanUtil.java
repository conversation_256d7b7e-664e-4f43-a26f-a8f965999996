package com.pttl.mobile.manager.util;

import com.alibaba.fastjson.JSON;
import org.apache.commons.lang.StringUtils;
import org.springframework.cglib.beans.BeanCopier;
import org.springframework.cglib.beans.BeanMap;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

public class BeanUtil {

	public static void copy(Object src, Object dest) {
		BeanCopier.create(src.getClass(), dest.getClass(), false).copy(src, dest, null);
	}

	/**
	 * 将对象装换为Map<String, Object>
	 *
	 * @param bean
	 * @return
	 */
	public static <T> Map<String, Object> beanToMap(T bean) {
		Map<String, Object> map = new HashMap<>();
		if (bean != null) {
			BeanMap beanMap = BeanMap.create(bean);
			for (Object key : beanMap.keySet()) {
				map.put(key + "", beanMap.get(key));
			}
		}
		return map;
	}

	/**
	 * 将对象装换为Map<String, String>
	 *
	 * @param bean
	 * @param pSkipNull
	 * @return
	 */
	public static <T> Map<String, String> beanToStringMap(T bean, boolean pSkipNull) {
		Map<String, String> map = new HashMap<>();
		if (bean == null) {
			return map;
		}
		BeanMap beanMap = BeanMap.create(bean);
		for (Object key : beanMap.keySet()) {
			Object value = beanMap.get(key);
			if (pSkipNull && value == null) {
				continue;
			}
			String strValue = value != null ? value.toString() : null;
			if (value instanceof Date) {
				strValue = DateUtil.dateToStr((Date) value);
			}
			map.put(key + "", strValue);
		}
		return map;
	}

	/**
	 * 将map装换为javabean对象
	 *
	 * @param map
	 * @param bean
	 * @return
	 */
	public static <T> T mapToBean(Map<String, Object> map, T bean) {
		BeanMap beanMap = BeanMap.create(bean);
		for (Map.Entry<String, Object> item : map.entrySet()) {
			// 解决Date与Long互转问题
			if (beanMap.getPropertyType(item.getKey()) == Date.class && item.getValue() instanceof Long) {
				item.setValue(new Date((Long) item.getValue()));
			}
			// 解决BigDecimal与Double互转问题
			if (beanMap.getPropertyType(item.getKey()) == BigDecimal.class && item.getValue() instanceof Double) {
				item.setValue(new BigDecimal((Double) item.getValue()));
			}
		}
		beanMap.putAll(map);
		return bean;
	}

	/**
	 * 将 String 转换为对象
	 *
	 * @param pString
	 * @param pClass
	 * @param <T>
	 * @return
	 */
	public static <T> T stringToBean(String pString, Class<T> pClass) {
		if (StringUtils.isBlank(pString) || pClass == null) {
			return null;
		}
		T result;
		switch (pClass.getSimpleName()) {
			case "List":
			case "ArrayList":
				try {
					result = JSON.parseObject(pString, pClass);
				} catch (Exception pE) {
					result = (T) CollectionUtils.arrayToList(pString.split(","));
				}
				break;
			default:
				result = JSON.parseObject(pString, pClass);
		}
		return result;
	}

	/**
	 * 将 String 转换为复杂对象
	 *
	 * @param pString
	 * @param pClass
	 * @param pContentClass
	 * @param <T>
	 * @return
	 */
	public static <T> T stringToComplexBean(String pString, Class<T> pClass, Class pContentClass) {
		if (StringUtils.isBlank(pString) || pClass == null) {
			return null;
		}
		T result;
		switch (pClass.getSimpleName()) {
			case "List":
			case "ArrayList":
				try {
					result = (T) JSON.parseArray(pString, pContentClass);
				} catch (Exception pE) {
					result = (T) CollectionUtils.arrayToList(pString.split(","));
				}
				break;
			default:
				result = JSON.parseObject(pString, pClass);
		}
		return result;
	}

	/**
	 * 将Map<String,Object>转换为Map<String,String>
	 * 
	 * @param pParams
	 * @return
	 */
	public static Map<String, String> coverObjMap(Map<String, Object> pParams) {
		Map<String, String> map = new HashMap<>();
		if (CollectionUtils.isEmpty(pParams)) {
			return map;
		}
		for (Map.Entry<String, Object> item : pParams.entrySet()) {
			String value = item.getValue() != null ? item.getValue().toString() : null;
			if (item.getValue() instanceof BigDecimal) {
				value = new Double(((BigDecimal) item.getValue()).doubleValue()).toString();
			}
			map.put(item.getKey(), value);
		}
		return map;
	}

	/**
	 * 将Map<String,String>转换为Map<String,Object>
	 *
	 * @param pParams
	 * @return
	 */
	public static Map<String, Object> coverStrMap(Map<String, String> pParams) {
		Map<String, Object> map = new HashMap<>();
		if (CollectionUtils.isEmpty(pParams)) {
			return map;
		}
		for (Map.Entry<String, String> item : pParams.entrySet()) {
			map.put(item.getKey(), item.getValue());
		}
		return map;
	}
}
