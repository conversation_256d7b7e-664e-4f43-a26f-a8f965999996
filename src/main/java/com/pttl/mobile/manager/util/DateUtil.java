package com.pttl.mobile.manager.util;

import java.text.ParseException;
import java.text.ParsePosition;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

public class DateUtil {

	public static SimpleDateFormat DEFAULT_FORMATTER = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    private final static SimpleDateFormat sdfDay = new SimpleDateFormat("yyyy-MM-dd");

    private final static SimpleDateFormat ym = new SimpleDateFormat("yyyy-MM");

    public static void main(String[] args) {
        int month = getCurrentMonth();

        System.out.println(month);
        String weekOfDate = DateUtil.getWeekNumByDate(new Date());
        System.out.println(weekOfDate);
        int passDayOfMonth = getPassDayOfMonth(new Date());
        System.out.println(passDayOfMonth);
    }

	public static int getCurrentYear() {
		Calendar now = Calendar.getInstance();
		return now.get(Calendar.YEAR);
	}

	public static String getCurrentYearStr(Date date) {
		Calendar now = Calendar.getInstance();
		now.setTime(date);
		int year = now.get(Calendar.YEAR);
		return Integer.toString(year);
	}

	public static String getCurrentYearStr() {
		Calendar now = Calendar.getInstance();
		int year = now.get(Calendar.YEAR);
		return Integer.toString(year);
	}

	public static int getCurrentMonth(Date date) {
		Calendar now = Calendar.getInstance();
		now.setTime(date);
		return now.get(Calendar.MONTH) + 1;
	}

	public static int getCurrentMonth() {
		Calendar now = Calendar.getInstance();
		return now.get(Calendar.MONTH) + 1;
	}

	public static String getCurrentMonthStr(Date date) {

		int month = getCurrentMonth(date);
		if (month >= 10) {
			return Integer.toString(month);
		}
		return "0" + month;
	}

	public static String getCurrentMonthStr() {
		int month = getCurrentMonth();
		if (month >= 10) {
			return Integer.toString(month);
		}
		return "0" + month;
	}

	public static String getLastYearMonth(String month) {
		String[] arr = month.split("-");
		int year = Integer.valueOf(arr[0]);
		int monthInt = Integer.valueOf(arr[1]);

		if (monthInt == 12) {
			monthInt = 1;
		} else {
			monthInt = monthInt + 1;
			year = year - 1;
		}
		return year + "-" + (monthInt >= 10 ? monthInt : "0" + monthInt);
	}

	public static Date[] getMonthRange(int year, int month) {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		try {
			String startDateStr = year + "-" + (month >= 10 ? month : ("0" + month)) + "-01 00:00:01";
			Date startDate = sdf.parse(startDateStr);
			Date endDate = getLastDayOfMonth(year, month);
			return new Date[] { startDate, endDate };
		} catch (ParseException e) {
			return null;
		}
	}

	public static int getCurrentDay(Date date) {
		Calendar now = Calendar.getInstance();
		now.setTime(date);
		return now.get(Calendar.DAY_OF_MONTH);
	}

	public static int getCurrentDay() {
		Calendar now = Calendar.getInstance();
		return now.get(Calendar.DAY_OF_MONTH);
	}

	public static String getCurrentDayStr(Date date) {
		int day = getCurrentDay(date);
		if (day >= 10) {
			return Integer.toString(day);
		}
		return "0" + day;
	}

	public static String getCurrentDayStr() {
		int day = getCurrentDay();
		if (day >= 10) {
			return Integer.toString(day);
		}
		return "0" + day;
	}

	public static Date getNextDayDate(Date date) throws Exception {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
		String nowDate = sdf.format(date);
		Calendar cal = Calendar.getInstance();
		cal.setTime(sdf.parse(nowDate));
		cal.add(Calendar.DAY_OF_YEAR, +1);
		return cal.getTime();
	}

	public static Date getNextDate(Date date) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		int day = calendar.get(Calendar.DATE);
		calendar.set(Calendar.DATE, day + 1);
		return getSqlDate(calendar.getTime());
	}

	public static java.sql.Date getSqlDate(Date date) {
		return new java.sql.Date(date.getTime());
	}

	public static Date getLastDayOfMonth(int year, int month) {
		Calendar cal = Calendar.getInstance();
		// 设置年份
		cal.set(Calendar.YEAR, year);
		// 设置月份
		cal.set(Calendar.MONTH, month - 1);
		// 获取某月最大天数
		int lastDay = cal.getActualMaximum(Calendar.DAY_OF_MONTH);
		// 设置日历中月份的最大天数
		cal.set(Calendar.DAY_OF_MONTH, lastDay);
		// 格式化日期
		return cal.getTime();
	}

	/**
	 * 根据时间类型比较时间大小
	 *
	 * @param source
	 * @param target
	 * @param type
	 *            "YYYY-MM-DD" "yyyyMMdd HH:mm:ss" 类型可自定义 传递时间的对比格式
	 * @return 0 ：source和target时间相同 1 ：source比target时间大 -1：source比target时间小
	 * @throws Exception
	 */
	public static int dateCompare(String source, String target, String type) throws Exception {
		int ret = 2;
		SimpleDateFormat sdf = new SimpleDateFormat(type);
		Date sourcedate = sdf.parse(source);
		Date targetDate = sdf.parse(target);
		ret = sourcedate.compareTo(targetDate);
		return ret;
	}

	/**
	 * 判断两个日期是否是同一天
	 *
	 * @param date1
	 *            date1
	 * @param date2
	 *            date2
	 * @return
	 */
	public static boolean isSameDay(Date date1, Date date2) {
		if (date1 == null || date2 == null) {
			return false;
		}

		Calendar cal1 = Calendar.getInstance();
		cal1.setTime(date1);

		Calendar cal2 = Calendar.getInstance();
		cal2.setTime(date2);

		boolean isSameYear = cal1.get(Calendar.YEAR) == cal2.get(Calendar.YEAR);
		boolean isSameMonth = isSameYear && cal1.get(Calendar.MONTH) == cal2.get(Calendar.MONTH);
		boolean isSameDate = isSameMonth && cal1.get(Calendar.DAY_OF_MONTH) == cal2.get(Calendar.DAY_OF_MONTH);

		return isSameDate;
	}

	/**
	 * 将短时间格式字符串转换为时间 yyyy-MM-dd
	 *
	 * @param strDate
	 * @return
	 */
	public static Date strToDate(String strDate, String format) {
		SimpleDateFormat formatter = new SimpleDateFormat(format);
		ParsePosition pos = new ParsePosition(0);
		Date strtodate = formatter.parse(strDate, pos);
		return strtodate;
	}

	/**
	 * 将短时间格式时间转换为字符串 yyyy-MM-dd
	 *
	 * @param dateDate
	 * @param format
	 * @return
	 */
	public static String dateToStr(Date dateDate, String format) {
		SimpleDateFormat formatter = new SimpleDateFormat(format);
		String dateString = formatter.format(dateDate);
		return dateString;
	}

	public static Date minusDays(Date d1, int days) {
		Calendar c = Calendar.getInstance();
		c.setTime(d1);
		c.add(Calendar.DATE, -days);
		Date da = c.getTime();
		return da;
	}

	public static Date addDays(Date d1, int days) {
		Calendar c = Calendar.getInstance();
		c.setTime(d1);
		c.add(Calendar.DATE, days);
		Date da = c.getTime();
		return da;
	}
	public static Date addHours(Date d1, int hours) {
		Calendar c = Calendar.getInstance();
		c.setTime(d1);
		c.add(Calendar.HOUR_OF_DAY, hours);
		Date da = c.getTime();
		return da;
	}

	public static Date addYears(Date d1, int years) {
		Calendar c = Calendar.getInstance();
		c.setTime(d1);
		c.add(Calendar.YEAR, years);
		Date da = c.getTime();
		return da;
	}

	/**
	 * 使用默认格式转换 Date 为 String
	 * 
	 * @param pDate
	 * @return
	 */
	public static String dateToStr(Date pDate) {
		String dateString = DEFAULT_FORMATTER.format(pDate);
		return dateString;
	}

    /**
     * 使用默认格式转换 String 为 Date
     *
     * @param strDate
     * @return
     */
    public static Date strToDate(String strDate) {
        ParsePosition pos = new ParsePosition(0);
        Date date = DEFAULT_FORMATTER.parse(strDate, pos);
        return date;
    }

    /**
     * 通过时间秒毫秒数判断两个时间的间隔
     * @param date1
     * @param date2
     * @return {@link int}
     * @throws
     * <AUTHOR>
     * @date 2020/5/12 13:52
     */
    public static int differentDaysByMillisecond(Date date1, Date date2) {
        int days = (int) ((date2.getTime() - date1.getTime()) / (1000 * 3600 * 24));
        return days;
    }


	/**
	 * 获取 几分钟前 的时间
	 * @param date 参考时间
	 * @param amount 几分钟
     * @return
     */
	public static Date getMinutesAgo(Date date,int amount){
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.add(Calendar.MINUTE,-amount);
		return calendar.getTime();
	}

	/**
	 * 通过时间获取周
	 * @param DateStr
	 * @return
	 */
	public static String getWeekNumByDate(String DateStr){
		try{
			SimpleDateFormat sdfDay = new SimpleDateFormat("yyyy-MM-dd");
			Calendar calendar = Calendar.getInstance();
			calendar.setFirstDayOfWeek(Calendar.MONDAY);
			calendar.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
			calendar.setTime(sdfDay.parse(DateStr));
			int num = calendar.get(Calendar.WEEK_OF_YEAR);
			/** 2017的周别逻辑特殊处理 */
			if (calendar.get(Calendar.YEAR) == 2017) {
				if (num > 1) {
					num -= 1;
				} else {
					num = 52;
				}
			}
			if (num < 10) {
				return "0"+num;
			}else {
				return String.valueOf(num);
			}
		}catch(Exception ex){
			ex.printStackTrace();
		}
		return "0";

	}

	//通过年获取最后一周
	public static String getLastWeekNumByYear(int year){
		try{
			Calendar calendar = Calendar.getInstance();
			calendar.setFirstDayOfWeek(Calendar.MONDAY);
			calendar.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
			calendar.setMinimalDaysInFirstWeek(4);
			calendar.set(Calendar.YEAR, year);
			calendar.roll(Calendar.DAY_OF_YEAR, -1);
			return String.valueOf(calendar.get(Calendar.WEEK_OF_YEAR));
		}catch(Exception ex){
			ex.printStackTrace();
		}
		return "0";
	}

	/**
	 * 通过日期获取周几
	 * @param dt
	 * @return
	 */
	public static int getWeekOfDate(Date dt) {
		int[] weekDays = {7, 1, 2, 3, 4, 5, 6};
		Calendar cal = Calendar.getInstance();
		cal.setTime(dt);
		int w = cal.get(Calendar.DAY_OF_WEEK) - 1;
		if (w < 0)
			w = 0;
		return weekDays[w];
	}

	/**
	 * 获取上个月第一天
	 * @param format
	 * @return
	 */
	public static String getLastMonthFirstDay(String format){
		try{
			Calendar cal = Calendar.getInstance();
			cal.set(Calendar.MONTH, cal.get(Calendar.MONTH)-1);
			cal.set(Calendar.DAY_OF_MONTH, 1);
			SimpleDateFormat sdf = new SimpleDateFormat(format);
			return sdf.format(cal.getTime());
		}catch(Exception ex){
			ex.printStackTrace();
		}
		return "0";
	}

    /**
     * 获取YYYY-MM-DD格式
     *
     * @return
     */
    public static String getDay() {
        return sdfDay.format(new Date());
    }

    /**
     *
     * 获取 几小时前 的时间
     *
     * @param date 参考时间
     * @param amount 几小时
     * @return
     */
    public static Date getHoursAgo(Date date, int amount) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.HOUR, -amount);
        return cal.getTime();
    }

    public static String getWeekNumByDate(Date date){
        try{
            if(null != date){
                Calendar calendar = Calendar.getInstance();
                calendar.setFirstDayOfWeek(Calendar.MONDAY);
                calendar.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
                calendar.setTime(date);
				calendar.setMinimalDaysInFirstWeek(4);
                int num = calendar.get(Calendar.WEEK_OF_YEAR);
                /** 2017的周别逻辑特殊处理 */
                if (calendar.get(Calendar.YEAR) == 2017) {
                    if (num > 1) {
                        num -= 1;
                    } else {
                        num = 52;
                    }
                }
                if (num < 10) {
                    return "0"+num;
                }else {
                    return String.valueOf(num);
                }
            }
        }catch(Exception ex){
            ex.printStackTrace();
        }
        return "0";
    }

    public static int getWeekNumberByDate(Date date){
        try{
            if(null != date){
                Calendar calendar = Calendar.getInstance();
                calendar.setFirstDayOfWeek(Calendar.MONDAY);
                calendar.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
                calendar.setTime(date);
                int num = calendar.get(Calendar.WEEK_OF_YEAR);
                /** 2017的周别逻辑特殊处理 */
                if (calendar.get(Calendar.YEAR) == 2017) {
                    if (num > 1) {
                        num -= 1;
                    } else {
                        num = 52;
                    }
                }
                if (num < 10) {
                    return num;
                }else {
                    return num;
                }
            }
        }catch(Exception ex){
            ex.printStackTrace();
        }
        return 0;
    }

    /**
     *
     * 获取 几个月前 的时间
     *
     * @param date 参考时间
     * @param amount 几个月
     * @return
     */
    public static Date getMonthsAgo(Date date, int amount) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.MONTH, -amount);
        return cal.getTime();
    }

	/**
	 * 获取 年月
	 * @param date
	 * @return
	 */
	public static String getYearMonth(Date date) {
		Calendar now = Calendar.getInstance();
		now.setTime(date);
		int year = now.get(Calendar.YEAR);
		int month= now.get(Calendar.MONTH)+1;
		return year+"年"+month+"月";
	}

	/**
	 * 获取 下个月第一天日期
	 * @param date
	 * @return
	 */
	public static Date getNextMonth(Date date){
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.set(Calendar.DAY_OF_MONTH,1);
		calendar.add(Calendar.MONTH,1);
		System.out.println("--------"+sdfDay.format(calendar.getTime())+"--------");
		return calendar.getTime();
	}

	/**
	 * 时间年月转化
	 * @param date
	 * @return
	 */
	public static Date getYearMonthDate(Date date){
		Date d = null;
		String yearMonth = ym.format(date);
		try {
			d = ym.parse(yearMonth);
		} catch (ParseException e) {
			e.printStackTrace();
		}
		return d;
	}

	/**
	 * 获取当月第一天
	 *
	 * @return Date
	 */
	public static Date getFirstDateOfMonth() {
		Calendar cal = Calendar.getInstance();// 获取当前日期
		cal.set(Calendar.DAY_OF_MONTH, 1);// 设置为1号，当前日期既为本月第一天

		Date firstDate = null;
		try {
			firstDate = sdfDay.parse(sdfDay.format(cal.getTime()));
		} catch (ParseException e) {
			e.printStackTrace();
		}

		return firstDate;
	}


	/**
	 * 获取年月字符串
	 * @param date
	 * @return
	 */
	public static String getYearMonthStr(Date date) {
		String dateString = ym.format(date);
		return dateString;
	}

	public static String getYearMonthDayStr(Date date) {
		String dateString = sdfDay.format(date);
		return dateString;
	}

    /**
     * 根据时间类型比较时间大小
     *
     * @param source
     * @param target
     * @param type
     *            "YYYY-MM-DD" "yyyyMMdd HH:mm:ss" 类型可自定义 传递时间的对比格式
     * @return 0 ：计算连个日期之间相差的天数
     * @throws Exception
     */
    public static Long stringDateCalculation(String source, String target, String type) throws Exception {
        SimpleDateFormat sdf = new SimpleDateFormat(type);
        Date sourcedate = sdf.parse(source);
        Date targetDate = sdf.parse(target);
        Long betweenDays = (sourcedate.getTime() - targetDate.getTime()) / (1000L*3600L*24L);
        return betweenDays;
    }
    /**
     * 根据时间类型比较时间大小
     *
     * @param sourcedate
     * @param targetDate
     * @param type
     *            "YYYY-MM-DD" "yyyyMMdd HH:mm:ss" 类型可自定义 传递时间的对比格式
     * @return 0 ：计算连个日期之间相差的天数
     * @throws Exception
     */
    public static Long dateCalculation(Date sourcedate, Date targetDate, String type) {
        SimpleDateFormat sdf = new SimpleDateFormat(type);
        Long betweenDays = (sourcedate.getTime() - targetDate.getTime()) / (1000L*3600L*24L);
        return betweenDays;
    }
	/**
	* @MethodName: getMonthDiff
	 * @Description: 判断两个时间相差的月份，此处2020-01-05  2020-02-06相差两个月
	 * 2020-01-05  2020-02-04相差0个月  2020-01-05  2020-02-05相差1个月
	 * @Param: [start, end]
	 * @Return: int
	 * @Author: huangxiaoqiang
	 * @Date: 2020/7/15
	**/
	public static int getMonthDiff(Date start, Date end) {
		Calendar c1 = Calendar.getInstance();
		Calendar c2 = Calendar.getInstance();
		c1.setTime(start);
		c2.setTime(end);
		int year1 = c1.get(Calendar.YEAR);
		int year2 = c2.get(Calendar.YEAR);
		int month1 = c1.get(Calendar.MONTH);
		int month2 = c2.get(Calendar.MONTH);
		int day1 = c1.get(Calendar.DAY_OF_MONTH);
		int day2 = c2.get(Calendar.DAY_OF_MONTH);
		int totalMonth1 = year1 * 12 + month1;
		int totalMonth2 = year2 * 12 + month2;
		int diffMonth = totalMonth2 - totalMonth1;
		if (day2 < day1) {
			diffMonth--;
		} else if (day2 > day1) {
			diffMonth++;
		}
		return diffMonth;
	}

	//获取当前年月日时间
	public static Date getCurrentMonthDate(){
		SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
		String format1 = format.format(new Date());
		try {
			return format.parse(format1);
		} catch (ParseException e) {
			e.printStackTrace();
		}
		return new Date();
	}
	/**
	 * 获取某年某月第一天: 例如：2020-09
	 * @param date
	 * @return 2020-09-01
	 */
	public static String getFirstDayOfMonth(String date) {
		String[] split = date.split("-");
		Calendar cal = Calendar.getInstance();
		cal.set(Calendar.YEAR, Integer.valueOf(split[0]));
		cal.set(Calendar.MONTH, Integer.valueOf(split[1])-1);
		cal.set(Calendar.DAY_OF_MONTH,cal.getMinimum(Calendar.DATE));
		return   new   SimpleDateFormat( "yyyy-MM-dd ").format(cal.getTime())+ "00:00:00";
	}

	/**
	 * 获取某年某月下个月的第一天: 例如：2020-09
	 * @param date
	 * @return 2020-10-01
	 */
	public static String getLastDayMonth(String date) {
		String[] split = date.split("-");
		Calendar cal = Calendar.getInstance();
		cal.set(Calendar.YEAR, Integer.valueOf(split[0]));
		cal.set(Calendar.MONTH, Integer.valueOf(split[1])-1);
		cal.set(Calendar.DAY_OF_MONTH,cal.getActualMaximum(Calendar.DATE));
		return  new   SimpleDateFormat( "yyyy-MM-dd ").format(cal.getTime())+"23:59:59";
	}

    /**
     * 取得月天数
     *
     * @param date
     * @return
     */
    public static int getDayOfMonth(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        return c.getActualMaximum(Calendar.DAY_OF_MONTH);
    }
    /**
     * 取得月第一天
     *
     * @param date
     * @return
     */
    public static Date getFirstDateOfMonth(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.set(Calendar.DAY_OF_MONTH, c.getActualMinimum(Calendar.DAY_OF_MONTH));
        return c.getTime();
    }

    /**
     * 取得月最后一天
     *
     * @param date
     * @return
     */
    public static Date getLastDateOfMonth(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        c.set(Calendar.DAY_OF_MONTH, c.getActualMaximum(Calendar.DAY_OF_MONTH));
        return c.getTime();
    }
    /**
     * 取得月已经过的天数
     *
     * @param date
     * @return
     */
    public static int getPassDayOfMonth(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        return c.get(Calendar.DAY_OF_MONTH);
    }

    /**
     * 取得月的剩余天数
     *
     * @param date
     * @return
     */
    public static int getRemainDayOfMonth(Date date) {
        int dayOfMonth = getDayOfMonth(date);
        int day = getPassDayOfMonth(date);
        return dayOfMonth - day;
    }

    /**
     * 取得季度第一天
     *
     * @param date
     * @return
     */
    public static Date getFirstDateOfSeason(Date date) {
        return getFirstDateOfMonth(getSeasonDate(date)[0]);
    }

    /**
     * 取得季度最后一天
     *
     * @param date
     * @return
     */
    public static Date getLastDateOfSeason(Date date) {
        return getLastDateOfMonth(getSeasonDate(date)[2]);
    }

    /**
     * 取得季度天数
     *
     * @param date
     * @return
     */
    public static int getDayOfSeason(Date date) {
        int day = 0;
        Date[] seasonDates = getSeasonDate(date);
        for (Date date2 : seasonDates) {
            day += getDayOfMonth(date2);
        }
        return day;
    }

    /**
     * 取得季度剩余天数
     *
     * @param date
     * @return
     */
    public static int getRemainDayOfSeason(Date date) {
        return getDayOfSeason(date) - getPassDayOfSeason(date);
    }

    /**
     * 取得季度已过天数
     *
     * @param date
     * @return
     */
    public static int getPassDayOfSeason(Date date) {
        int day = 0;

        Date[] seasonDates = getSeasonDate(date);

        Calendar c = Calendar.getInstance();
        c.setTime(date);
        int month = c.get(Calendar.MONTH);

        if (month == Calendar.JANUARY || month == Calendar.APRIL
                || month == Calendar.JULY || month == Calendar.OCTOBER) {// 季度第一个月
            day = getPassDayOfMonth(seasonDates[0]);
        } else if (month == Calendar.FEBRUARY || month == Calendar.MAY
                || month == Calendar.AUGUST || month == Calendar.NOVEMBER) {// 季度第二个月
            day = getDayOfMonth(seasonDates[0])
                    + getPassDayOfMonth(seasonDates[1]);
        } else if (month == Calendar.MARCH || month == Calendar.JUNE
                || month == Calendar.SEPTEMBER || month == Calendar.DECEMBER) {// 季度第三个月
            day = getDayOfMonth(seasonDates[0]) + getDayOfMonth(seasonDates[1])
                    + getPassDayOfMonth(seasonDates[2]);
        }
        return day;
    }

    /**
     * 取得季度月
     *
     * @param date
     * @return
     */
    public static Date[] getSeasonDate(Date date) {
        Date[] season = new Date[3];

        Calendar c = Calendar.getInstance();
        c.setTime(date);

        int nSeason = getSeason(date);
        if (nSeason == 1) {// 第一季度
            c.set(Calendar.MONTH, Calendar.JANUARY);
            season[0] = c.getTime();
            c.set(Calendar.MONTH, Calendar.FEBRUARY);
            season[1] = c.getTime();
            c.set(Calendar.MONTH, Calendar.MARCH);
            season[2] = c.getTime();
        } else if (nSeason == 2) {// 第二季度
            c.set(Calendar.MONTH, Calendar.APRIL);
            season[0] = c.getTime();
            c.set(Calendar.MONTH, Calendar.MAY);
            season[1] = c.getTime();
            c.set(Calendar.MONTH, Calendar.JUNE);
            season[2] = c.getTime();
        } else if (nSeason == 3) {// 第三季度
            c.set(Calendar.MONTH, Calendar.JULY);
            season[0] = c.getTime();
            c.set(Calendar.MONTH, Calendar.AUGUST);
            season[1] = c.getTime();
            c.set(Calendar.MONTH, Calendar.SEPTEMBER);
            season[2] = c.getTime();
        } else if (nSeason == 4) {// 第四季度
            c.set(Calendar.MONTH, Calendar.OCTOBER);
            season[0] = c.getTime();
            c.set(Calendar.MONTH, Calendar.NOVEMBER);
            season[1] = c.getTime();
            c.set(Calendar.MONTH, Calendar.DECEMBER);
            season[2] = c.getTime();
        }
        return season;
    }

    /**
     *
     * 1 第一季度 2 第二季度 3 第三季度 4 第四季度
     *
     * @param date
     * @return
     */
    public static int getSeason(Date date) {

        int season = 0;

        Calendar c = Calendar.getInstance();
        c.setTime(date);
        int month = c.get(Calendar.MONTH);
        switch (month) {
            case Calendar.JANUARY:
            case Calendar.FEBRUARY:
            case Calendar.MARCH:
                season = 1;
                break;
            case Calendar.APRIL:
            case Calendar.MAY:
            case Calendar.JUNE:
                season = 2;
                break;
            case Calendar.JULY:
            case Calendar.AUGUST:
            case Calendar.SEPTEMBER:
                season = 3;
                break;
            case Calendar.OCTOBER:
            case Calendar.NOVEMBER:
            case Calendar.DECEMBER:
                season = 4;
                break;
            default:
                break;
        }
        return season;
    }
}