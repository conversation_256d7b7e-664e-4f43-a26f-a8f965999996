package com.pttl.mobile.manager.util;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SmUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.SM2;
import lombok.extern.slf4j.Slf4j;

/**
 * 国密
 *
 * <AUTHOR>
 */
@Slf4j
public class SM2Utils {

    /**
     * 公钥 私钥
     */
    public static final String PRIVATE_KEY = "76fa412b1ad6605bb0aa8fa661065cec7be162c133b4c6b6463dacf404286c3c";
    public static final String PUBLIC_KEY = "047a5cb98affa45fbd3c352d8994ba353b23c1b94e51146cbc3e2bd7723bd96ac6d1f18dc34bd445d99d3de48ee6c43048e6411e6dcf76393a838bbd85c7a8c156";

    /**
     * 通过传递公钥加密字符串
     *
     * @param sourceStr    需要加密的原始字符串
     * @param publicKeyStr 公钥(BASE64编码)
     * @return 加密之后的字符串
     * @throws Exception
     */
    public static String encryptByPublicKey(String sourceStr, String publicKeyStr) {
        try {
            SM2 sm2 = SmUtil.sm2(null, publicKeyStr);
            return sm2.encryptBcd(sourceStr, KeyType.PublicKey);
        } catch (Exception e) {
            log.error("SM2使用公钥加密失败: {}", e.getMessage());
        }

        return null;
    }

    /**
     * 通过传递私钥解密字符串
     *
     * @param encryptionStr 需要解密的字符串
     * @param privateKeyStr 私钥(BASE64编码)
     * @return 解密之后的明文字符串
     * @throws Exception
     */
    public static String decryptByPrivateKey(String encryptionStr, String privateKeyStr) throws Exception {

        if (StringUtil.isNotEmpty(encryptionStr) && !encryptionStr.startsWith("04")) {
            encryptionStr = "04" + encryptionStr;
        }
        SM2 sm2 = SmUtil.sm2(privateKeyStr, null);
        return StrUtil.utf8Str(sm2.decryptStr(encryptionStr, KeyType.PrivateKey));
    }

    public static void main(String[] args) throws Exception {
        String text = "User@123";
        System.out.println("原文：" + text);

        String publicKeyStr = "047a5cb98affa45fbd3c352d8994ba353b23c1b94e51146cbc3e2bd7723bd96ac6d1f18dc34bd445d99d3de48ee6c43048e6411e6dcf76393a838bbd85c7a8c156";
        String privateKeyStr = "76fa412b1ad6605bb0aa8fa661065cec7be162c133b4c6b6463dacf404286c3c";
        System.out.println("公钥：\n" + publicKeyStr);
        System.out.println("私钥：\n" + privateKeyStr);
        String encryptionStr = encryptByPublicKey(text, publicKeyStr);
        System.out.println("加密后：" + encryptionStr);
        String decryptStr = decryptByPrivateKey(encryptionStr, privateKeyStr);
        System.out.println("解密后：" + decryptStr);
    }
}