package com.pttl.mobile.manager.util;


import javax.servlet.http.HttpServletResponse;
import java.io.*;

/**
 * 文件下载工具
 *
 * <AUTHOR>
 * @date 2022/10/18
 **/

public class FileDownloadUtil {

    /**
     * 文件下载
     *
     * @param path     本地路径
     * @param response response
     */
    public void download(String path, HttpServletResponse response) {
        try (InputStream fis = new BufferedInputStream(new FileInputStream(path))) {
            // path是指欲下载的文件的路径。
            File file = new File(path);
            // 取得文件名。
            String filename = file.getName();

            // 以流的形式下载文件。
            byte[] buffer = new byte[fis.available()];
            int read = fis.read(buffer);
            //判断是不是读到了数据流的末尾 ，防止出现死循环。
            if (read == -1) {
                return;
            }
            // 清空response
            response.reset();
            // 设置response的Header
            response.addHeader("Content-Disposition", "attachment;filename=" + new String(filename.getBytes()));
            response.addHeader("Content-Length", "" + file.length());
            OutputStream toClient = new BufferedOutputStream(response.getOutputStream());
            response.setContentType("application/octet-stream");
            toClient.write(buffer);
            toClient.flush();
            toClient.close();
        } catch (Exception exception) {
            exception.printStackTrace();
        }
    }

}
