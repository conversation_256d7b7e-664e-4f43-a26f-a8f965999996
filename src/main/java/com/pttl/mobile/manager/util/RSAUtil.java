package com.pttl.mobile.manager.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.springframework.stereotype.Component;

import javax.crypto.Cipher;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;

/*
 * RSA加密
 * */
@Slf4j
@Component
public class RSAUtil {
    private final String RSA_ALGORITHM = "RSA";
    private final String PUBLIC_KEY_STRING = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDHqtzxyXB4KGAQbssXaDekERWRZAEO7ZjE10uZ8ptGwj7JdvNX959jtF5B8ixf3uVLpGWZC6mHg2ssARXXP53ZL9qzv0Dq+v9BBcphWNBBvC8myIOaU9BSMXyHZ+0k5xIh/9glIVBB6ITUWyNlDVavjXbr1qbPzSR7liOiuCVKyQIDAQAB";
    private final String PRIVATE_KEY_STRING = "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAMeq3PHJcHgoYBBuyxdoN6QRFZFkAQ7tmMTXS5nym0bCPsl281f3n2O0XkHyLF/e5UukZZkLqYeDaywBFdc/ndkv2rO/QOr6/0EFymFY0EG8LybIg5pT0FIxfIdn7STnEiH/2CUhUEHohNRbI2UNVq+NduvWps/NJHuWI6K4JUrJAgMBAAECgYAIvN12Z05Ys8ugbHjOyWzF7Ul4V1wEfzlUQ+e3EPrimdWNk3sKundrMZV+ZU6z01lNeiBIFk6Q85tZDaMbDFmn97RsFZyZCtGgcZTHERvxPFd8PPYfGf9cFk9CZ6uqdnfXnh95xLev3LouPcUg2O7PO0DLu5UwMUqndXYJOsV+QQJBAO1ZvNZrrZ7gw1nNPHGGN18I/PgJYk9NzlPQLMPKQmsVEiysgjFDzDQiF2qpwbCcQ9IvR2QlKoxb/vzqwXUl3SUCQQDXWyMAVkCgE30HSGwkygJ7yln5rw47ob+zPlIq6eyl6fPM0xl1c/NYzN84TA9cwkLYMqrXHEPqe0xjLNt6GK/VAkEA3FqvwrH9Yr9xHfI453yf8484rF6p8s5de9qNdMhWmWvaj7cot+wlrVDpRJGNzRWGsaBLO2J1+hF+zfh02vnoMQJAIB/IKNS07yv5Co0TFKnNlQmqQkSKqvqFzgcN2rjqRBWR+IvE0HBrbGOjKJKv9/ZCYl2sfkoJF4Es895NHvlvwQJAQThT4UWkxZIooofRPg5tnrZhY7Gfq0+q2lHNE5AsjsbfbLWvwVVSXlfBZxaoLp8UnyYhKwqbO669ciSrOHPpKw==";

    private byte[] rsaSplitCodec(Cipher cipher, byte[] datas, int keySize) throws Exception {
        int maxBlock;
        maxBlock = keySize / 8;
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        int offSet = 0;
        byte[] buff;
        int i = 0;
        try {
            while (datas.length > offSet) {
                if (datas.length - offSet > maxBlock) {
                    buff = cipher.doFinal(datas, offSet, maxBlock);
                } else {
                    buff = cipher.doFinal(datas, offSet, datas.length - offSet);
                }
                out.write(buff, 0, buff.length);
                i++;
                offSet = i * maxBlock;
            }
        } catch (Exception e) {
            throw new RuntimeException();
        }
        byte[] resultDatas = out.toByteArray();
        try {
            if (out != null) {
                out.close();
            }
        } catch (IOException var2) {
        }
        return resultDatas;
    }

    public String encrypt(String str) throws Exception {
        byte[] pubKeyByte = Base64.decodeBase64(PUBLIC_KEY_STRING);
        RSAPublicKey pubKey = (RSAPublicKey) KeyFactory.getInstance(RSA_ALGORITHM).generatePublic(new X509EncodedKeySpec(pubKeyByte));
        Cipher cipher = Cipher.getInstance(RSA_ALGORITHM);
        cipher.init(1, pubKey);
        byte[] encryptByte = cipher.doFinal(str.getBytes(StandardCharsets.UTF_8));
        byte[] encryptBase64 = Base64.encodeBase64(encryptByte);
        return new String(encryptBase64, StandardCharsets.UTF_8);
    }

    public String decrypt(String token) throws Exception {
        byte[] priKeyByte = Base64.decodeBase64(PRIVATE_KEY_STRING);
        RSAPrivateKey privateKey = (RSAPrivateKey) KeyFactory.getInstance(RSA_ALGORITHM).generatePrivate(new PKCS8EncodedKeySpec(priKeyByte));
        Cipher cipher = Cipher.getInstance(RSA_ALGORITHM);
        cipher.init(Cipher.DECRYPT_MODE, privateKey);
        return new String(rsaSplitCodec(cipher, Base64.decodeBase64(token), privateKey.getModulus().bitLength()), StandardCharsets.UTF_8);
    }
}
