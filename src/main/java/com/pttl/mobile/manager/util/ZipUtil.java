package com.pttl.mobile.manager.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.net.URL;
import java.net.URLConnection;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;
import java.util.zip.ZipInputStream;
import java.util.zip.ZipOutputStream;


public class ZipUtil {

    private static Logger logger = LoggerFactory.getLogger(ZipUtil.class);


    public static byte[] zipFiles(List<String> entryNames, List<byte[]> files) {

        ByteArrayOutputStream out = new ByteArrayOutputStream();
        ZipOutputStream zipOut = new ZipOutputStream(out);

        try {
            for (int i = 0; i < files.size(); i++) {
                if (files.get(i) == null) {
                    continue;
                }
                ZipEntry entry = new ZipEntry(entryNames.get(i));
                zipOut.putNextEntry(entry);
                zipOut.write(files.get(i));
            }
            zipOut.close();
            out.close();
        } catch (IOException e) {
            logger.error("zipFiles", e);
        }
        return out.toByteArray();
    }


    public static void batchFileToZip(List<String> paths, ByteArrayOutputStream byteOutPutStream) {

        ZipOutputStream zipOut = new ZipOutputStream(byteOutPutStream);
        InputStream inputStream = null;
        try {
            for (String path : paths) {
                // 文件路径
                URL url = new URL(path);
                URLConnection connection = url.openConnection();
                inputStream = connection.getInputStream();
                // 使用指定名称创建新的 ZIP 条目 （通俗点就是文件名）
                String fileName = path.substring(path.lastIndexOf("/")+1);
                ZipEntry zipEntry = new ZipEntry(fileName);
                // 开始写入新的 ZIP 文件条目并将流定位到条目数据的开始处
                zipOut.putNextEntry(zipEntry);
                byte[] b = new byte[1024];
                int length;
                while ((length = inputStream.read(b)) != -1) {
                    zipOut.write(b, 0, length);
                }
                zipOut.closeEntry();
            }
        } catch (IOException e) {
            logger.error("batchFileToZip 发生异常:", e);
        } finally {
            try {
                if (inputStream != null) {
                    inputStream.close();
                }
                zipOut.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 读取压缩包中指定一个文件的内容
     *
     * @param path     压缩包文件路径
     * @param fileName 要读取的文件名称 包含扩展名 例如 result.json
     * @return 文件内容
     */
    public static String readFile(String path, String fileName) {
        if (StringUtil.isEmpty(fileName)) {
            return null;
        }

        ZipInputStream zin = null;
        StringBuilder sb = new StringBuilder();
        try {
            zin = new ZipInputStream(new FileInputStream(path), StandardCharsets.UTF_8);
            ZipFile zf = new ZipFile(path);
            ZipEntry ze;
            while ((ze = zin.getNextEntry()) != null) {
                if (ze.toString().endsWith(fileName)) {
                    BufferedReader br = new BufferedReader(new InputStreamReader(zf.getInputStream(ze)));
                    String line;
                    while ((line = br.readLine()) != null) {
                        sb.append(line.trim());
                    }
                    br.close();
                    break;
                }
            }
        } catch (IOException e) {
            logger.error("readFile 发生异常:", e);
        } finally {
            if (zin != null) {
                try {
                    zin.closeEntry();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }

        return sb.toString();
    }
}
