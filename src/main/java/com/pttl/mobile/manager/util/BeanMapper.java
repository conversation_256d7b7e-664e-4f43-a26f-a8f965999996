package com.pttl.mobile.manager.util;

import java.lang.reflect.Array;
import java.util.ArrayList;
import java.util.List;

/**
 * bean映射工具
 *
 * <AUTHOR>
 * @date 2021/11/22
 **/

public class BeanMapper {

    private BeanMapper() {
    }

    /**
     * bean 复制
     *
     * @param source           待复制资源
     * @param destinationClass 目标类
     * @param <S>              资源泛型
     * @param <D>              目标泛型
     * @return 返回复制后的资源
     */
    public static <S, D> D map(S source, Class<D> destinationClass) {
        D d = null;
        try {
            d = destinationClass.newInstance();
        } catch (InstantiationException | IllegalAccessException e) {
            e.printStackTrace();
        }

        if (d != null) {
            org.springframework.beans.BeanUtils.copyProperties(source, d);
        }

        return d;
    }

    /**
     * bean 复制 集合
     *
     * @param sourceList       待复制资源
     * @param destinationClass 目标类
     * @param <S>              资源泛型
     * @param <D>              目标泛型
     * @return 返回复制后的资源
     */
    public static <S, D> List<D> mapList(Iterable<S> sourceList, Class<D> destinationClass) {
        List<D> destinationList = new ArrayList<>();

        for (S source : sourceList) {
            if (source != null) {
                destinationList.add(BeanMapper.map(source, destinationClass));
            }
        }

        return destinationList;
    }

    /**
     * bean 复制 数组
     *
     * @param sourceArray      待复制资源
     * @param destinationClass 目标类
     * @param <S>              资源泛型
     * @param <D>              目标泛型
     * @return 返回复制后的资源
     */
    @SuppressWarnings("unchecked")
    public static <S, D> D[] mapArray(S[] sourceArray, Class<D> destinationClass) {
        D[] destinationArray = (D[]) Array.newInstance(destinationClass, sourceArray.length);

        int len = sourceArray.length;
        for (int i = 0; i < len; ++i) {
            S source = sourceArray[i];
            if (source != null) {
                destinationArray[i] = BeanMapper.map(sourceArray[i], destinationClass);
            }
        }

        return destinationArray;
    }

}
