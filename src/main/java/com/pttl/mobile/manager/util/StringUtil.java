package com.pttl.mobile.manager.util;

import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class StringUtil extends StringUtils {
    private static Pattern PATTERN_NUMERIC = Pattern.compile("-?[0-9]*.?[0-9]*");
    private static Pattern PATTERN_PHONE = Pattern.compile("^((13[0-9])|(14[5,7,9])|(15([0-3]|[5-9]))|(166)|(17[0,1,3,5,6,7,8])|(18[0-9])|(19[8|9]))\\d{8}$");

    public StringUtil() {
    }

    public static boolean isNumeric(String str) {
        Matcher matcher = PATTERN_NUMERIC.matcher(str);
        return matcher.matches();
    }

    public static boolean isValidPhoneNumber(String str) {
        if (isEmpty(str))
            return false;

        if (str.length() != 11)
            return false;

        Matcher m = PATTERN_PHONE.matcher(str);
        return m.matches();
    }


    public static boolean isEmpty(String str) {
        return str == null || str.length() == 0;
    }

    public static boolean equals(String param1, String param2) {
        if (!isEmpty(param1) && !isEmpty(param2)) {
            return param1.equals(param2);
        } else {
            return false;
        }
    }

    public static boolean equalsIgoneUpper(String param1, String param2) {
        if (!isEmpty(param1) && !isEmpty(param2)) {
            return toUpperCase(param1).equals(toUpperCase(param2));
        } else {
            return false;
        }
    }

    public static String toUpperCase(String param) {
        return isEmpty(param) ? "" : param.toUpperCase();
    }

    public static String toLowerCase(String param) {
        return isEmpty(param) ? "" : param.toLowerCase();
    }

    public static Integer toInteger(Object param) {
        try {
            if (param == null) {
                return 0;
            } else {
                String str = String.valueOf(param);
                if (!isEmpty(str) && !equals("NULL", toUpperCase(str))) {
                    Integer res = Integer.valueOf(str.replace(",", ""));
                    return res;
                } else {
                    return 0;
                }
            }
        } catch (Exception e) {
            return 0;
        }
    }

    public static BigDecimal toBigDecimal(Object param) {
        if (param == null) {
            return BigDecimal.ZERO;
        } else {
            try {
                BigDecimal res = new BigDecimal(toString(param).replace(",", ""));
                return res;
            } catch (Exception e) {
                return BigDecimal.ZERO;
            }
        }
    }

    public static String toString(Object param) {
        return param == null ? "" : param.toString();
    }

    public static boolean isGuid(String id) {
        if (!isEmpty(id)) {
            return id.indexOf("@") < 0;
        } else {
            return false;
        }
    }

    public static String concat(Object... args) {
        StringBuilder sb = new StringBuilder();
        for (Object obj : args) {
            if (obj != null) {
                sb.append(obj);
            }
        }
        return sb.toString();
    }

    public static String join(String[] array) {
        return join(array, "");
    }

    public static String join(String[] list, String separator) {
        separator = separator == null ? "" : separator;
        StringBuffer buff = new StringBuffer(5 * list.length);

        for (int i = 0; i < list.length; ++i) {
            String s = list[i];
            if (i > 0) {
                buff.append(separator);
            }

            if (s != null) {
                buff.append(s);
            }
        }

        return buff.toString();
    }


    /**
     * 获取应用完整访问链接
     *
     * @param oldPath                  原路径
     * @param applicationIconUrlPrefix 待拼接前缀
     * @return 获取应用完整访问链接
     */
    public static String getApplicationIconUr(String oldPath, String applicationIconUrlPrefix) {
        // /home/<USER>/manager/file/upload/989170_1672881080984.jpg
        if (isEmpty(oldPath)) {
            return null;
        }

        String substring = oldPath.substring(oldPath.lastIndexOf("/"));
        return applicationIconUrlPrefix + substring;
    }
}
