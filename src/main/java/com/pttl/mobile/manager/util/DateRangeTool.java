package com.pttl.mobile.manager.util;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 时间范围工具
 * (查询时间范围7天的-默认)
 *
 * <AUTHOR>
 * @date 2023/9/6
 */
public class DateRangeTool {

    /**
     * 时间范围对象
     *
     * @date 2023/9/6
     */
    public static class DateRange {
        private Date startDate;
        private Date endDate;

        public DateRange(Date startDate, Date endDate) {
            this.startDate = startDate;
            this.endDate = endDate;
        }

        public Date getStartDate() {
            return startDate;
        }

        public Date getEndDate() {
            return endDate;
        }
    }

    /**
     * 计算日期范围
     *
     * @param inputDate   起始输入时间
     * @param intervalDay 间隔天数
     * @return 时间范围集合
     * ([{2023 - 08 - 15 19 : 00 : 31, 2023 - 08 - 16 19 : 00 : 31},
     * {2023 - 08 - 16 19 : 00 : 32 ，2023 - 08 - 17 19 : 00 : 32}])
     */
    public static List<DateRange> calculateDateRanges(Date inputDate, int intervalDay) {
        List<DateRange> dateRanges = new ArrayList<>();

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(inputDate);

        // 给定时间加上一秒
        calendar.add(Calendar.SECOND, 1);
        inputDate = calendar.getTime();

        Date currentDate = new Date();

        while (inputDate.before(currentDate)) {
            Date startDate = inputDate;

            // 计算结束时间为开始时间加6天，或者当前时间，取两者中较小的那个
            calendar.setTime(startDate);
            calendar.add(Calendar.DAY_OF_MONTH, intervalDay > 1 ? (intervalDay - 1) : 1);
            Date endDate = calendar.getTime();
            endDate = (endDate.before(currentDate)) ? endDate : currentDate;

            dateRanges.add(new DateRange(startDate, endDate));

            // 设置下一个查询开始时间为结束时间的后一秒
            calendar.setTime(endDate);
            calendar.add(Calendar.SECOND, 1);
            inputDate = calendar.getTime();
        }

        return dateRanges;
    }

    public static void main(String[] args) {
        DateTime parse = DateUtil.parse("2023-08-15 19:00:30");
        Date inputDate = parse.toJdkDate();
        List<DateRange> dateRanges = calculateDateRanges(inputDate, 7);

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        for (DateRange range : dateRanges) {
            System.out.println("开始时间：" + sdf.format(range.getStartDate()) + "，结束时间：" + sdf.format(range.getEndDate()));
        }
    }
}



