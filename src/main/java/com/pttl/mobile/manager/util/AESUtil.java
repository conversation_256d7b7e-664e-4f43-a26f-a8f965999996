package com.pttl.mobile.manager.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.springframework.stereotype.Component;

import javax.crypto.*;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;

/**
 * AES加解密工具
 *
 * <AUTHOR>
 * @date 2025/5/29
 */
@Slf4j
@Component
public class AESUtil {

    private static final String AES_ALGORITHM = "AES";
    private static final String AES_TRANSFORMATION = "AES/CBC/PKCS5Padding";
    // 16字节IV
    private static final String DEFAULT_IV = "1234567890123456";

    /**
     * 生成AES密钥
     *
     * @param keySize 密钥长度，可以是128、192、256位
     * @return Base64编码的密钥字符串
     */
    public static String generateKey(int keySize) {
        try {
            KeyGenerator keyGenerator = KeyGenerator.getInstance(AES_ALGORITHM);
            SecureRandom secureRandom = new SecureRandom();
            keyGenerator.init(keySize, secureRandom);
            SecretKey secretKey = keyGenerator.generateKey();
            return Base64.encodeBase64String(secretKey.getEncoded());
        } catch (NoSuchAlgorithmException e) {
            log.error("生成AES密钥失败: {}", e.getMessage());
            throw new RuntimeException("生成AES密钥失败", e);
        }
    }

    /**
     * 生成随机IV（初始化向量）
     * AES CBC模式需要16字节的IV
     *
     * @return 16字节的随机IV字符串
     */
    public static String generateIV() {
        SecureRandom secureRandom = new SecureRandom();
        // AES块大小为16字节
        byte[] iv = new byte[16];
        secureRandom.nextBytes(iv);
        // 将字节数组转换为可打印的字符串（使用Base64编码）
        return Base64.encodeBase64String(iv);
    }

    /**
     * 生成指定长度的随机IV（初始化向量）
     *
     * @param length IV的字节长度，AES通常使用16字节
     * @return 指定长度的随机IV字符串（Base64编码）
     */
    public static String generateIV(int length) {
        if (length <= 0) {
            throw new IllegalArgumentException("IV长度必须大于0");
        }
        SecureRandom secureRandom = new SecureRandom();
        byte[] iv = new byte[length];
        secureRandom.nextBytes(iv);
        return Base64.encodeBase64String(iv);
    }

    /**
     * AES加密
     *
     * @param plaintext 明文
     * @param key Base64编码的密钥
     * @return Base64编码的密文
     */
    public static String encrypt(String plaintext, String key) {
        return encrypt(plaintext, key, DEFAULT_IV);
    }

    /**
     * AES加密（指定IV）
     *
     * @param plaintext 明文
     * @param key Base64编码的密钥
     * @param iv 初始化向量（可以是普通字符串或Base64编码的字符串）
     * @return Base64编码的密文
     */
    public static String encrypt(String plaintext, String key, String iv) {
        try {
            byte[] keyBytes = Base64.decodeBase64(key);
            SecretKeySpec keySpec = new SecretKeySpec(keyBytes, AES_ALGORITHM);

            Cipher cipher = Cipher.getInstance(AES_TRANSFORMATION);

            // 尝试将IV作为Base64解码，如果失败则作为普通字符串处理
            byte[] ivBytes;
            try {
                ivBytes = Base64.decodeBase64(iv);
                // 检查解码后的长度是否为16字节
                if (ivBytes.length != 16) {
                    ivBytes = iv.getBytes(StandardCharsets.UTF_8);
                }
            } catch (Exception e) {
                ivBytes = iv.getBytes(StandardCharsets.UTF_8);
            }

            IvParameterSpec ivSpec = new IvParameterSpec(ivBytes);
            cipher.init(Cipher.ENCRYPT_MODE, keySpec, ivSpec);

            byte[] encryptedBytes = cipher.doFinal(plaintext.getBytes(StandardCharsets.UTF_8));
            return Base64.encodeBase64String(encryptedBytes);
        } catch (Exception e) {
            log.error("AES加密失败: {}", e.getMessage());
            throw new RuntimeException("AES加密失败", e);
        }
    }

    /**
     * AES解密
     *
     * @param ciphertext Base64编码的密文
     * @param key Base64编码的密钥
     * @return 明文
     */
    public static String decrypt(String ciphertext, String key) {
        return decrypt(ciphertext, key, DEFAULT_IV);
    }

    /**
     * AES解密（指定IV）
     *
     * @param ciphertext Base64编码的密文
     * @param key Base64编码的密钥
     * @param iv 初始化向量（可以是普通字符串或Base64编码的字符串）
     * @return 明文
     */
    public static String decrypt(String ciphertext, String key, String iv) {
        try {
            byte[] keyBytes = Base64.decodeBase64(key);
            SecretKeySpec keySpec = new SecretKeySpec(keyBytes, AES_ALGORITHM);

            Cipher cipher = Cipher.getInstance(AES_TRANSFORMATION);

            // 尝试将IV作为Base64解码，如果失败则作为普通字符串处理
            byte[] ivBytes;
            try {
                ivBytes = Base64.decodeBase64(iv);
                // 检查解码后的长度是否为16字节
                if (ivBytes.length != 16) {
                    ivBytes = iv.getBytes(StandardCharsets.UTF_8);
                }
            } catch (Exception e) {
                ivBytes = iv.getBytes(StandardCharsets.UTF_8);
            }

            IvParameterSpec ivSpec = new IvParameterSpec(ivBytes);
            cipher.init(Cipher.DECRYPT_MODE, keySpec, ivSpec);

            byte[] decryptedBytes = cipher.doFinal(Base64.decodeBase64(ciphertext));
            return new String(decryptedBytes, StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("AES解密失败: {}", e.getMessage());
            throw new RuntimeException("AES解密失败", e);
        }
    }

    public static void main(String[] args) throws NoSuchPaddingException, NoSuchAlgorithmException, InvalidAlgorithmParameterException, InvalidKeyException, IllegalBlockSizeException, BadPaddingException {
        // 生成128位密钥
        String key = generateKey(128);
        System.out.println("生成的AES密钥: " + key);

        // 测试加密解密
        String originalText = "这是需要加密的敏感数据";
        System.out.println("原始文本: " + originalText);

        // 使用默认IV加密
        String encryptedText = encrypt(originalText, key);
        System.out.println("加密后: " + encryptedText);

        // 解密
        String decryptedText = decrypt(encryptedText, key);
        System.out.println("解密后: " + decryptedText);

        System.out.println("\n=== 测试自定义IV功能 ===");

        // 使用固定自定义IV（16字节字符串）
        String customIv = "ABCDEFGHIJKLMNOP";
        String encryptedWithCustomIv = encrypt(originalText, key, customIv);
        System.out.println("使用固定自定义IV加密后: " + encryptedWithCustomIv);

        String decryptedWithCustomIv = decrypt(encryptedWithCustomIv, key, customIv);
        System.out.println("使用固定自定义IV解密后: " + decryptedWithCustomIv);

        // 生成随机IV（Base64编码）
        String randomIv = generateIV();
        System.out.println("生成的随机IV (Base64): " + randomIv);

        String encryptedWithRandomIv = encrypt(originalText, key, randomIv);
        System.out.println("使用随机IV加密后: " + encryptedWithRandomIv);

        String decryptedWithRandomIv = decrypt(encryptedWithRandomIv, key, randomIv);
        System.out.println("使用随机IV解密后: " + decryptedWithRandomIv);

        // 生成指定长度的IV
        String customLengthIv = generateIV(16);
        System.out.println("生成的16字节IV (Base64): " + customLengthIv);

        String encryptedWithCustomLengthIv = encrypt(originalText, key, customLengthIv);
        System.out.println("使用16字节IV加密后: " + encryptedWithCustomLengthIv);

        String decryptedWithCustomLengthIv = decrypt(encryptedWithCustomLengthIv, key, customLengthIv);
        System.out.println("使用16字节IV解密后: " + decryptedWithCustomLengthIv);

        System.out.println("\n=== 测试固定密钥和IV的加密解密 ===");

        // 加密部分
        String key2 = "BPM666!#@$888bpmBPM666!#@$888bpm";
        String iv2 = "BPM666!#@$888bpm";
        // 要加密的明文
        String plainText = "Hello World!";

        try {
            // 加密过程
            String encryptedData = appointEncrypt(plainText, key2, iv2);

            System.out.println("原始明文: " + plainText);
            System.out.println("加密后的数据: " + encryptedData);

            // 解密部分（验证加密结果）
            String decryptedData = appointDecrypt(encryptedData, key2, iv2);
            System.out.println("解密后的数据: " + decryptedData);

        } catch (Exception e) {
            System.err.println("加密解密过程出错: " + e.getMessage());
            e.printStackTrace();
        }
    }

    public static String appointDecrypt(String ciphertext, String key, String iv) {
        try {
            byte[] keyBytes = key.getBytes();
            byte[] ivBytes = iv.getBytes();
            SecretKeySpec keySpec = new SecretKeySpec(keyBytes, AES_ALGORITHM);
            IvParameterSpec ivSpec = new IvParameterSpec(ivBytes);
            Cipher decryptCipher = Cipher.getInstance(AES_TRANSFORMATION);
            decryptCipher.init(Cipher.DECRYPT_MODE, keySpec, ivSpec);
            byte[] decryptedBytes = decryptCipher.doFinal(Base64.decodeBase64(ciphertext));
            return new String(decryptedBytes);
        } catch (Exception e) {
            log.error("AES解密失败: {}", e.getMessage());
            throw new RuntimeException("AES解密失败", e);
        }
    }

    public static String appointEncrypt(String plaintext, String key, String iv) {
        try {
            byte[] keyBytes = key.getBytes();
            byte[] ivBytes = iv.getBytes();
            SecretKeySpec keySpec = new SecretKeySpec(keyBytes, AES_ALGORITHM);
            IvParameterSpec ivSpec = new IvParameterSpec(ivBytes);

            // CBC模式和PKCS5Padding填充方式
            Cipher encryptCipher = Cipher.getInstance(AES_TRANSFORMATION);
            encryptCipher.init(Cipher.ENCRYPT_MODE, keySpec, ivSpec);

            byte[] plainBytes = plaintext.getBytes();
            byte[] encryptedBytes = encryptCipher.doFinal(plainBytes);
            return Base64.encodeBase64String(encryptedBytes);
        } catch (Exception e) {
            log.error("AES加密失败: {}", e.getMessage());
            throw new RuntimeException("AES加密失败", e);
        }
    }

}
