package com.pttl.mobile.manager.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.net.URLEncoder;
import java.util.Calendar;

public class FileUtil {

    private static Logger logger = LoggerFactory.getLogger(FileUtil.class);

    public static final String FILE_SEPARATOR = System.getProperty("file.separator");


    public static String getUTF8FileName(String fileName) {
        try {
            return new String(fileName.getBytes("UTF-8"), "ISO-8859-1");
        } catch (UnsupportedEncodingException e) {
            return fileName;
        }
    }

    public static String urlEncode(String str) {

        try {
            return URLEncoder.encode(str, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            logger.error("urlEncode", e);
            return null;
        }
    }

    public static void deleteFile(String filePath) {
        new File(filePath).delete();
    }

    public static byte[] readFile(String filePath) {

        File filePdf = new File(filePath);

        InputStream is = null;
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        try {
            is = new BufferedInputStream(new FileInputStream(filePdf));
            byte[] b = new byte[1024];
            int n;
            while ((n = is.read(b)) != -1) {
                out.write(b, 0, n);
            }
            return out.toByteArray();
        } catch (Exception e) {
            logger.error("readFile", e);
        } finally {
            if (is != null) {
                try {
                    is.close();
                } catch (Exception ignore) {
                    logger.error("readFile", ignore);
                }
            }
        }
        return null;
    }


    public static void writeFile(byte[] data, String dest) {

        FileOutputStream out = null;
        try {
            out = new FileOutputStream(dest);
            out.write(data);
        } catch (Exception e) {
            logger.error("writeFile", e);
        } finally {
            if (out != null) {
                try {
                    out.close();
                } catch (Exception ignore) {
                    logger.error("writeFile", ignore);
                }
            }
        }
    }


    public static String getDateDir() {

        Calendar now = Calendar.getInstance();
        int year = now.get(Calendar.YEAR);
        int month = now.get(Calendar.MONTH) + 1;
        int day = now.get(Calendar.DAY_OF_MONTH);
        String monthStr = month >= 10 ? Integer.toString(month) : "0" + month;
        String dayStr = day >= 10 ? Integer.toString(day) : "0" + day;
        return year + monthStr + dayStr;
    }


    public static String getFileExtNameFromFileName(String fileName) {

        if (fileName == null) {
            return null;
        }

        int dotIndex = fileName.lastIndexOf(".");
        if (dotIndex == -1) {
            return null;
        }

        return fileName.substring(dotIndex);
    }

}
