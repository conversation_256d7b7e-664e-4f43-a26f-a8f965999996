package com.pttl.mobile.manager.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * httpclient工具
 *
 * <AUTHOR>
 */
@Slf4j
public class HttpClientUtil {

	/**
	 * bpm系统登陆成功状态码
	 */
	private static final int BPM_STATUS_CODE_SUCCESS = 302;

	/**
	 * 默认成功状态码
	 */
	public static final int DEFAULT_STATUS_CODE_SUCCESS = 200;

	public static String doGet(String url, Map<String, String> param) {

		// 创建Httpclient对象
		CloseableHttpClient httpclient = HttpClients.createDefault();

		String resultString = "";
		CloseableHttpResponse response = null;
		try {
			// 创建uri
			URIBuilder builder = new URIBuilder(url);
			builder.setCharset(Charset.forName("UTF-8"));
			if (param != null) {
				for (String key : param.keySet()) {
					builder.addParameter(key, param.get(key));
				}
			}
			URI uri = builder.build();
			// 创建http GET请求
			HttpGet httpGet = new HttpGet(uri);
			// 执行请求
			response = httpclient.execute(httpGet);
			// 判断返回状态是否为200
			if (response.getStatusLine().getStatusCode() == 200) {
				resultString = EntityUtils.toString(response.getEntity(), "UTF-8");
			}
		} catch (Exception e) {
			log.error("请求异常：", e);
		} finally {
			try {
				if (response != null) {
					response.close();
				}
				httpclient.close();
			} catch (IOException e) {
				log.error("请求关闭异常：", e);
			}
		}
		return resultString;
	}

	public static String doGet(String url) {
		return doGet(url, null);
	}

	public static String doPost(String url, Map<String, String> param) {
		// 创建Httpclient对象
		CloseableHttpClient httpClient = HttpClients.createDefault();
		CloseableHttpResponse response = null;
		String resultString = "";
		try {
			// 创建Http Post请求
			HttpPost httpPost = new HttpPost(url);
			// 创建参数列表
			if (param != null) {
				List<NameValuePair> paramList = new ArrayList<>();
				for (String key : param.keySet()) {
					paramList.add(new BasicNameValuePair(key, param.get(key)));
				}
				// 模拟表单
				UrlEncodedFormEntity entity = new UrlEncodedFormEntity(paramList, "utf-8");
				httpPost.setEntity(entity);
			}
			// 执行http请求
			response = httpClient.execute(httpPost);
			resultString = EntityUtils.toString(response.getEntity(), "utf-8");
		} catch (Exception e) {
			log.error("请求异常：", e);
		} finally {
			try {
				if (response != null) {
					response.close();
				}
			} catch (IOException e) {
				log.error("请求关闭异常：", e);
			}
		}
		return resultString;
	}

	/**
	 * get方法 设置请求头参数
	 * 使用这个需要注意 (需要手动关闭response) 代码如下：
	 * try {
	 * if (response != null) {
	 * response.close();
	 * }
	 * } catch (IOException e) {
	 * log.error("请求关闭异常：", e);
	 * }
	 *
	 * @param url         地址
	 * @param param       参数
	 * @param headerParam 头部参数
	 * @return 响应字符窜混
	 */
	public static CloseableHttpResponse doGetOfHeaderParam(String url, Map<String, String> param, Map<String, String> headerParam) {

		// 创建Httpclient对象
		CloseableHttpClient httpclient = HttpClients.createDefault();

		CloseableHttpResponse response = null;
		try {
			// 创建uri
			URIBuilder builder = new URIBuilder(url);
			builder.setCharset(StandardCharsets.UTF_8);

			// 请求参数
			if (param != null) {
				for (String key : param.keySet()) {
					builder.addParameter(key, param.get(key));
				}
			}

			URI uri = builder.build();
			// 创建http GET请求
			HttpGet httpGet = new HttpGet(uri);

			// 请求头
			if (headerParam != null) {
				for (String key : headerParam.keySet()) {
					httpGet.setHeader(key, headerParam.get(key));
				}
			}

			// 执行请求
			response = httpclient.execute(httpGet);
		} catch (Exception e) {
			log.error("请求异常：", e);
		}
		return response;
	}

	public static String doPost(String url) {
		return doPost(url, null);
	}

	public static String doPostJson(String url, String json) {
		// 创建Httpclient对象
		CloseableHttpClient httpClient = HttpClients.createDefault();
		CloseableHttpResponse response = null;
		String resultString = "";
		try {
			// 创建Http Post请求
			HttpPost httpPost = new HttpPost(url);
			log.info("url: {}, param: {}", url, json);
			// 创建请求内容
			StringEntity entity = new StringEntity(json, ContentType.create("application/json", "utf-8"));
			entity.setContentEncoding("utf-8");
			httpPost.setEntity(entity);
			// 执行http请求
			response = httpClient.execute(httpPost);
			resultString = EntityUtils.toString(response.getEntity(), "utf-8");
			log.info("url: {}, result: {}", url, resultString);
		} catch (Exception e) {
			log.error("请求异常：", e);
		} finally {
			try {
				if (response != null) {
					response.close();
				}
			} catch (IOException e) {
				log.error("请求关闭异常：", e);
			}
		}
		return resultString;
	}

	/**
	 * post请求json数据并设置请求头
	 *
	 * @param url         地址
	 * @param json        json格式数据
	 * @param headerParam 请求头部参数
	 * @return 返回请求报文
	 */
	public static String doPostJsonAndHeader(String url, String json, Map<String, String> headerParam) {
		// 创建Httpclient对象
		CloseableHttpClient httpClient = HttpClients.createDefault();
		CloseableHttpResponse response = null;
		String resultString = "";
		try {
			// 创建Http Post请求
			HttpPost httpPost = new HttpPost(url);

			log.info("url: {}, param: {}", url, json);

			// 请求头
			if (headerParam != null) {
				for (String key : headerParam.keySet()) {
					httpPost.setHeader(key, headerParam.get(key));
				}
			}

			// 创建请求内容
			StringEntity entity = new StringEntity(json, ContentType.create("application/json", "utf-8"));
			entity.setContentEncoding("utf-8");
			httpPost.setEntity(entity);

			// 执行http请求
			response = httpClient.execute(httpPost);
			resultString = EntityUtils.toString(response.getEntity(), "utf-8");
			log.info("url: {}, result: {}", url, resultString);
		} catch (Exception e) {
			log.error("请求异常：", e);
		} finally {
			try {
				if (response != null) {
					response.close();
				}
			} catch (IOException e) {
				log.error("请求关闭异常：", e);
			}
		}
		return resultString;
	}

	public static String doPostXML(String url, String xml, String charset) {
		// 创建Httpclient对象
		CloseableHttpClient httpClient = HttpClients.createDefault();
		CloseableHttpResponse response = null;
		String resultString = "";
		log.info("请求参数：url={},xml={},charset={}", url, xml, charset);
		if (StringUtil.isEmpty(charset)) {
			charset = "utf-8";
		}
		try {
			// 创建Http Post请求
			HttpPost httpPost = new HttpPost(url);
			// 创建请求内容
			StringEntity entity = new StringEntity(xml, ContentType.create("text/xml", charset));
			httpPost.setEntity(entity);
			// 执行http请求
			response = httpClient.execute(httpPost);
			resultString = EntityUtils.toString(response.getEntity(), charset);
		} catch (Exception e) {
			log.error("请求异常：", e);
		} finally {
			try {
				if (response != null) {
					response.close();
				}
			} catch (IOException e) {
				log.error("请求关闭异常：", e);
			}
		}
		log.info("请求结果：resultString={}", resultString);
		return resultString;
	}

	/**
	 * 模拟请求
	 *
	 * @param url  地址
	 * @param json 参数
	 * @return 响应对象
	 */
	public static CloseableHttpResponse doPostJson2(String url, String json) {
		log.info("url: {}, param: {}", url, json);
		// 创建Httpclient对象
		CloseableHttpClient httpClient = HttpClients.createDefault();
		CloseableHttpResponse response = null;
		try {
			// 创建Http Post请求
			HttpPost httpPost = new HttpPost(url);
			// 创建请求内容
			StringEntity entity = new StringEntity(json, ContentType.create("application/json", "utf-8"));
			entity.setContentEncoding("utf-8");
			httpPost.setEntity(entity);

			// 执行http请求
			response = httpClient.execute(httpPost);
		} catch (Exception e) {
			log.error("请求异常：", e);
		}
		return response;
	}

	/**
	 * 模拟请求 返回整个响应
	 * 使用这个需要铁北注意 (需要手动关闭response) 代码如下：
	 * try {
	 * 		if (response != null) {
	 * 			response.close();
	 *      }
	 *  } catch (IOException e) {
	 * 		log.error("请求关闭异常：", e);
	 *  }
	 *
	 *
	 * @param url         地址
	 * @param param       参数
	 * @param headerParam 请求头
	 * @return 整个响应
	 */
	public static CloseableHttpResponse doPost(String url, Map<String, String> param, Map<String, String> headerParam) {
		// 创建Httpclient对象
		CloseableHttpClient httpClient = HttpClients.createDefault();
		CloseableHttpResponse response = null;
		try {
			// 创建Http Post请求
			HttpPost httpPost = new HttpPost(url);
			// 设置请求头参数
			if (headerParam != null) {
				for (String key : headerParam.keySet()) {
					httpPost.setHeader(key, headerParam.get(key));
				}
			}

			// 创建参数列表
			UrlEncodedFormEntity urlEncodedFormEntity = dealUrlEncodedFormEntity(param);
			if (urlEncodedFormEntity != null) {
				httpPost.setEntity(urlEncodedFormEntity);
			}

			// 执行http请求
			response = httpClient.execute(httpPost);
		} catch (Exception e) {
			log.error("请求异常：", e);
		}

		return response;
	}

	/**
	 * 模拟请求 返回整个响应
	 *
	 * @param url         地址
	 * @param param       参数
	 * @param headerParam 请求头
	 * @return 响应体
	 */
	public static String doPost2(String url, Map<String, String> param, Map<String, String> headerParam) {
		CloseableHttpResponse closeableHttpResponse = doPost(url, param, headerParam);
		// 获取响应体
		try {
			return EntityUtils.toString(closeableHttpResponse.getEntity(), "utf-8");
		} catch (IOException e) {
			log.error("{} error: {}", url, e.getMessage());
			e.printStackTrace();
		} finally {
			try {
				closeableHttpResponse.close();
			} catch (IOException e) {
				log.error("请求关闭异常：", e);
			}
		}

		return null;
	}

	/**
	 * 创建表单参数
	 *
	 * @param param 参数
	 * @return 表单参数
	 * @throws UnsupportedEncodingException 转换异常
	 */
	private static UrlEncodedFormEntity dealUrlEncodedFormEntity(Map<String, String> param) throws UnsupportedEncodingException {
		// 创建参数列表
		if (param != null) {
			List<NameValuePair> paramList = new ArrayList<>();
			for (String key : param.keySet()) {
				paramList.add(new BasicNameValuePair(key, param.get(key)));
			}
			// 模拟表单
			return new UrlEncodedFormEntity(paramList, "utf-8");
		}

		return null;
	}
}
