package com.pttl.mobile.manager.util;

import cn.hutool.core.collection.CollUtil;
import cn.jiguang.common.utils.StringUtils;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.reflect.MethodSignature;

import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.IOException;
import java.lang.reflect.Method;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * aop辅助工具类
 *
 * <AUTHOR>
 * @date 2022/11/11
 **/

public class AspectSupportUtil {

    private AspectSupportUtil() {
    }

    /**
     * 获取body
     *
     * @param request request
     * @return str
     */
    public static String getRequestBody(HttpServletRequest request) {
        BufferedReader br;
        try {
            br = request.getReader();
            String str;
            StringBuilder stringBuffer = new StringBuilder();
            while ((str = br.readLine()) != null) {
                stringBuffer.append(str);
            }
            String toString = stringBuffer.toString();
            if (StringUtils.isNotEmpty(toString)) {
                return toString;
            }
            return null;
        } catch (IOException e1) {
            e1.getStackTrace();
            return null;
        }
    }

    /**
     * 获取方法名
     *
     * @param joinPoint 切入
     * @return 方法名
     */
    public static String getMethodName(JoinPoint joinPoint) {
        // 从切面织入点处通过反射机制获取织入点处的方法
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        // 获取切入点所在的方法 获取操作
        Method method = signature.getMethod();
        // 获取请求的类名
        String className = joinPoint.getTarget().getClass().getName();

        // 获取请求的方法名
        return className + "." + method.getName();
    }

    /**
     * 转换request 请求参数
     *
     * @param paramMap request获取的参数数组
     */
    public static Map<String, String> convertMap(Map<String, String[]> paramMap) {
        if (CollUtil.isEmpty(paramMap)) {
            return Collections.EMPTY_MAP;
        }

        Map<String, String> rtnMap = new HashMap<>(paramMap.size());
        for (Map.Entry<String, String[]> key : paramMap.entrySet()) {
            rtnMap.put(key.getKey(), key.getValue()[0]);
        }
        return rtnMap;
    }

    /**
     * 转换异常信息为字符串
     *
     * @param exceptionName    异常名称
     * @param exceptionMessage 异常信息
     * @param elements         堆栈信息
     */
    public static String stackTraceToString(String exceptionName, String exceptionMessage, StackTraceElement[] elements) {
        StringBuilder stringBuilder = new StringBuilder();
        for (StackTraceElement stet : elements) {
            stringBuilder.append(stet).append("\n");
        }
        return exceptionName + ":" + exceptionMessage + "\n\t" + stringBuilder;
    }
}
