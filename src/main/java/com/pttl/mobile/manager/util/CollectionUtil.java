package com.pttl.mobile.manager.util;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

public class CollectionUtil {


    public static boolean isEmpty(Collection collection) {
        return collection == null || collection.isEmpty();
    }


    public static String collectionToString(Collection<String> collection) {

        if (isEmpty(collection)) {
            return null;
        }

        StringBuilder sb = new StringBuilder();
        int i = 0;
        for (String tmp : collection) {
            if (i > 0) {
                sb.append(",");
            }
            i++;
            sb.append(tmp);
        }
        return sb.toString();
    }


    public static <T> List<T> asList(Collection<T> collection) {

        if (collection == null) {
            return null;
        }

        List<T> list = new ArrayList<>();
        list.addAll(collection);
        return list;
    }

    public static <T> List<T> asList(T[] arr) {

        if (arr == null) {
            return null;
        }

        List<T> list = new ArrayList<>();
        for (T tmp : arr) {
            list.add(tmp);
        }
        return list;
    }


    public static <T> List<T> asList(T element) {

        if (element == null) {
            return null;
        }

        List<T> list = new ArrayList<>();
        list.add(element);
        return list;
    }


}
