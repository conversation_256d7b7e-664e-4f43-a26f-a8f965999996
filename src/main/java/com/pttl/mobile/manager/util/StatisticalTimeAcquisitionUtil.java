package com.pttl.mobile.manager.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 统计时间范围获取工具
 *
 * <AUTHOR>
 * @date 2023/3/21
 **/

public class StatisticalTimeAcquisitionUtil {

    /**
     * 查询最近一周时 使用该类型  表示获取近7天 从0开始的所以是6
     */
    public static final int DATE_FIELD_TYPE_DAYS_7 = -6;


    /**
     * 近两周
     */
    public static final int DATE_FIELD_TYPE_DAYS_14 = -13;

    /**
     * 查询最近一月时 使用该类型  表示获取近30天 从0开始的所以是29
     */
    public static final int DATE_FIELD_TYPE__DAYS_30 = -29;

    /**
     * 查询近一年时
     */
    public static final int DATE_FIELD_TYPE_MONTHS_12 = -11;

    public static void main(String[] args) {
        List<String> dateTimes = hourList();
        dateTimes.forEach(System.out::println);
        System.out.println("----------");

        List<String> days7List = days7List();
        days7List.forEach(System.out::println);
        System.out.println("----------");


        List<String> days30List = days30List();
        days30List.forEach(System.out::println);
        System.out.println("----------");

        List<String> month12List = month12List();
        month12List.forEach(System.out::println);
        System.out.println("----------");

        Date date = new Date();
        DateTime dateTime1 = DateUtil.endOfDay(date);
        System.out.println(dateTime1);
        DateTime dateTime = DateUtil.offsetMonth(date, -1);
        System.out.println(DateUtil.beginOfDay(dateTime));

        Date date1 = new Date();
        DateTime endDate = DateUtil.endOfDay(date1);
        System.out.println(endDate.toString());
        DateTime beginTime = DateUtil.offsetMonth(date1, -1);
        DateTime beginDate = DateUtil.beginOfDay(beginTime);
        System.out.println(beginDate.toString());
    }

    /**
     * 获取从当天零时开始到当前时间(小时) 的整点
     * 如下:
     * 2023-03-21 00:00:00
     * 2023-03-21 01:00:00
     * 2023-03-21 02:00:00
     */
    public static List<String> hourList() {
        Date now = new Date();
        DateTime dateTime = DateUtil.beginOfDay(now);
        return DateUtil.rangeToList(dateTime, now, DateField.HOUR_OF_DAY).stream()
                .map(DateTime::toString).collect(Collectors.toList());
    }


    /**
     * 获取过去近一周日期
     * 如下:
     * 2023-03-15
     * 2023-03-16
     * 2023-03-17
     * 2023-03-18
     * 2023-03-19
     * 2023-03-20
     * 2023-03-21
     *
     * @return 日期列表
     */
    public static List<String> days7List() {
        return dayList(DATE_FIELD_TYPE_DAYS_7, DateField.DAY_OF_YEAR);
    }

    /**
     * 获取过去近30天日期
     * 如下:
     * 2023-02-20
     * 2023-02-21
     * 2023-02-22
     * 2023-02-23
     * 2023-02-24
     * 2023-02-25
     * 2023-02-26
     * 2023-02-27
     * 2023-02-28
     * 2023-03-01
     * 2023-03-02
     * 2023-03-03
     * 2023-03-04
     * 2023-03-05
     * 2023-03-06
     * 2023-03-07
     * 2023-03-08
     * 2023-03-09
     * 2023-03-10
     * 2023-03-11
     * 2023-03-12
     * 2023-03-13
     * 2023-03-14
     * 2023-03-15
     * 2023-03-16
     * 2023-03-17
     * 2023-03-18
     * 2023-03-19
     * 2023-03-20
     * 2023-03-21
     *
     * @return 日期列表
     */
    public static List<String> days30List() {
        return dayList(DATE_FIELD_TYPE__DAYS_30, DateField.DAY_OF_YEAR);
    }

    /**
     * 获取过去近1年月份日期
     * 如下:
     * 2022-04
     * 2022-05
     * 2022-06
     * 2022-07
     * 2022-08
     * 2022-09
     * 2022-10
     * 2022-11
     * 2022-12
     * 2023-01
     * 2023-02
     * 2023-03
     *
     * @return 日期列表
     */
    public static List<String> month12List() {
        return dayList(DATE_FIELD_TYPE_MONTHS_12, DateField.MONTH);
    }

    /**
     * 获取指定天的日期集合
     *
     * @param days      获取近几天的日期(days-1) 从零开始,所以需要-1
     *                  正数为当前日期往后数(未来的), 负数为当前日期往前数(过去的)
     * @param dateField 获取的数据类型, 天/月     DateField.DAY_OF_YEAR/天  DateField.MONTH/月
     *                  例:
     *                  2023-03-18
     *                  2023-03-19
     *                  2023-03-20
     *                  2023-03-21
     * @return 日期集合
     */
    public static List<String> dayList(int days, DateField dateField) {
        Date now = new Date();
        DateTime dateTime = DateUtil.offset(now, dateField, days);
        List<DateTime> dateTimes;
        if (days < 0) {
            dateTimes = DateUtil.rangeToList(dateTime, now, dateField);
        } else {
            dateTimes = DateUtil.rangeToList(now, dateTime, dateField);
        }

        ArrayList<String> results = CollUtil.newArrayList();
        // 时间格式化类型
        String datePatternString;
        if (dateField.equals(DateField.DAY_OF_YEAR)) {
            // 年月日 yyyy-MM-dd
            datePatternString = DatePattern.NORM_DATE_PATTERN;
        } else {
            // 年月 yyyy-MM
            datePatternString = DatePattern.NORM_MONTH_PATTERN;
        }

        dateTimes.forEach(dateTime1 -> {
            String format = DateUtil.format(dateTime1, datePatternString);
            results.add(format);
        });

        return results;
    }
}
