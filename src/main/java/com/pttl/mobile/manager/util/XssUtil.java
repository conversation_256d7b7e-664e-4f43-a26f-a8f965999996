package com.pttl.mobile.manager.util;

import org.apache.commons.lang3.StringEscapeUtils;
import org.jsoup.Jsoup;
import org.jsoup.safety.Whitelist;

public class XssUtil {

	private final static Whitelist user_content_filter = Whitelist.basic();

	static {
		user_content_filter.addTags("embed", "object", "td", "param", "span", "div", "p", "strong", "b", "font", "img", "tr", "li", "th", "ul", "br", "h1", "h2", "h3", "h4", "h5", "h6", "ol", "table",
				"tbody");
		user_content_filter.addAttributes(":all", "style", "type", "class", "id", "name", "color", "src", "width", "height");
		user_content_filter.addAttributes("object", "width", "height", "classid", "codebase", "alert");
		user_content_filter.addAttributes("param", "name", "value");
		user_content_filter.addAttributes("embed", "src", "quality", "width", "height", "allowFullScreen", "allowScriptAccess", "flashvars", "name", "type", "pluginspage");
		user_content_filter.addAttributes("a", "href");
	}

	/**
	 * Xss敏感字符过滤
	 * 
	 * @param value
	 * @return
	 */
	public final static String filterScript(String value) {
		if (value == null) {
			return null;
		}
		value = Jsoup.clean(value, user_content_filter);
		return StringEscapeUtils.unescapeHtml4(value);//html特殊字符转义
	}
}