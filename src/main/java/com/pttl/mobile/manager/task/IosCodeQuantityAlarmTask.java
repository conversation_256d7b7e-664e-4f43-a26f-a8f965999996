package com.pttl.mobile.manager.task;

import com.pttl.mobile.manager.service.IosCodeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * ios code 数量告警 task
 *
 * <AUTHOR>
 * @date 2024/11/28
 **/
@Slf4j
@Component
@ConditionalOnProperty(prefix = "schedule.ios-code", name = "enabled", havingValue = "true")
public class IosCodeQuantityAlarmTask {

    /**
     * 短信网关地址
     */
    @Value("${schedule.ios-code.sms-gateway}")
    private String smsGateway;

    /**
     * ios code服务 层
     */
    @Resource
    private IosCodeService iosCodeService;
    @Scheduled(cron = "${schedule.ios-code.cron}")
    public void processSendWarningSMSMessages() {
        log.info("start processSendWarningSMSMessages ...");

        iosCodeService.checkIosCodeQuantitySendWarningSMSMessages(smsGateway);

        log.info("processSendWarningSMSMessages success ...");
    }
}
