package com.pttl.mobile.manager.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

import static com.pttl.mobile.manager.config.ApplicationDefinedConfiguration.CONFIGURE_PREFIX;

/**
 * 系统参数配置
 *
 * <AUTHOR>
 * @date 2022/11/7
 **/
@Component
@Data
@ConfigurationProperties(prefix = CONFIGURE_PREFIX)
public class ApplicationDefinedConfiguration {
    public static final String CONFIGURE_PREFIX = "application-defined";

    /**
     * 系统配置
     */
    private SystemManager systemManager;

    /**
     * jwt相关配置 移动端token
     */
    private MobileToken mobileToken;

    /**
     * ios-qrcode 二维码有效时间
     */
    private IosCode iosCode;

    /**
     * ihr web-service 通讯录接口参数
     */
    private IhrAddressList ihrAddressList;

    /**
     * 移动通讯录配置参数
     */
    private MobileAddressBook mobileAddressBook;

    /**
     * 第三方bpm登陆 cookie获取 相关配置参数
     */
    private BpmCookieClient bpmCookieClient;

    /**
     * 第三方vcrm登陆 cookie获取 相关配置参数
     */
    private VcrmCookieClient vcrmCookieClient;

    /**
     * 移动端 第三方 通知相关配置参数
     */
    private InformCookieClient informCookieClient;

    /**
     * 移动端 在途挂单 相关配置参数
     */
    private InTransitOrder inTransitOrder;


    @Data
    public static class SystemManager {
        /**
         * 管理端登陆开关
         */
        private Boolean loginSwitch;

        /**
         * 管理端登陆验证码开关
         */
        private Boolean loginSmsSwitch;

        /**
         * 应用图标访问地址前缀
         */
        private String applicationIconUrlPrefix;
    }

    @Data
    public static class MobileToken {
        private Long tokenExpirationTime;
        private String jwtKey;

    }

    @Data
    public static class IosCode {

        /**
         * ios-qrcode 二维码有效时间
         * 单位/分钟
         */
        private Integer validTime;
    }

    @Data
    public static class MobileAddressBook {

        /**
         * # 组织架构(为了提高效率) 检索索引结果缓存时间,
         * # 如果为空则表示当前时间到当天凌晨,单位/分钟
         * # 需要参考定时任务: organizational-structure
         */
        private Long searchIndexExpireTime;

        /**
         * 头像所在目录 扫描目录
         */
        private List<String> headPictureDirectory;

        /**
         * 头像公网访问地址前缀
         */
        private String headPictureVisitPre;

        /**
         * 移动端 我的 头像公网访问地址前缀
         */
        private String headPictureVisitPreSelf;
    }

    @Data
    public static class IhrAddressList {

        /**
         * 地址前缀  http://ip:port
         */
        private String urlPrefix;

        /**
         * HPS_INF_ID
         */
        private String infoId;

        /**
         * HPS_INF_APP
         */
        private String infoApp;

        /**
         * HPS_INF_NAME
         */
        //private String infoName;

        /**
         * HPS_INF_ACTION
         */
        private String infoAction;


    }

    @Data
    public static class BpmCookieClient {

        /**
         * 密码aes加密密钥
         */
        private String aesKey;
        private String aesIv;

        /**
         * 登陆地址
         */
        private String urlAddress;

        /**
         * 登陆地址鸿蒙
         */
        private String urlAddressHm;

        /**
         * 请求头参数 map
         */
        private Map<String, String> headerMap;

        /**
         * 成功时状态码 默认为200
         */
        private Integer successStatusCode;

        /**
         * 需要获取的响应头 cookie名称
         */
        private String cookieName;

        /**
         * rpa权限检查请求地址
         */
        private String rpaUrlAddress;

        /**
         * 人员项目管理 权限检查请求地址
         */
        private String menuNameUrlAddress;
    }

    @Data
    public static class VcrmCookieClient {

        /**
         * 登陆地址
         */
        private String urlAddress;

        /**
         * 登陆地址鸿蒙
         */
        private String urlAddressHm;

        /**
         * 需要获取的响应头 cookie名称
         */
        private String cookieName;

        /**
         * vcrm账密加密公钥获取地址
         */
        private String vcrmPublicKeyUrl;
    }

    @Data
    public static class InformCookieClient {
        /**
         * 通知登陆地址
         */
        private String informUrlAddress;

        /**
         * 绑定极光推送
         */
        private String auroraUrlAddress;

        /**
         * 获取通知类型
         */
        private String typeUrlAddress;

        /**
         * 获取通知列表地址
         */
        private String listUrlAddress;

        /**
         * 一键已读 地址
         */
        private String onceReadUrlAddress;

        /**
         * 已读 地址
         */
        private String readUrlAddress;

        /**
         * 通知搜索 地址
         */
        private String searchUrlAddress;

        /**
         * BPM红点显示 地址
         */
        private String redDotUrlAddress;

        /**
         * BPM红点显示请求头 X-TLSI-APPKEY属性值
         */
        private String redDotHeaderValue;
    }


    @Data
    public static class InTransitOrder {

        /**
         * 获取VCRM子账号地址
         */
        private String vcrmSubAccountUrlAddress;

        /**
         * 获取中台key地址
         */
        private String middleGroundKeyUrlAddress;

        /**
         * 获取UUID 地址
         */
        private String uuidUrlAddress;

        /**
         * 获取登录token 地址(与上面uuid接口需要间隔两秒)
         */
        private String loginTokenUrlAddress;

    }
}
