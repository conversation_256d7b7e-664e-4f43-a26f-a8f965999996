package com.pttl.mobile.manager.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.LinkedHashMap;

/**
 * ios code码 分类标识map配置
 *
 * <AUTHOR>
 * @date 2023/12/25
 */

@Component
@Data
@ConfigurationProperties(prefix = IosCodeTypeMapConfiguration.CONFIGURE_PREFIX)
public class IosCodeTypeMapConfiguration {
    public static final String CONFIGURE_PREFIX = "map-data";

    /**
     * ios code码类型
     */
    private LinkedHashMap<String, Integer> iosCodeType;

}
