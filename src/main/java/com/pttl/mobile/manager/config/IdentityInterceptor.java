package com.pttl.mobile.manager.config;

import com.auth0.jwt.exceptions.JWTVerificationException;
import com.pttl.mobile.manager.constant.CommonConstants;
import com.pttl.mobile.manager.domain.bo.MobileManagerUser;
import com.pttl.mobile.manager.lib.ResponseMessage;
import com.pttl.mobile.manager.mobile.dto.UserToken;
import com.pttl.mobile.manager.mobile.exception.ErrorMessageException;
import com.pttl.mobile.manager.mobile.util.JwtUtil;
import com.pttl.mobile.manager.mobile.util.RequestContextUtils;
import com.pttl.mobile.manager.util.JsonUtil;
import com.pttl.mobile.manager.util.RSAUtil;
import com.pttl.mobile.manager.util.RedisUtil;
import com.pttl.mobile.manager.util.StringUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.entity.ContentType;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.Enumeration;
import java.util.List;

/**
 * 身份校验中间件
 *
 * <AUTHOR> Cao
 * @date 2022/1/20 18:12
 */
@Component
@Slf4j
@Data
@ConfigurationProperties(prefix = "mobile")
public class IdentityInterceptor implements HandlerInterceptor {
    private static final String HTTP_TYPE_OPTIONS = "OPTIONS";
    private static final String HTTP_TYPE_OPTIONS2 = "options";

    /**
     * 动态配置
     */
    @Resource
    private ApplicationDefinedConfiguration applicationDefinedConfiguration;

    /**
     * jwt工具
     */
    @Resource
    private JwtUtil jwtUtil;

    @Resource
    private RSAUtil rsaUtil;

    @Resource
    private RedisUtil redisUtil;

    private List<String> whitelist;

    private List<String> clientlist;


    private List<String> mobilelist;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        String requestMethod = request.getMethod();
        if (requestMethod.contains(HTTP_TYPE_OPTIONS) || requestMethod.contains(HTTP_TYPE_OPTIONS2)) {
            return true;
        }

        if (response.getStatus() == 404) {
            sendResponse(response, 404, "The requested resource does not exist");
            return false;
        }

//        response.setHeader("Access-Control-Allow-Origin", "*");
        response.setHeader("Access-Control-Allow-Headers", "Origin, X-Requested-With, Content-Type, Accept");
        response.setHeader("Access-Control-Allow-Credentials", "true");

        String requestUri = request.getRequestURI();
        if (clientlist.contains(requestUri) || CommonConstants.MOBILE_USER_LOGIN_URL.contains(requestUri)
                || checkTokenHeader(request)) {

            // 校验移动端用户登录
            return verifyMobileUserLogin(request, response, requestUri);
        } else {
            // 管理端 免登陆模式
            Boolean loginSwitch = applicationDefinedConfiguration.getSystemManager().getLoginSwitch();
            if (Boolean.FALSE.equals(loginSwitch)) {
                return true;
            }

            if (!whitelist.contains(requestUri) && !MobileManagerUser.isLoggedIn()) {
                sendResponse(response, 401, "Please log in first");
                return false;
            }

            return true;
        }
    }


    /**
     * 校验移动端用户登录
     *
     * @param httpRequest
     * @param httpResponse
     * @param requestUri
     * @return
     * @throws IOException
     */
    private boolean verifyMobileUserLogin(HttpServletRequest httpRequest, HttpServletResponse httpResponse,
                                          String requestUri) throws IOException {
        // 登录
        if (requestUri.equalsIgnoreCase(CommonConstants.MOBILE_USER_LOGIN_URL)) {
            return true;
        }

        String mobileToken = httpRequest.getHeader(CommonConstants.MOBILE_USER_LOGIN_HEADER_NAME);
        // 校验token
        if (StringUtil.isEmpty(mobileToken)) {
            sendResponse(httpResponse, 401, "mobile token cannot be empty");
            return false;
        }

        try {
            // 校验token是否合法
            UserToken userToken = jwtUtil.verify(mobileToken);
            // 缓存用户信息
            RequestContextUtils.setUserToken(userToken);
        } catch (JWTVerificationException verificationException) {
            // token 非法
            sendResponse(httpResponse, 401, "mobile token illegal");
            return false;
        } catch (ErrorMessageException errorMessageException) {
            // token 无效
            sendResponse(httpResponse, 401, errorMessageException.getMessage());
            return false;
        }

        return true;
    }

    /**
     * 检查是否存在请求头
     *
     * @param request
     * @return
     */
    private boolean checkTokenHeader(HttpServletRequest request) {
        try {
            Enumeration<String> headerNames = request.getHeaderNames();
            while (headerNames.hasMoreElements()) {
                String name = headerNames.nextElement();
                if (name.equals(CommonConstants.MOBILE_USER_LOGIN_HEADER_NAME)) {
                    return true;
                }
            }

        } catch (Exception ex) {
            return false;
        }
        return false;
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) throws Exception {
        // not do
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        // 清空本地缓存
        RequestContextUtils.clear();
    }

    private void sendResponse(HttpServletResponse httpResponse, int status, String message) throws IOException {
        httpResponse.setStatus(status);
        httpResponse.setCharacterEncoding("UTF-8");
        httpResponse.setContentType(ContentType.APPLICATION_JSON.toString());
        PrintWriter out = httpResponse.getWriter();
        ResponseMessage<String> res = new ResponseMessage<>();
        res.setCode(status);
        res.setMessage(message);
        out.append(JsonUtil.toJson(res));
    }
}
