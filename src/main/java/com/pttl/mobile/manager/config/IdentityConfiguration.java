package com.pttl.mobile.manager.config;

import cn.hutool.core.util.StrUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.format.FormatterRegistry;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.validation.MessageCodesResolver;
import org.springframework.validation.Validator;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.HandlerMethodReturnValueHandler;
import org.springframework.web.servlet.HandlerExceptionResolver;
import org.springframework.web.servlet.config.annotation.*;
import org.springframework.web.servlet.handler.MappedInterceptor;

import java.util.List;

/**
 * <AUTHOR>
 * @description: 身份校验拦截器
 * @date 2022/1/20 16:24
 */
@Configuration
public class IdentityConfiguration implements WebMvcConfigurer {
    @Autowired
    private IdentityInterceptor loginInterceptor;

    @Value("${cors-filter.allowedOrigins}")
    private String allowedOrigins;

    @Bean
    public MappedInterceptor getMappedInterceptor() {
        //拦截路径 ("/**")对所有请求都拦截
        String[] includePatterns = new String[]{"/**"};
        //排除拦截路径
        String[] excludePatterns = new String[]{
                "/swagger-resources/**",
                "/webjars/**",
                "/v2/**",
                "/swagger-ui.html/**",
                "/api",
                "/api-docs",
                "/api-docs/**",
                "/health",
                // 获取ios-code二维码路径 不做拦截
                "/ios/code/getQrCode/**",
                "/ios/code/getQC/**/**",
                "/user/getSmsVerificationCode",
                "/user/getCaptcha",
                "/mobile/tp/**",
                "/mobile/inTransit/order/**",
                "/mobile/record/save"
        };

        return new MappedInterceptor(includePatterns, excludePatterns, loginInterceptor);
    }

    @Override
    public void configurePathMatch(PathMatchConfigurer pathMatchConfigurer) {

    }

    @Override
    public void configureContentNegotiation(ContentNegotiationConfigurer contentNegotiationConfigurer) {

    }

    @Override
    public void configureAsyncSupport(AsyncSupportConfigurer asyncSupportConfigurer) {

    }

    @Override
    public void configureDefaultServletHandling(DefaultServletHandlerConfigurer defaultServletHandlerConfigurer) {

    }

    @Override
    public void addFormatters(FormatterRegistry formatterRegistry) {

    }

    @Override
    public void addInterceptors(InterceptorRegistry interceptorRegistry) {

    }

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry resourceHandlerRegistry) {

    }

    @Override
    public void addCorsMappings(CorsRegistry corsRegistry) {
        String allowedHeaders = "x-auth-token,content-type,X-Requested-With,XMLHttpRequest";
        String exposedHeaders = "x-auth-token,content-type,X-Requested-With,XMLHttpRequest";
        String allowedMethods = "POST,GET,DELETE,PUT,OPTIONS,PATCH";
        String allowedOrigins2 = this.allowedOrigins;
        // 不配置则全部放过
        if (StrUtil.isEmpty(this.allowedOrigins)) {
            allowedOrigins2 = "*";
        }

        corsRegistry.addMapping("/**")
                .allowedOrigins(allowedOrigins2.split(","))
                .allowedHeaders(allowedHeaders.split(","))
                .exposedHeaders(exposedHeaders.split(","))
                .allowedMethods(allowedMethods.split(","));
    }

    @Override
    public void addViewControllers(ViewControllerRegistry viewControllerRegistry) {

    }

    @Override
    public void configureViewResolvers(ViewResolverRegistry viewResolverRegistry) {

    }

    @Override
    public void addArgumentResolvers(List<HandlerMethodArgumentResolver> list) {

    }

    @Override
    public void addReturnValueHandlers(List<HandlerMethodReturnValueHandler> list) {

    }

    @Override
    public void configureMessageConverters(List<HttpMessageConverter<?>> list) {

    }

    @Override
    public void extendMessageConverters(List<HttpMessageConverter<?>> list) {

    }

    @Override
    public void configureHandlerExceptionResolvers(List<HandlerExceptionResolver> list) {

    }

    @Override
    public void extendHandlerExceptionResolvers(List<HandlerExceptionResolver> list) {

    }

    @Override
    public Validator getValidator() {
        return null;
    }

    @Override
    public MessageCodesResolver getMessageCodesResolver() {
        return null;
    }
}
