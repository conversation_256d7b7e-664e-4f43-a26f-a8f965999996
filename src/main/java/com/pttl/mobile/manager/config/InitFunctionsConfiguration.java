package com.pttl.mobile.manager.config;

import cn.hutool.core.collection.CollUtil;
import com.pttl.mobile.manager.constant.CommonConstants;
import com.pttl.mobile.manager.dao.Department2Mapper;
import com.pttl.mobile.manager.domain.entity.Department2DO;
import com.pttl.mobile.manager.mobile.ws.service.OrganizationalStructureInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 初始化配置/数据
 *
 * <AUTHOR>
 * @date 2022/10/20
 **/
@Component
@Slf4j
public class InitFunctionsConfiguration {

    /**
     * redis客户端
     */
    @Resource
    private RedisTemplate<String, HashMap<Long, Department2DO>> redisTemplate;

    /**
     * 部门服务
     */
    @Resource
    private Department2Mapper department2Mapper;

    /**
     * 组织架构
     */
    @Resource
    private OrganizationalStructureInfoService organizationalStructureInfoService;

    /**
     * 初始化部门信息
     * 数据结构为map:
     * key:部门id,
     * value: 部门名称
     */
    @PostConstruct
    public void initFullDepartmentNameMap() {
        log.info("start initializing department information ...");

        List<Department2DO> listAll = department2Mapper.listAll();
        // 没有数据则返回空
        if (CollUtil.isNotEmpty(listAll)) {
            // 初始化
            Map<Long, Department2DO> fullDepartmentNameMap = listAll.stream().collect(Collectors
                    .toMap(Department2DO::getId, department2DO -> department2DO));
            redisTemplate.opsForHash().putAll(CommonConstants.DEPARTMENT_NAME_MAP_NAME, fullDepartmentNameMap);
        }

        log.info("initializing department information completed. total: {}", listAll.size());
    }

}
