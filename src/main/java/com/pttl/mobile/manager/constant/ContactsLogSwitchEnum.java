package com.pttl.mobile.manager.constant;

/**
 * 通讯录日志开关枚举类
 *
 * <AUTHOR>
 * @date 2024/5/7
 */
public enum ContactsLogSwitchEnum {
    YES(1, "YES","打开"),
    NO(0, "NO","关闭");

    ;
    private int code;
    private String flag;

    private String description;

    ContactsLogSwitchEnum(int code, String flag, String description) {
        this.code = code;
        this.flag = flag;
        this.description = description;
    }

    public static ContactsLogSwitchEnum instanceOf(String flag) {
        switch (flag) {
            case "YES":
                return YES;
            case "NO":
                return NO;
            default:
                return null;
        }
    }

    public String getFlag() {
        return flag;
    }

    public int getCode() {
        return code;
    }


    public static int getCodeByFlag(String flag) {
        if ("YES".equals(flag)) {
            return YES.code;
        }
        return NO.code;
    }

}