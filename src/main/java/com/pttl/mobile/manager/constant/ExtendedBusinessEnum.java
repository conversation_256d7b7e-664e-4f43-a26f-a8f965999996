package com.pttl.mobile.manager.constant;

import lombok.Getter;

/**
 * <AUTHOR>
 * @description: 扩展业务枚举类
 * @date 2024/12/20 14:05
 */
@Getter
public enum ExtendedBusinessEnum {
    IOS_VR_SWITCH_ON("ios_vr_switch", "on", "ios-vr开关 开启状态"),
    IOS_VR_SWITCH_OFF("ios_vr_switch", "off", "ios-vr开关 关闭状态");

    private String key;

    private String data;

    private String desc;

    ExtendedBusinessEnum(String key, String data, String desc) {
        this.key = key;
        this.data = data;
        this.desc = desc;
    }
}