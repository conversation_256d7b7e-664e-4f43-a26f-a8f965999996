package com.pttl.mobile.manager.constant;

/**
 * 公用常量定义
 *
 * <AUTHOR>
 * @date 2022/10/14
 **/

public class CommonConstants {

    private CommonConstants() {
    }

    /**
     * 所有部门id与名称的map集合的key
     * 组装成为hashMap结构数据, 用于映射部门全路径时的转义成文字
     */
    public static final String DEPARTMENT_NAME_MAP_NAME = "department:name:map";


    /**
     * 移动端登录缓存key
     */
    public static final String MOBILE_USER_TOKEN_KEY = "mobile:user:token:%s";

    /**
     * 移动端登录
     */
    public static final String MOBILE_USER_LOGIN_URL = "/mobile/login";
    public static final String MOBILE_USER_LOGIN_HEADER_NAME = "token";


    /**
     * 用户初始化明文密码
     * 存储时不使用明文存储
     * 策略如下:
     * 1.后台在初始化时, 直接入库时使用md5("123456")后的hash串存储
     * 2.前端修改密码时, 逻辑为, 前端使用RSA(公钥加密)加密算法加密后, 后端获取到, 使用RSA(私钥解密)后,
     * 再使用md5("明文密码")后的hash串,作为入库密码
     */
    public static final String SUER_INIT_PASSWORD = "123456";


    /**
     * 错误信息
     */
    public static final String ERROR_MESSAGE_PARAMETER_IS_NULL = "参数不能为空";
    public static final String ERROR_MESSAGE_PARSING_FAILED = "适配包解析失败";
    public static final String ERROR_MESSAGE_DATA_NOT_EXIST = "数据不存在";
    public static final String ERROR_MESSAGE_DATA_ALREADY_EXIST = "数据已存在";
    public static final String ERROR_MESSAGE_DATA_ALREADY_EXIST_ID = "索引已存在";
    public static final String ERROR_MESSAGE_CHECK_PASS = "检验通过";


    /**
     * ios-code 获取二维码 用户id key
     */
    public static final String QRCODE_USER_ID_KEY = "qrcode:user:id:%s";


    /**
     * 图形验证码key前缀
     */
    public static final String CAPTCHA_CODE_PREFIX = "captcha:code:%s";

    /**
     * 短信验证码前缀 %s 验证码
     */
    public static final String SMS_CODE_PREFIX = "sms:code:%s";

    /**
     * 统计的类型: 1-近一天 2-近一周 3-近一个与 4-近三个月
     */
    public static final String DAY_TYPE_ONE_DAY = "1";
    public static final String DAY_TYPE_ONE_WEEK = "2";
    public static final String DAY_TYPE_ONE_MOUTH = "3";
    public static final String DAY_TYPE_THREE_MOUTH = "4";

    /**
     * 客户端类型
     */
    public static final String SOURCE_TYPE_ANDROID = "android";

    /**
     * 工号前缀
     */
    public static final String JOB_NUMBER_PREFIX = "TL";
    public static final String JOB_NUMBER_PREFIX2 = "tl";


    public static final String COOKIE_NAME = "Cookie";

    /**
     * json字段名称
     */
    public static final String JSON_MODULE_NAME = "moduleName";
    public static final String JSON_PAGE_LOAD_TIME = "pageLoadTime";
    public static final String JSON_PAGE_LOG = "pageLog";
    public static final String JSON_APIS = "Apis";
    public static final String JSON_LISTS = "Lists";
    public static final String JSON_TOTAL_TIME = "totalTIme";
}
