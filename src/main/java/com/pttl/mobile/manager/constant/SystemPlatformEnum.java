package com.pttl.mobile.manager.constant;

/**
 * <AUTHOR>
 * @description: 平台枚举类
 * @date 2022/1/20 16:24
 */
public enum SystemPlatformEnum {
    ONLINEOFFICE(1, "在线办公"),
    MALL(2, "太力商城");

    private int systemPlatform;

    private String name;

    SystemPlatformEnum(int systemPlatform, String name) {
        this.systemPlatform = systemPlatform;
        this.name = name;
    }

    public int getSystemPlatform() {
        return systemPlatform;
    }

    public static SystemPlatformEnum instanceOf(String val) {
        for (SystemPlatformEnum v : SystemPlatformEnum.values()) {
            if (v.name.equals(val)) {
                return v;
            }
        }

        return null;
    }
}
