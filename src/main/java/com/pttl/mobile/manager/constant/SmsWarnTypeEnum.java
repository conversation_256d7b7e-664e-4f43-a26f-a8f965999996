package com.pttl.mobile.manager.constant;

import lombok.Getter;

/**
 * 短信警告类型枚举
 *
 * <AUTHOR>
 * @date 2024/11/25
 */
@Getter
public enum SmsWarnTypeEnum {
    IOS_CODE("ios-code", "IOS兑换码警告"),
    UNKNOWN("unknown", "未知告警类型");

    private String type;

    private String name;

    SmsWarnTypeEnum(String type, String name) {
        this.type = type;
        this.name = name;
    }

    public static SmsWarnTypeEnum getEnumByType(String type) {
        for (SmsWarnTypeEnum value : SmsWarnTypeEnum.values()) {
            if (value.getType().equals(type)) {
                return value;
            }
        }
        return UNKNOWN;
    }
}