package com.pttl.mobile.manager.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

import lombok.Data;

/**
 * 策略组与用户关联表
 *
 * <AUTHOR>
 */
@ApiModel(value = "策略组与用户关联表")
@Data
public class StrategyUser2DO implements Serializable {
    @ApiModelProperty(value = "主键")
    private Integer id;

    @ApiModelProperty(value = "策略id")
    private Long strategyId;

    @ApiModelProperty(value = "用户id")
    private Long userId;

    private static final long serialVersionUID = 1L;
}