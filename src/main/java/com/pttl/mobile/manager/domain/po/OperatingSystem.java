package com.pttl.mobile.manager.domain.po;

import java.io.Serializable;

public class OperatingSystem implements Serializable {
    /**
     * IOS：1； Android：2
     */
    private Boolean id;

    /**
     * IOS或者Android或者其他...
     */
    private String name;

    /**
     * operating_system
     */
    private static final long serialVersionUID = 1L;

    /**
     * IOS：1； Android：2
     * @return id IOS：1； Android：2
     */
    public Boolean getId() {
        return id;
    }

    /**
     * IOS：1； Android：2
     * @param id IOS：1； Android：2
     */
    public void setId(Boolean id) {
        this.id = id;
    }

    /**
     * IOS或者Android或者其他...
     * @return name IOS或者Android或者其他...
     */
    public String getName() {
        return name;
    }

    /**
     * IOS或者Android或者其他...
     * @param name IOS或者Android或者其他...
     */
    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }
}