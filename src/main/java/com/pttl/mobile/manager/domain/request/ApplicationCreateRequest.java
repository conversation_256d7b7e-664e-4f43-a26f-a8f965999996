package com.pttl.mobile.manager.domain.request;

import com.pttl.mobile.manager.domain.dto.ManifestDTO;
import com.pttl.mobile.manager.util.XssUtil;
import lombok.Data;

import java.io.Serializable;

@Data
public class ApplicationCreateRequest implements Serializable {
    private String id;

    private String name;

    public String getName() {
        return XssUtil.filterScript(name);
    }

    private String description;

    public String getDescription() {
        return XssUtil.filterScript(description);
    }

    private String address;

    private String innerAddress;

    private String logoUrl;

    private String packageName;

    private String packageUrl;

    private Integer type;

    private Boolean isVisible = false;

    private String version;

    private String applicationGroupId;

    private ManifestDTO manifest;

    private Boolean isManifestChange = false;

    private Boolean isLogoChange = false;
}
