package com.pttl.mobile.manager.domain.po;

import java.io.Serializable;
import java.util.Date;

public class Release implements Serializable {
    /**
     * 发布版本id
     */
    private String id;

    /**
     * 发布的版本
     */
    private String version;

    /**
     * 下载地址
     */
    private String downloadUrl;

    /**
     * 外键 发版的系统平台
     */
    private Integer systemPlatform;

    /**
     * 外键 更新的类型 客户端或者适配包
     */
    private Integer type;

    /**
     * 外键 操作系统 Android或者IOS
     */
    private Integer operatingSystem;

    /**
     * 是否强制更新
     */
    private Boolean isForced;

    /**
     * 发布版本的日期
     */
    private Date releaseDate;

    /**
     * 更新内容
     */
    private String description;

    /**
     * 环境标识 1:正式环境  2:预生产环境 用户发版测试
     */
    private Integer env;

    /**
     * release
     */
    private static final long serialVersionUID = 1L;

    /**
     * 发布版本id
     * @return id 发布版本id
     */
    public String getId() {
        return id;
    }

    /**
     * 发布版本id
     * @param id 发布版本id
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * 发布的版本
     * @return version 发布的版本
     */
    public String getVersion() {
        return version;
    }

    /**
     * 发布的版本
     * @param version 发布的版本
     */
    public void setVersion(String version) {
        this.version = version == null ? null : version.trim();
    }

    /**
     * 下载地址
     * @return download_url 下载地址
     */
    public String getDownloadUrl() {
        return downloadUrl;
    }

    /**
     * 下载地址
     * @param downloadUrl 下载地址
     */
    public void setDownloadUrl(String downloadUrl) {
        this.downloadUrl = downloadUrl == null ? null : downloadUrl.trim();
    }

    /**
     * 外键 发版的系统平台
     * @return system_platform 外键 发版的系统平台
     */
    public Integer getSystemPlatform() {
        return systemPlatform;
    }

    /**
     * 外键 发版的系统平台
     * @param systemPlatform 外键 发版的系统平台
     */
    public void setSystemPlatform(Integer systemPlatform) {
        this.systemPlatform = systemPlatform;
    }

    /**
     * 外键 更新的类型 客户端或者适配包
     * @return type 外键 更新的类型 客户端或者适配包
     */
    public Integer getType() {
        return type;
    }

    /**
     * 外键 更新的类型 客户端或者适配包
     * @param type 外键 更新的类型 客户端或者适配包
     */
    public void setType(Integer type) {
        this.type = type;
    }

    /**
     * 外键 操作系统 Android或者IOS
     * @return operating_system 外键 操作系统 Android或者IOS
     */
    public Integer getOperatingSystem() {
        return operatingSystem;
    }

    /**
     * 外键 操作系统 Android或者IOS
     * @param operatingSystem 外键 操作系统 Android或者IOS
     */
    public void setOperatingSystem(Integer operatingSystem) {
        this.operatingSystem = operatingSystem;
    }

    /**
     * 是否强制更新
     * @return is_forced 是否强制更新
     */
    public Boolean getIsForced() {
        return isForced;
    }

    /**
     * 是否强制更新
     * @param isForced 是否强制更新
     */
    public void setIsForced(Boolean isForced) {
        this.isForced = isForced;
    }

    /**
     * 发布版本的日期
     * @return release_date 发布版本的日期
     */
    public Date getReleaseDate() {
        return releaseDate;
    }

    /**
     * 发布版本的日期
     * @param releaseDate 发布版本的日期
     */
    public void setReleaseDate(Date releaseDate) {
        this.releaseDate = releaseDate;
    }

    /**
     * 更新内容
     * @return description 更新内容
     */
    public String getDescription() {
        return description;
    }

    /**
     * 更新内容
     * @param description 更新内容
     */
    public void setDescription(String description) {
        this.description = description == null ? null : description.trim();
    }

    /**
     * 环境标识 1:正式环境  2:预生产环境 用户发版测试
     * @return env 环境标识 1:正式环境  2:预生产环境 用户发版测试
     */
    public Integer getEnv() {
        return env;
    }

    /**
     * 环境标识 1:正式环境  2:预生产环境 用户发版测试
     * @param env 环境标识 1:正式环境  2:预生产环境 用户发版测试
     */
    public void setEnv(Integer env) {
        this.env = env;
    }
}