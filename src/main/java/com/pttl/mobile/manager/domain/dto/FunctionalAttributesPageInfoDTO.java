package com.pttl.mobile.manager.domain.dto;

import com.pttl.mobile.manager.domain.entity.FunctionalAttributesDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 属性配置分页dto
 *
 * <AUTHOR>
 * @date 2022/10/25
 **/
@Data
@EqualsAndHashCode
@ApiModel(value = "查询结果vo")
public class FunctionalAttributesPageInfoDTO {

    /**
     * 主键
     */
    @ApiModelProperty(value = "数据id")
    private Long id;

    /**
     * 操作系统 1-Android 2-IOS 3-Android&IOS
     */
    @ApiModelProperty(value = "操作系统 1-Android  2-IOS 3-Android&IOS")
    private Integer operatingSystem;

    /**
     * 属于哪种数据类型: 1:环境参数 2:Webview拦截系统加载地址 3:系统应用描述（/）区分 ; 后续可自己定义
     *
     * @see FunctionalAttributesDO#DATA_TYPE_ENVIRONMENTAL_PARAMETERS
     */
    @ApiModelProperty(value = "属于哪种数据类型: 1:环境参数 2:Webview拦截系统加载地址 3:系统应用描述（/）区分 ; 后续可自己定义")
    private Integer dataType;

    /**
     * 功能属性, 例:消息推送：http://mpttl.com
     */
    @ApiModelProperty(value = "功能属性id")
    private String functionalAttributes;

    /**
     * 属性值
     */
    @ApiModelProperty(value = "属性值")
    private String attributeValue;

    /**
     * 功能属性描述, 例:消息推送：http://mpttl.com
     */
    @ApiModelProperty(value = "功能属性描述, 例:消息推送：http://mpttl.com")
    private String remark;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
}
