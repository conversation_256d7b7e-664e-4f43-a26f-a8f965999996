package com.pttl.mobile.manager.domain.dto;

import com.pttl.mobile.manager.domain.entity.ManagerOperationLogDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * 管理端 操作日志表
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "管理端 操作日志分页结果")
@EqualsAndHashCode()
public class ManagerOperationLogPageResultDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 当前操作成功与否状态, 0: 异常, 1:成功
     *
     * @see ManagerOperationLogDO#OPERATION_STATUS_SUCCESS
     */
    @ApiModelProperty(value = "当前操作成功与否状态, 0: 异常, 1:成功")
    private Integer operationStatus;

    /**
     * 功能模块
     */
    @ApiModelProperty(value = "功能模块")
    private String operationModule;


    /**
     * 操作类型
     */
    @ApiModelProperty(value = "操作类型")
    private String operationType;

    /**
     * 操作描述
     */
    @ApiModelProperty(value = "操作描述")
    private String operationDescription;

    /**
     * 操作员名称
     */
    @ApiModelProperty(value = "操作员名称")
    private String operationUserName;

    /**
     * 请求ip
     */
    @ApiModelProperty(value = "请求ip")
    private String operationIp;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
}