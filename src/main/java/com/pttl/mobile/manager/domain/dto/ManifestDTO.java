package com.pttl.mobile.manager.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class ManifestDTO implements Serializable {
    private Object adapter_urls;

    private String default_url;

    private String name;

    private String description;

    private Object options;

    private Object override_urls;

    private String runtime_env;

    private String runtime_version;

    private String scope;

    private String short_name;

    private String start_url;

    private String type;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date update_time;

    private String version;
}

