package com.pttl.mobile.manager.domain.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode
@Data
@ApiModel(value = "部门更新请求参数")
public class DepartmentUpdateRequest implements Serializable {

    private static final long serialVersionUID = -4746601269267992289L;

    /**
     * 部门ID
     */
    @ApiModelProperty(value = "部门ID")
    private Long id;

    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称")
    private String name;

    /**
     * 部门描述
     */
    @ApiModelProperty(value = "部门描述")
    private String description;
}
