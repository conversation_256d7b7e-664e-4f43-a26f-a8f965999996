package com.pttl.mobile.manager.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class ApplicationAdapterClientDTO implements Serializable {
    private String applicationId;

    @JsonIgnore
    private String manifestStr;

    private ManifestDTO manifest;

    private String runtimePrimaryVersion;

    private String runtimeVersion;

    private Boolean isSpecificRuntimeUsed = false;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastUpdate;
}
