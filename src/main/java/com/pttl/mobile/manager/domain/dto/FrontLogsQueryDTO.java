package com.pttl.mobile.manager.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.pttl.mobile.manager.domain.request.BasePageRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 前端日志查询
 *
 * <AUTHOR>
 * @date 2024/6/6
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class FrontLogsQueryDTO extends BasePageRequest {

    /**
     * 需要查询的数据类型
     * 1 前端日志, 2 移动端日志
     */
    private Integer datatype = 1;

    /**
     * 系统类型
     */
    private String serverName;

    /**
     * 系统类型
     */
    private String systemType;

    /**
     * 工号
     */
    private String ihrName;

    private String userName;

    /**
     * 登陆时间开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startLoginTime;

    /**
     * 登陆时间结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endLoginTime;

    /**
     * 日志状态
     */
    private String status;

    private Long startLoginTimeLong;
    private Long endLoginTimeLong;
}
