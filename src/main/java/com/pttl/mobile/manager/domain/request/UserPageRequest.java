package com.pttl.mobile.manager.domain.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "用户分页查询vo")
public class UserPageRequest extends BasePageRequest implements Serializable {


    private static final long serialVersionUID = 5730093112545929374L;

    /**
     * 用户姓名
     */
    @ApiModelProperty(value = "用户姓名")
    private String name;

    /**
     * 部门ID
     */
    @ApiModelProperty(value = "部门ID")
    private Long departmentId;

}
