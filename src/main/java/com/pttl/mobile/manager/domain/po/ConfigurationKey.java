package com.pttl.mobile.manager.domain.po;

import java.io.Serializable;

public class ConfigurationKey implements Serializable {
    /**
     * 配置名称 Key
     */
    private String name;

    /**
     * 外键 当前数据生效的系统平台
     */
    private Integer systemPlatform;

    /**
     * 环境标识 1:正式环境  2:预生产环境 用户发版测试
     */
    private Integer env;

    /**
     * configuration
     */
    private static final long serialVersionUID = 1L;

    /**
     * 配置名称 Key
     * @return name 配置名称 Key
     */
    public String getName() {
        return name;
    }

    /**
     * 配置名称 Key
     * @param name 配置名称 Key
     */
    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    /**
     * 外键 当前数据生效的系统平台
     * @return system_platform 外键 当前数据生效的系统平台
     */
    public Integer getSystemPlatform() {
        return systemPlatform;
    }

    /**
     * 外键 当前数据生效的系统平台
     * @param systemPlatform 外键 当前数据生效的系统平台
     */
    public void setSystemPlatform(Integer systemPlatform) {
        this.systemPlatform = systemPlatform;
    }

    /**
     * 环境标识 1:正式环境  2:预生产环境 用户发版测试
     * @return env 环境标识 1:正式环境  2:预生产环境 用户发版测试
     */
    public Integer getEnv() {
        return env;
    }

    /**
     * 环境标识 1:正式环境  2:预生产环境 用户发版测试
     * @param env 环境标识 1:正式环境  2:预生产环境 用户发版测试
     */
    public void setEnv(Integer env) {
        this.env = env;
    }
}