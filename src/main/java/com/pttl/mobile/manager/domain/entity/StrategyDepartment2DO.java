package com.pttl.mobile.manager.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

import lombok.Data;

/**
 * 策略与部门关联表
 *
 * <AUTHOR>
 */
@ApiModel(value = "策略与部门关联表")
@Data
public class StrategyDepartment2DO implements Serializable {
    @ApiModelProperty(value = "主键")
    private Integer id;

    /**
     * 策略id
     */
    @ApiModelProperty(value = "策略id")
    private Long strategyId;

    /**
     * 部门id
     */
    @ApiModelProperty(value = "部门id")
    private Long departmentId;

    private static final long serialVersionUID = 1L;
}