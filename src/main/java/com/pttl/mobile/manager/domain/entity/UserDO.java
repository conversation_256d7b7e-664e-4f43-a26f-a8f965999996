package com.pttl.mobile.manager.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;

/**
 * 用户表
 */
@ApiModel(value = "用户信息实体")
@Data
public class UserDO implements Serializable {

    /**
     * 密码默认修改次数
     */
    public static final int MODIFY_PASSWORD_WRONG_COUNT_DEFAULT = 0;

    /**
     * 数据来源(1: 手动创建; 2: 批量导入; 3: 定制导入; 4: AD导入)
     */
    public static final int SOURCE_TYPE_MANUAL_CONTROL = 1;
    public static final int SOURCE_TYPE_BATCH_IMPORT = 2;
    public static final int SOURCE_TYPE_CUSTOM_IMPORT = 3;

    /**
     * 是否删除(0:未删除; 1: 已删除, 默认0:未删除)
     */
    public static final int VALID_STATUS_VALID = 0;
    public static final int VALID_STATUS_DEL = 1;

    /**
     * 账号激活状态 0 未激活 默认 ; 1 已激活
     */
    public static final int ACTIVATED_STATUS_INACTIVATED = 0;
    public static final int ACTIVATED_STATUS_ACTIVATED = 1;

    /**
     * 禁用状态 0 未禁用 默认 ; 1 已禁用
     */
    public static final int DISABLED_STATUS_NOT_DISABLED = 0;
    public static final int DISABLED_STATUS_DISABLED = 1;

    /**
     * 失败认证次数 默认0
     */
    public static final int FAILED_LOGIN_TIMES_DEFAULT = 0;

    /**
     * 部门内用户最大设备数 默认为3
     */
    public static final int USER_DEVICE_NUM_3 = 3;

    /**
     * 是否开启最大设备限制数 1 开启 0未开启; 默认1 开启
     */
    public static final int DEVICE_SWITCH_STATUS_ON = 1;
    public static final int DEVICE_SWITCH_STATUS_OFF = 0;

    /**
     * 用户临时设备数 默认为0
     */
    public static final int TEMP_DEVICE_NUM_DEFAULT = 0;


    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID")
    private Long id;

    /**
     * 用户所属部门id
     */
    @ApiModelProperty(value = "用户所属部门id")
    private Long departmentId;

    /**
     * 用户姓名
     */
    @ApiModelProperty(value = "用户姓名")
    private String name;

    /**
     * 用户密码
     */
    @ApiModelProperty(value = "用户密码")
    private String password;

    /**
     * 用户email
     */
    @ApiModelProperty(value = "用户email")
    private String email;

    /**
     * 手机
     */
    @ApiModelProperty(value = "手机")
    private String mobile;

    /**
     * 用户登录账户
     */
    @ApiModelProperty(value = "用户登录账户")
    private String loginName;

    /**
     * 岗位名称
     */
    @ApiModelProperty(value = "岗位名称")
    private String positionName;

    /**
     * 密码修改错误次数
     *
     * @see UserDO#MODIFY_PASSWORD_WRONG_COUNT_DEFAULT
     */
    @ApiModelProperty(value = "密码修改错误次数")
    private Integer modifyPasswordWrongCount;

    /**
     * 密码修改错误时间
     */
    @ApiModelProperty(value = "密码修改错误时间")
    private Date modifyPasswordWrongTime;

    /**
     * 头像路径
     */
    @ApiModelProperty(value = "头像路径")
    private String avatarPath;

    /**
     * 员工工号
     */
    @ApiModelProperty(value = "员工工号")
    private String employeeNo;

    /**
     * 人员级别
     */
    @ApiModelProperty(value = "人员级别")
    private String personLevel;

    /**
     * 工作地点
     */
    @ApiModelProperty(value = "工作地点")
    private String locality;

    /**
     * 办公电话
     */
    @ApiModelProperty(value = "办公电话")
    private String telephone;

    /**
     * 数据来源(1: 手动创建; 2: 批量导入; 3: 定制导入; 4: AD导入)
     *
     * @see com.pttl.mobile.manager.domain.entity.UserDO#SOURCE_TYPE_MANUAL_CONTROL
     */
    @ApiModelProperty(value = "数据来源(1: 手动创建; 2: 批量导入; 3: 定制导入; 4: AD导入)")
    private Integer source;

    /**
     * 是否删除(0:未删除; 1: 已删除, 默认0:未删除)
     *
     * @see UserDO#VALID_STATUS_VALID
     */
    @ApiModelProperty(value = "是否删除(0:未删除; 1: 已删除, 默认0:未删除)")
    private Integer validStatus;

    /**
     * 是否已经激活(0:未激活, 1:已激活)
     *
     * @see UserDO#ACTIVATED_STATUS_INACTIVATED
     */
    @ApiModelProperty(value = "是否已经激活(0:未激活, 1:已激活)")
    private Integer activatedStatus;

    /**
     * 激活时间
     */
    @ApiModelProperty(value = "激活时间")
    private Date activatedTime;

    /**
     * 是否被禁用(0:未禁用,1:已禁用)
     *
     * @see UserDO#DISABLED_STATUS_NOT_DISABLED
     */
    @ApiModelProperty(value = "是否被禁用(0:未禁用,1:已禁用)")
    private Integer disabledStatus;

    /**
     * 最近登录时间
     */
    @ApiModelProperty(value = "最近登录时间")
    private Date lastLoginTime;

    /**
     * 失败认证次数
     */
    @ApiModelProperty(value = "失败认证次数")
    private Integer failedLoginTimes;

    /**
     * 部门内用户最大设备数, (暂且保留)
     */
    @ApiModelProperty(value = "部门内用户最大设备数, (暂且保留)")
    private Integer deviceNum;

    /**
     * 是否开启最大设备限制数, (暂且保留)
     */
    @ApiModelProperty(value = "是否开启最大设备限制数, (暂且保留)")
    private Integer deviceSwitchStatus;

    /**
     * 用户临时设备数
     */
    @ApiModelProperty(value = "用户临时设备数")
    private Integer tempDeviceNum;

    /**
     * 用户临时设备数失效时间
     */
    @ApiModelProperty(value = "用户临时设备数失效时间")
    private Date tempDeviceNumExpireTime;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * ad域中的distinguishName属性
     */
    @ApiModelProperty(value = "ad域中的distinguishName属性")
    private String adDn;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 扩展字段1
     */
    @ApiModelProperty(value = "扩展字段1")
    private String extension1;

    /**
     * 扩展字段2
     */
    @ApiModelProperty(value = "扩展字段2")
    private String extension2;

    /**
     * 扩展字段3
     */
    @ApiModelProperty(value = "扩展字段3")
    private String extension3;

    private static final long serialVersionUID = 1L;
}