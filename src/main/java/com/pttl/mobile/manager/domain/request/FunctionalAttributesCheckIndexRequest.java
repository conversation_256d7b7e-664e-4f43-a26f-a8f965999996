package com.pttl.mobile.manager.domain.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 配置索引唯一性校验 dto
 *
 * <AUTHOR>
 * @date 2023/8
 **/
@Data
@EqualsAndHashCode
@ApiModel(value = "配置索引唯一性校验vo")
public class FunctionalAttributesCheckIndexRequest {

    /**
     * 主键
     */
    @ApiModelProperty(value = "数据id")
    private Long id;

    /**
     * 功能属性id
     */
    @ApiModelProperty(value = "功能属性id")
    private String functionalAttributes;
}
