package com.pttl.mobile.manager.domain.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * 移动端业务操作记录日志分页vo
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "移动端业务操作记录日志分页vo")
@Data
public class MobileRecordLogPageRequest extends BasePageRequest implements Serializable {


    /**
     * 年月份 2022_11
     */
    @ApiModelProperty(value = "年月份 例子: 2022_11 ,本月或者上个月")
    private String yearMonth;

    /**
     * 登陆名称
     */
    @ApiModelProperty(value = "登陆名称(支持模糊查询)")
    private String loginName;

    /**
     * 登陆开始时间范围
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "登陆开始时间范围")
    private Date startTime;

    /**
     * 登陆截止时间范围
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "登陆截止时间范围")
    private Date endTime;

    /**
     * 模块
     */
    @ApiModelProperty(value = "模块(不支持模糊查询)")
    private String module;

    /**
     * 工号
     */
    @ApiModelProperty(value = "工号")
    private String employeeId;

    /**
     * 平台 在线商城/在线办公
     */
    @ApiModelProperty(value = "平台 在线商城/在线办公")
    private String platform;

    private static final long serialVersionUID = 1L;
}