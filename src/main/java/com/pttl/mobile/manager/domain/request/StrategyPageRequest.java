package com.pttl.mobile.manager.domain.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "策略分页查询vo")
public class StrategyPageRequest extends BasePageRequest implements Serializable {


    private static final long serialVersionUID = 6991529752086177473L;
    /**
     * 策略id
     */
    @ApiModelProperty(value = "策略id")
    private Long id;

    /**
     * 策略名称
     */
    @ApiModelProperty(value = "策略名称")
    private String name;

}
