package com.pttl.mobile.manager.domain.po;

import java.io.Serializable;

public class Configuration extends Configuration<PERSON><PERSON> implements Serializable {
    /**
     * 配置说明
     */
    private String description;

    /**
     * 配置值
     */
    private String value;

    /**
     * configuration
     */
    private static final long serialVersionUID = 1L;

    /**
     * 配置说明
     * @return description 配置说明
     */
    public String getDescription() {
        return description;
    }

    /**
     * 配置说明
     * @param description 配置说明
     */
    public void setDescription(String description) {
        this.description = description == null ? null : description.trim();
    }

    /**
     * 配置值
     * @return value 配置值
     */
    public String getValue() {
        return value;
    }

    /**
     * 配置值
     * @param value 配置值
     */
    public void setValue(String value) {
        this.value = value == null ? null : value.trim();
    }
}