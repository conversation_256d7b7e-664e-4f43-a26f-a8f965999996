package com.pttl.mobile.manager.domain.entity;

import lombok.Data;

import java.io.Serializable;

/**
 * 前端日志表
 * <AUTHOR>
 */
@Data
public class FrontLogs implements Serializable {
    private Long id;

    /**
    * logId
    */
    private Long logId;

    /**
    * 系统名称
    */
    private String serverName;

    /**
    * 系统用户名
    */
    private String userName;

    /**
    * 日志开始时间
    */
    private Long logDate;

    /**
    * 状态
    */
    private String status;

    /**
    * 版本
    */
    private String appVersion;

    /**
    * 设备信息
    */
    private String deviceInfo;

    /**
    * ihrName
    */
    private String ihrName;

    /**
    * logStartTime
    */
    private Long logStartTime;

    /**
    * 系统类型
    */
    private String systemType;

    /**
    * 版本
    */
    private String systemVersion;

    /**
    * nwePageLoad
    */
    private String nwePageLoad;

    /**
    * logDetail
    */
    private String logDetail;

    /**
    * 备注
    */
    private String remark;

    private static final long serialVersionUID = 1L;
}