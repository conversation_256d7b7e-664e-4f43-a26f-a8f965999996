package com.pttl.mobile.manager.domain.po;

import java.io.Serializable;
import java.util.Date;

public class Strategy implements Serializable {
    /**
     * 策略id
     */
    private String id;

    /**
     * 策略名称
     */
    private String name;

    /**
     * 描述
     */
    private String description;

    /**
     * 权重
     */
    private Short weight;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date lastUpdate;

    /**
     * 外键 当前数据生效的系统平台
     */
    private Boolean systemPlatform;

    /**
     * 环境标识 1:正式环境  2:预生产环境 用户发版测试
     */
    private Boolean env;

    /**
     * strategy
     */
    private static final long serialVersionUID = 1L;

    /**
     * 策略id
     * @return id 策略id
     */
    public String getId() {
        return id;
    }

    /**
     * 策略id
     * @param id 策略id
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * 策略名称
     * @return name 策略名称
     */
    public String getName() {
        return name;
    }

    /**
     * 策略名称
     * @param name 策略名称
     */
    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    /**
     * 描述
     * @return description 描述
     */
    public String getDescription() {
        return description;
    }

    /**
     * 描述
     * @param description 描述
     */
    public void setDescription(String description) {
        this.description = description == null ? null : description.trim();
    }

    /**
     * 权重
     * @return weight 权重
     */
    public Short getWeight() {
        return weight;
    }

    /**
     * 权重
     * @param weight 权重
     */
    public void setWeight(Short weight) {
        this.weight = weight;
    }

    /**
     * 创建时间
     * @return create_date 创建时间
     */
    public Date getCreateDate() {
        return createDate;
    }

    /**
     * 创建时间
     * @param createDate 创建时间
     */
    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    /**
     * 更新时间
     * @return last_update 更新时间
     */
    public Date getLastUpdate() {
        return lastUpdate;
    }

    /**
     * 更新时间
     * @param lastUpdate 更新时间
     */
    public void setLastUpdate(Date lastUpdate) {
        this.lastUpdate = lastUpdate;
    }

    /**
     * 外键 当前数据生效的系统平台
     * @return system_platform 外键 当前数据生效的系统平台
     */
    public Boolean getSystemPlatform() {
        return systemPlatform;
    }

    /**
     * 外键 当前数据生效的系统平台
     * @param systemPlatform 外键 当前数据生效的系统平台
     */
    public void setSystemPlatform(Boolean systemPlatform) {
        this.systemPlatform = systemPlatform;
    }

    /**
     * 环境标识 1:正式环境  2:预生产环境 用户发版测试
     * @return env 环境标识 1:正式环境  2:预生产环境 用户发版测试
     */
    public Boolean getEnv() {
        return env;
    }

    /**
     * 环境标识 1:正式环境  2:预生产环境 用户发版测试
     * @param env 环境标识 1:正式环境  2:预生产环境 用户发版测试
     */
    public void setEnv(Boolean env) {
        this.env = env;
    }
}