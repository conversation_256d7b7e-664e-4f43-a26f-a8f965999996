package com.pttl.mobile.manager.domain.po;

import java.io.Serializable;
import java.util.Date;

public class Runtime implements Serializable {
    /**
     * 
     */
    private String id;

    /**
     * 运行时版本
     */
    private String version;

    /**
     * 描述信息
     */
    private String description;

    /**
     * 运行时包的名称
     */
    private String packageName;

    /**
     * 运行时上传地址
     */
    private String packageUrl;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 外键 当前数据生效的系统平台
     */
    private Integer systemPlatform;

    /**
     * 环境标识 1:正式环境  2:预生产环境 用户发版测试
     */
    private Integer env;

    /**
     * runtime
     */
    private static final long serialVersionUID = 1L;

    /**
     * 
     * @return id 
     */
    public String getId() {
        return id;
    }

    /**
     * 
     * @param id 
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * 运行时版本
     * @return version 运行时版本
     */
    public String getVersion() {
        return version;
    }

    /**
     * 运行时版本
     * @param version 运行时版本
     */
    public void setVersion(String version) {
        this.version = version == null ? null : version.trim();
    }

    /**
     * 描述信息
     * @return description 描述信息
     */
    public String getDescription() {
        return description;
    }

    /**
     * 描述信息
     * @param description 描述信息
     */
    public void setDescription(String description) {
        this.description = description == null ? null : description.trim();
    }

    /**
     * 运行时包的名称
     * @return package_name 运行时包的名称
     */
    public String getPackageName() {
        return packageName;
    }

    /**
     * 运行时包的名称
     * @param packageName 运行时包的名称
     */
    public void setPackageName(String packageName) {
        this.packageName = packageName == null ? null : packageName.trim();
    }

    /**
     * 运行时上传地址
     * @return package_url 运行时上传地址
     */
    public String getPackageUrl() {
        return packageUrl;
    }

    /**
     * 运行时上传地址
     * @param packageUrl 运行时上传地址
     */
    public void setPackageUrl(String packageUrl) {
        this.packageUrl = packageUrl == null ? null : packageUrl.trim();
    }

    /**
     * 创建时间
     * @return create_date 创建时间
     */
    public Date getCreateDate() {
        return createDate;
    }

    /**
     * 创建时间
     * @param createDate 创建时间
     */
    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    /**
     * 外键 当前数据生效的系统平台
     * @return system_platform 外键 当前数据生效的系统平台
     */
    public Integer getSystemPlatform() {
        return systemPlatform;
    }

    /**
     * 外键 当前数据生效的系统平台
     * @param systemPlatform 外键 当前数据生效的系统平台
     */
    public void setSystemPlatform(Integer systemPlatform) {
        this.systemPlatform = systemPlatform;
    }

    /**
     * 环境标识 1:正式环境  2:预生产环境 用户发版测试
     * @return env 环境标识 1:正式环境  2:预生产环境 用户发版测试
     */
    public Integer getEnv() {
        return env;
    }

    /**
     * 环境标识 1:正式环境  2:预生产环境 用户发版测试
     * @param env 环境标识 1:正式环境  2:预生产环境 用户发版测试
     */
    public void setEnv(Integer env) {
        this.env = env;
    }
}