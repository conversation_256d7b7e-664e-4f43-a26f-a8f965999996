package com.pttl.mobile.manager.domain.dto;

import com.pttl.mobile.manager.domain.entity.Strategy2DO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 策略详情信息
 *
 * <AUTHOR>
 * @date 2022/10/17
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "策略详情dto")
public class StrategyDetailDTO extends Strategy2DO {

    private static final long serialVersionUID = -3109371271516887244L;

    /**
     * 策略生效人数
     */
    @ApiModelProperty(value = "策略生效人数")
    private Integer strategyNum;
}
