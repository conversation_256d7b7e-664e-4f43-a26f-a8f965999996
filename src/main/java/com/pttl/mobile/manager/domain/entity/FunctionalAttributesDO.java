package com.pttl.mobile.manager.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 系统参数(属性配置)表
 *
 * <AUTHOR>
 */
@ApiModel(value = "系统参数(属性配置)")
@Data
public class FunctionalAttributesDO implements Serializable {

    /**
     * 环境标识 1:正式环境  2:测试环境 用户发版测试
     */
    public static final int ENV_FORMAL = 1;
    public static final int ENV_TEST = 2;

    /**
     * 属于哪种数据类型: 1:环境参数 2:Webview拦截系统加载地址 3:系统应用描述（/）区分 ; 后续可自己定义
     */
    public static final int DATA_TYPE_ENVIRONMENTAL_PARAMETERS = 1;
    public static final int DATA_TYPE_INTERCEPT_PATH = 2;
    public static final int DATA_TYPE_APP_DESCRIBE = 3;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 操作系统 1-Android 2-IOS
     */
    @ApiModelProperty(value = "操作系统 1-Android  2-IOS")
    private Integer operatingSystem;

    /**
     * 环境标识 1:正式环境  2:测试环境 用户发版测试
     *
     * @see FunctionalAttributesDO#ENV_FORMAL
     */
    @ApiModelProperty(value = "环境标识 1:正式环境  2:测试环境 用户发版测试")
    private Integer env;

    /**
     * 属于哪种数据类型: 1:环境参数 2:Webview拦截系统加载地址 3:系统应用描述（/）区分 ; 后续可自己定义
     *
     * @see FunctionalAttributesDO#DATA_TYPE_ENVIRONMENTAL_PARAMETERS
     */
    @ApiModelProperty(value = "属于哪种数据类型: 1:环境参数 2:Webview拦截系统加载地址 3:系统应用描述（/）区分 ; 后续可自己定义")
    private Integer dataType;

    /**
     * 功能属性id
     */
    @ApiModelProperty(value = "功能属性id")
    private String functionalAttributes;

    /**
     * 属性值
     */
    @ApiModelProperty(value = "属性值")
    private String attributeValue;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 功能属性描述, 例:消息推送：http://mpttl.com
     */
    @ApiModelProperty(value = "功能属性描述, 例:消息推送：http://mpttl.com")
    private String remark;

    private static final long serialVersionUID = 1L;
}