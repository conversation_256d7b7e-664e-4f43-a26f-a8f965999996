package com.pttl.mobile.manager.domain.entity;

import lombok.Data;

import java.io.Serializable;

/**
 * 移动端日志表
 * <AUTHOR>
 */
@Data
public class MobileLogs implements Serializable {
    private Long id;

    private String serverName;

    /**
    * api名称
    */
    private String apiName;

    /**
    * 版本号
    */
    private String appVersion;

    /**
    * 描述
    */
    private String description;

    /**
    * 时间戳
    */
    private Long logDate;

    /**
    * ihrName
    */
    private String ihrName;

    /**
    * 设备类型
    */
    private String deviceInfo;

    private Integer pageTime;

    /**
    * 平台
    */
    private String platform;

    /**
    * 系统类型
    */
    private String systemType;

    /**
    * 状态
    */
    private String status;

    private String systemLoginName;

    private String systemVersion;

    private Integer timeUsed;

    /**
    * 备注
    */
    private String remark;

    private static final long serialVersionUID = 1L;
}