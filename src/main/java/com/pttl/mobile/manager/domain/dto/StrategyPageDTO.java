package com.pttl.mobile.manager.domain.dto;

import com.pttl.mobile.manager.domain.entity.Strategy2DO;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 策略分页数据dto
 *
 * <AUTHOR>
 * @date 2022/10/17
 **/
@Data
@ApiModel(value = "策略分页数据dto")
@EqualsAndHashCode(callSuper = true)
public class StrategyPageDTO extends Strategy2DO {
    private static final long serialVersionUID = 1560504517820496750L;
}
