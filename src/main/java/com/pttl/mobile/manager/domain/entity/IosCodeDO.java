package com.pttl.mobile.manager.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * ios QRCode (ios二维码数据导入)
 *
 * <AUTHOR>
 */
@ApiModel(value = "ios QRCode (ios二维码数据导入)")
@Data
public class IosCodeDO implements Serializable {

    private static final long serialVersionUID = -2017718843612311349L;

    /**
     * 是否有效(0:有效; 1: 有效, 默认0:有效)
     */
    public static final int VALID_STATUS_VALID = 0;
    public static final int VALID_STATUS_DEL = 1;

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "代码")
    private String code;

    @ApiModelProperty(value = "链接")
    private String codeLink;

    /**
     * @see IosCodeDO#VALID_STATUS_VALID
     */
    @ApiModelProperty(value = "是否有效: 0 : 有效, 1 : 有效")
    private Integer validStatus;

    @ApiModelProperty(value = "导入时间")
    private Date importTime;

    @ApiModelProperty(value = "失效时间, 当状态为无效时, 这里才会有值")
    private Date failureTime;

    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 应用类型, (动态配置项目配置文件中)
     *
     * @see com.pttl.mobile.manager.config.IosCodeTypeMapConfiguration#iosCodeType
     */
    @ApiModelProperty(value = "应用类型, (动态配置项目配置文件中)")
    private Integer appType;
}