package com.pttl.mobile.manager.domain.po;

import java.io.Serializable;
import java.util.Date;

public class Swa implements Serializable {
    /**
     * id
     */
    private String id;

    /**
     * 应用id
     */
    private String applicationId;

    /**
     * swa类型(1: 密码管家; 2: AD代理认证)
     */
    private Integer type;

    /**
     * 用户登录方式(1: 统一认证; 2: 用户自定义)
     */
    private Integer loginType;

    /**
     * 登录字段(1: 用户登录名; 2: 邮箱; 3: 邮箱前缀; 4: 手机号)
     */
    private Integer loginField;

    /**
     * 登录地址或者host地址
     */
    private String url;

    /**
     * 是否存在自定义前缀或者后缀(0: 否; 1: 是)
     */
    private Boolean isCustomizedCharUsed;

    /**
     * 前缀
     */
    private String prefix;

    /**
     * 后缀
     */
    private String suffix;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间戳
     */
    private Date lastUpdate;

    /**
     * 外键 当前数据生效的系统平台
     */
    private Integer systemPlatform;

    /**
     * 环境标识 1:正式环境  2:预生产环境 用户发版测试
     */
    private Integer env;

    /**
     * swa
     */
    private static final long serialVersionUID = 1L;

    /**
     * id
     * @return id id
     */
    public String getId() {
        return id;
    }

    /**
     * id
     * @param id id
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * 应用id
     * @return application_id 应用id
     */
    public String getApplicationId() {
        return applicationId;
    }

    /**
     * 应用id
     * @param applicationId 应用id
     */
    public void setApplicationId(String applicationId) {
        this.applicationId = applicationId == null ? null : applicationId.trim();
    }

    /**
     * swa类型(1: 密码管家; 2: AD代理认证)
     * @return type swa类型(1: 密码管家; 2: AD代理认证)
     */
    public Integer getType() {
        return type;
    }

    /**
     * swa类型(1: 密码管家; 2: AD代理认证)
     * @param type swa类型(1: 密码管家; 2: AD代理认证)
     */
    public void setType(Integer type) {
        this.type = type;
    }

    /**
     * 用户登录方式(1: 统一认证; 2: 用户自定义)
     * @return login_type 用户登录方式(1: 统一认证; 2: 用户自定义)
     */
    public Integer getLoginType() {
        return loginType;
    }

    /**
     * 用户登录方式(1: 统一认证; 2: 用户自定义)
     * @param loginType 用户登录方式(1: 统一认证; 2: 用户自定义)
     */
    public void setLoginType(Integer loginType) {
        this.loginType = loginType;
    }

    /**
     * 登录字段(1: 用户登录名; 2: 邮箱; 3: 邮箱前缀; 4: 手机号)
     * @return login_field 登录字段(1: 用户登录名; 2: 邮箱; 3: 邮箱前缀; 4: 手机号)
     */
    public Integer getLoginField() {
        return loginField;
    }

    /**
     * 登录字段(1: 用户登录名; 2: 邮箱; 3: 邮箱前缀; 4: 手机号)
     * @param loginField 登录字段(1: 用户登录名; 2: 邮箱; 3: 邮箱前缀; 4: 手机号)
     */
    public void setLoginField(Integer loginField) {
        this.loginField = loginField;
    }

    /**
     * 登录地址或者host地址
     * @return url 登录地址或者host地址
     */
    public String getUrl() {
        return url;
    }

    /**
     * 登录地址或者host地址
     * @param url 登录地址或者host地址
     */
    public void setUrl(String url) {
        this.url = url == null ? null : url.trim();
    }

    /**
     * 是否存在自定义前缀或者后缀(0: 否; 1: 是)
     * @return is_customized_char_used 是否存在自定义前缀或者后缀(0: 否; 1: 是)
     */
    public Boolean getIsCustomizedCharUsed() {
        return isCustomizedCharUsed;
    }

    /**
     * 是否存在自定义前缀或者后缀(0: 否; 1: 是)
     * @param isCustomizedCharUsed 是否存在自定义前缀或者后缀(0: 否; 1: 是)
     */
    public void setIsCustomizedCharUsed(Boolean isCustomizedCharUsed) {
        this.isCustomizedCharUsed = isCustomizedCharUsed;
    }

    /**
     * 前缀
     * @return prefix 前缀
     */
    public String getPrefix() {
        return prefix;
    }

    /**
     * 前缀
     * @param prefix 前缀
     */
    public void setPrefix(String prefix) {
        this.prefix = prefix == null ? null : prefix.trim();
    }

    /**
     * 后缀
     * @return suffix 后缀
     */
    public String getSuffix() {
        return suffix;
    }

    /**
     * 后缀
     * @param suffix 后缀
     */
    public void setSuffix(String suffix) {
        this.suffix = suffix == null ? null : suffix.trim();
    }

    /**
     * 创建时间
     * @return create_date 创建时间
     */
    public Date getCreateDate() {
        return createDate;
    }

    /**
     * 创建时间
     * @param createDate 创建时间
     */
    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    /**
     * 更新时间戳
     * @return last_update 更新时间戳
     */
    public Date getLastUpdate() {
        return lastUpdate;
    }

    /**
     * 更新时间戳
     * @param lastUpdate 更新时间戳
     */
    public void setLastUpdate(Date lastUpdate) {
        this.lastUpdate = lastUpdate;
    }

    /**
     * 外键 当前数据生效的系统平台
     * @return system_platform 外键 当前数据生效的系统平台
     */
    public Integer getSystemPlatform() {
        return systemPlatform;
    }

    /**
     * 外键 当前数据生效的系统平台
     * @param systemPlatform 外键 当前数据生效的系统平台
     */
    public void setSystemPlatform(Integer systemPlatform) {
        this.systemPlatform = systemPlatform;
    }

    /**
     * 环境标识 1:正式环境  2:预生产环境 用户发版测试
     * @return env 环境标识 1:正式环境  2:预生产环境 用户发版测试
     */
    public Integer getEnv() {
        return env;
    }

    /**
     * 环境标识 1:正式环境  2:预生产环境 用户发版测试
     * @param env 环境标识 1:正式环境  2:预生产环境 用户发版测试
     */
    public void setEnv(Integer env) {
        this.env = env;
    }
}