package com.pttl.mobile.manager.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 管理端 操作日志表
 *
 * <AUTHOR>
 */
@ApiModel(value = "管理端 操作日志")
@Data
public class ManagerOperationLogDO implements Serializable {

    /**
     * 操作成功与否状态, 0: 异常, 1:成功
     */
    public static final int OPERATION_STATUS_EXCEPTION = 0;
    public static final int OPERATION_STATUS_SUCCESS = 1;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 功能模块
     */
    @ApiModelProperty(value = "功能模块")
    private String operationModule;

    /**
     * 当前操作成功与否状态, 0: 异常, 1:成功
     *
     * @see ManagerOperationLogDO#OPERATION_STATUS_SUCCESS
     */
    @ApiModelProperty(value = "日志类型 状态值, 0: 异常, 1:成功")
    private Integer operationStatus;

    /**
     * 操作类型
     */
    @ApiModelProperty(value = "操作类型")
    private String operationType;

    /**
     * 操作描述
     */
    @ApiModelProperty(value = "操作描述")
    private String operationDescription;

    /**
     * 请求参数
     */
    @ApiModelProperty(value = "请求参数")
    private String operationRequestParam;

    /**
     * 返回参数
     */
    @ApiModelProperty(value = "返回参数; 日志类型 状态值为异常时, 返回参数为空")
    private String operationResponseParam;

    /**
     * 异常名称, 当操作成功状态为异常时存在值
     */
    @ApiModelProperty(value = "异常名称, 当操作成功状态为异常时存在值")
    private String exceptionName;

    /**
     * 异常信息, 当操作成功状态为异常时存在值
     */
    @ApiModelProperty(value = "异常信息, 当操作成功状态为异常时存在值")
    private String exceptionMessage;

    /**
     * 操作员id
     */
    @ApiModelProperty(value = "操作员id")
    private Long operationUserId;

    /**
     * 操作员名称
     */
    @ApiModelProperty(value = "操作员名称")
    private String operationUserName;

    /**
     * 操作方法
     */
    @ApiModelProperty(value = "操作方法")
    private String operationMethod;

    /**
     * 请求URI
     */
    @ApiModelProperty(value = "请求URI")
    private String operationUri;

    /**
     * 请求ip
     */
    @ApiModelProperty(value = "请求ip")
    private String operationIp;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    private static final long serialVersionUID = 1L;
}