package com.pttl.mobile.manager.domain.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 策略组
 *
 * <AUTHOR>
 */
@ApiModel(value = "策略信息更新vo")
@Data
public class StrategyUpdateRequest implements Serializable {


    /**
     * 策略id
     */
    @ApiModelProperty(value = "策略id")
    private Long id;

    /**
     * 策略名称
     */
    @ApiModelProperty(value = "策略名称")
    private String name;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String description;

    /**
     * 权重
     */
    @ApiModelProperty(value = "权重")
    private Integer weight;

    private static final long serialVersionUID = 1L;
}