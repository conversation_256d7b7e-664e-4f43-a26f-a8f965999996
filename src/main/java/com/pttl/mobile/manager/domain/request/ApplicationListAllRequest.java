package com.pttl.mobile.manager.domain.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode
@Data
@ApiModel(value = "获取全部应用请求参数")
public class ApplicationListAllRequest implements Serializable {

    private static final long serialVersionUID = 1860458495001250904L;

    /**
     * 应用名称
     */
    @ApiModelProperty(value = "应用名称")
    private String name;

    /**
     * 应用组id
     */
    @ApiModelProperty(value = "应用组id")
    private String applicationGroupId;
}
