package com.pttl.mobile.manager.domain.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 移动端业务操作记录日志详情vo
 *
 * <AUTHOR>
 */
@EqualsAndHashCode()
@ApiModel(value = "移动端业务操作记录日志详情vo")
@Data
public class MobileRecordLogDetailsRequest {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 年月份 2022_11
     */
    @ApiModelProperty(value = "年月份 例子: 2022_11 ,本月或者上个月")
    private String yearMonth;

}