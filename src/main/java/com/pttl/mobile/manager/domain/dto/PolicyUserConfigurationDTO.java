package com.pttl.mobile.manager.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 策略用户配置-查询dto
 *
 * <AUTHOR>
 * @date 2022/10/17
 **/
@Data
public class PolicyUserConfigurationDTO {

    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID")
    private Long id;

    /**
     * 用户姓名
     */
    @ApiModelProperty(value = "用户姓名")
    private String name;

    /**
     * 用户email
     */
    @ApiModelProperty(value = "用户email")
    private String email;

    /**
     * 是否已经激活(0:未激活, 1:已激活)
     */
    @ApiModelProperty(value = "是否已经激活(0:未激活, 1:已激活)")
    private Integer activatedStatus;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
}
