package com.pttl.mobile.manager.domain.po;

import java.io.Serializable;

public class SystemPlatform implements Serializable {
    /**
     * 在线办公平台：1； 太力商城：2
     */
    private Boolean id;

    /**
     * 在线办公平台或者太力商城或者其他...
     */
    private String name;

    /**
     * system_platform
     */
    private static final long serialVersionUID = 1L;

    /**
     * 在线办公平台：1； 太力商城：2
     * @return id 在线办公平台：1； 太力商城：2
     */
    public Boolean getId() {
        return id;
    }

    /**
     * 在线办公平台：1； 太力商城：2
     * @param id 在线办公平台：1； 太力商城：2
     */
    public void setId(Boolean id) {
        this.id = id;
    }

    /**
     * 在线办公平台或者太力商城或者其他...
     * @return name 在线办公平台或者太力商城或者其他...
     */
    public String getName() {
        return name;
    }

    /**
     * 在线办公平台或者太力商城或者其他...
     * @param name 在线办公平台或者太力商城或者其他...
     */
    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }
}