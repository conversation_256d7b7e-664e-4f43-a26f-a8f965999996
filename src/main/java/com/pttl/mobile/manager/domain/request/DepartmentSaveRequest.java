package com.pttl.mobile.manager.domain.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "部门保存请求参数")
public class DepartmentSaveRequest implements Serializable {

    private static final long serialVersionUID = 4489191976022710520L;
    /**
     * 部门全路径ID
     */
    @ApiModelProperty(value = "部门全路径ID")
    private String fullDepartmentId;

    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称")
    private String name;

    /**
     * 部门描述
     */
    @ApiModelProperty(value = "部门描述")
    private String description;

    /**
     * 父部门ID
     */
    @ApiModelProperty(value = "父部门ID")
    private Long parentDepartmentId;
}
