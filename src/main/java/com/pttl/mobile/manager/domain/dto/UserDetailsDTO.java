package com.pttl.mobile.manager.domain.dto;

import com.pttl.mobile.manager.domain.entity.UserDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户信息DTO实体
 *
 * <AUTHOR>
 */
@ApiModel(value = "用户信息DTO实体")
@Data
public class UserDetailsDTO implements Serializable {

    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID")
    private Long id;

    /**
     * 用户所属部门id
     */
    @ApiModelProperty(value = "用户所属部门id")
    private Long departmentId;

    /**
     * 用户所属部门全路径
     */
    @ApiModelProperty(value = "用户所属部门全路径")
    private String fullDepartmentName;

    /**
     * 用户姓名
     */
    @ApiModelProperty(value = "用户姓名")
    private String name;


    /**
     * 用户email
     */
    @ApiModelProperty(value = "用户email")
    private String email;

    /**
     * 手机
     */
    @ApiModelProperty(value = "手机")
    private String mobile;

    /**
     * 用户登录账户
     */
    @ApiModelProperty(value = "用户登录账户")
    private String loginName;


    /**
     * 岗位名称
     */
    @ApiModelProperty(value = "岗位名称")
    private String positionName;


    /**
     * 是否已经激活(0:未激活, 1:已激活)
     *
     * @see UserDO#ACTIVATED_STATUS_INACTIVATED
     */
    @ApiModelProperty(value = "是否已经激活(0:未激活, 1:已激活)")
    private Integer activatedStatus;

    /**
     * 激活时间
     */
    @ApiModelProperty(value = "激活时间")
    private Date activatedTime;

    /**
     * 是否被禁用(0:未禁用,1:已禁用)
     *
     * @see UserDO#DISABLED_STATUS_NOT_DISABLED
     */
    @ApiModelProperty(value = "是否被禁用(0:未禁用,1:已禁用)")
    private Integer disabledStatus;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}