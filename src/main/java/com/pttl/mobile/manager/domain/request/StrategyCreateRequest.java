package com.pttl.mobile.manager.domain.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 策略组
 *
 * <AUTHOR>
 */
@ApiModel(value = "策略信息新增vo")
@Data
public class StrategyCreateRequest implements Serializable {


    /**
     * 策略名称
     */
    @ApiModelProperty(value = "策略名称")
    private String name;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String description;

    /**
     * 权重
     */
    @ApiModelProperty(value = "权重")
    private Integer weight;

    /**
     * 平台(1:PC  2:移动 3:pad)
     */
    @ApiModelProperty(value = "平台(1:PC  2:移动 3:pad)")
    private Integer platform;


    /**
     * 策略关联 应用id集合
     */
    @ApiModelProperty(value = "策略关联 应用id集合")
    private List<String> applicationIds;

    /**
     * 策略关联 部门id集合
     */
    @ApiModelProperty(value = "策略关联 部门id集合")
    private List<Long> departmentIds;

    /**
     * 策略关联 用户id集合
     */
    @ApiModelProperty(value = "策略关联 用户id集合")
    private List<Long> userIds;

    private static final long serialVersionUID = 1L;
}