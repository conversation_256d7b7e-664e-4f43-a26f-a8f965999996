package com.pttl.mobile.manager.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 部门树信息集合dto
 *
 * <AUTHOR>
 * @date 2022/10/13
 **/
@Data
@ApiModel(value = "部门树实体")
public class DepartmentTreeDTO {

    /**
     * 部门ID
     */
    @ApiModelProperty(value = "部门ID")
    private Long id;

    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称")
    private String name;

    /**
     * 部门描述
     */
    @ApiModelProperty(value = "部门描述")
    private String description;

    /**
     * 父部门ID
     */
    @ApiModelProperty(value = "父部门ID")
    private Long parentDepartmentId;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 当前部门的子部门
     */
    @ApiModelProperty(value = "当前部门的子部门")
    private List<DepartmentTreeDTO> childDepartments;
}