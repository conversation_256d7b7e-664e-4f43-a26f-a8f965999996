package com.pttl.mobile.manager.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode
@Data
@ApiModel(value = "单个部门获取dto")
public class DepartmentQueryDTO implements Serializable {

    private static final long serialVersionUID = -110551535316243987L;

    /**
     * 部门ID
     */
    @ApiModelProperty(value = "部门ID")
    private Long id;

    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称")
    private String name;

    /**
     * 部门描述
     */
    @ApiModelProperty(value = "部门描述")
    private String description;

    /**
     * 部门全路径
     */
    @ApiModelProperty(value = "部门全路径")
    private String fullDepartmentName;
}
