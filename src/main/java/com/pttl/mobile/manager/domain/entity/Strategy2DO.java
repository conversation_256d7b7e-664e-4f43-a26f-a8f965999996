package com.pttl.mobile.manager.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;

/**
 * 策略组
 *
 * <AUTHOR>
 */
@ApiModel(value = "策略信息表")
@Data
public class Strategy2DO implements Serializable {

    /**
     * 1: 基本信息; 2: 功能配置; 3: 应用配置; 4: 用户配置
     */
    public static final Integer CURRENT_STEP_BASE = 1;


    /**
     * 平台(1:PC  2:移动 3:pad)
     */
    public static final Integer PLATFORM_PC = 1;
    public static final Integer PLATFORM_MOBILE = 2;
    public static final Integer PLATFORM_PAD = 3;


    /**
     * 是否删除(0:未删除; 1: 已删除, 默认0:未删除)
     */
    public static final int DELETED_NO = 0;
    public static final int DELETED_YES = 1;


    /**
     * 策略id
     */
    @ApiModelProperty(value = "策略id")
    private Long id;

    /**
     * 策略名称
     */
    @ApiModelProperty(value = "策略名称")
    private String name;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String description;

    /**
     * 权重
     */
    @ApiModelProperty(value = "权重")
    private Integer weight;

    /**
     * 平台(1:PC  2:移动 3:pad)
     */
    @ApiModelProperty(value = "平台(1:PC  2:移动 3:pad)")
    private Integer platform;

    /**
     * 1: 基本信息; 2: 功能配置; 3: 应用配置; 4: 用户配置
     */
    @ApiModelProperty(value = "1: 基本信息; 2: 功能配置; 3: 应用配置; 4: 用户配置")
    private Integer currentStep;

    /**
     * 是否删除 0:未删除 1已删除
     */
    @ApiModelProperty(value = "是否删除 0:未删除 1已删除")
    private Integer isDeleted;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createDate;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date lastUpdate;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    private static final long serialVersionUID = 1L;
}