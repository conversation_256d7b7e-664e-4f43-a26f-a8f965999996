package com.pttl.mobile.manager.domain.po;

import java.io.Serializable;

public class StrategyUser implements Serializable {
    /**
     * 
     */
    private String id;

    /**
     * 
     */
    private String strategyId;

    /**
     * 
     */
    private String userId;

    /**
     * strategy_user
     */
    private static final long serialVersionUID = 1L;

    /**
     * 
     * @return id 
     */
    public String getId() {
        return id;
    }

    /**
     * 
     * @param id 
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * 
     * @return strategy_id 
     */
    public String getStrategyId() {
        return strategyId;
    }

    /**
     * 
     * @param strategyId 
     */
    public void setStrategyId(String strategyId) {
        this.strategyId = strategyId == null ? null : strategyId.trim();
    }

    /**
     * 
     * @return user_id 
     */
    public String getUserId() {
        return userId;
    }

    /**
     * 
     * @param userId 
     */
    public void setUserId(String userId) {
        this.userId = userId == null ? null : userId.trim();
    }
}