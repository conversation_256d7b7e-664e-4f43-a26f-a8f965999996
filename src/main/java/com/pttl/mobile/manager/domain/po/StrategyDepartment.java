package com.pttl.mobile.manager.domain.po;

import java.io.Serializable;

public class StrategyDepartment implements Serializable {
    /**
     * 
     */
    private String id;

    /**
     * 策略id
     */
    private String strategyId;

    /**
     * 部门id
     */
    private String departmentId;

    /**
     * strategy_department
     */
    private static final long serialVersionUID = 1L;

    /**
     * 
     * @return id 
     */
    public String getId() {
        return id;
    }

    /**
     * 
     * @param id 
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * 策略id
     * @return strategy_id 策略id
     */
    public String getStrategyId() {
        return strategyId;
    }

    /**
     * 策略id
     * @param strategyId 策略id
     */
    public void setStrategyId(String strategyId) {
        this.strategyId = strategyId == null ? null : strategyId.trim();
    }

    /**
     * 部门id
     * @return department_id 部门id
     */
    public String getDepartmentId() {
        return departmentId;
    }

    /**
     * 部门id
     * @param departmentId 部门id
     */
    public void setDepartmentId(String departmentId) {
        this.departmentId = departmentId == null ? null : departmentId.trim();
    }
}