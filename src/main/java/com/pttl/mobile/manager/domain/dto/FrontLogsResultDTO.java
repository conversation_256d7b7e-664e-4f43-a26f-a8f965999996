package com.pttl.mobile.manager.domain.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 前端日志表
 * <AUTHOR>
 */
@Data
public class FrontLogsResultDTO implements Serializable {
    private Long id;


    /**
    * 系统名称
    */
    private String serverName;

    /**
     * 系统类型
     */
    private String systemType;

    /**
    * 日志开始时间
    */
    private Long logDate;

    /**
    * 状态
    */
    private String status;

    /**
    * ihrName
    */
    private String ihrName;

    private String userName;


    private static final long serialVersionUID = 1L;
}