package com.pttl.mobile.manager.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

import lombok.Data;

/**
 * 策略组应用关联表
 *
 * <AUTHOR>
 */
@ApiModel(value = "策略与应用管理表")
@Data
public class StrategyApplication2DO implements Serializable {
    @ApiModelProperty(value = "主键")
    private Integer id;

    /**
     * 策略组id
     */
    @ApiModelProperty(value = "策略组id")
    private Long strategyId;

    /**
     * 应用id(如果选了部分二级应用需要包括父应用id)
     */
    @ApiModelProperty(value = "应用id(如果选了部分二级应用需要包括父应用id)")
    private String applicationId;

    private static final long serialVersionUID = 1L;
}