package com.pttl.mobile.manager.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 通讯录人员排序
 *
 * <AUTHOR>
 */
@ApiModel(value = "通讯录人员排序参数dto")
@Data
public class AddressBookOfPersonnelRankingDTO implements Serializable {

    /**
     * 部门id(dept_id)
     */
    @ApiModelProperty(value = "部门id(dept_id)")
    private Integer deptId;

    /**
     * 员工id
     */
    @ApiModelProperty(value = "(待)更新交换 员工id 1")
    private String employeeId1;

    /**
     * 员工id
     */
    @ApiModelProperty(value = "(被)更新交换 员工id 2")
    private String employeeId2;

    private static final long serialVersionUID = 1L;
}