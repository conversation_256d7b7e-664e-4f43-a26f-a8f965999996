package com.pttl.mobile.manager.domain.entity;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 短信发送记录日志
 * <AUTHOR>
 */
@Data
@Builder
public class SmsLogs implements Serializable {
    /**
    * 主键
    */
    private Long id;

    /**
    * 告警类型, 如 ios-code
    */
    private String smsType;

    /**
    * 人员名称
    */
    private String name;

    /**
    * 手机号
    */
    private String phoneNum;

    /**
    * 模板名称
    */
    private String modelName;

    /**
    * 短信模板内容
    */
    private String modelContent;

    /**
    * 条件或者阀值
    */
    private String smsCondition;

    /**
    * 创建时间
    */
    private Date createTime;

    private String reservedField1;

    private String reservedField2;

    private static final long serialVersionUID = 1L;
}