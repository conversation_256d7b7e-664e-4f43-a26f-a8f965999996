package com.pttl.mobile.manager.domain.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 扩展业务数据存储表, 比如增加个开关/配置等
 */
@Data
public class ExtendedBusiness implements Serializable {
    private Long id;

    /**
     * 关键字/类型标识
     */
    private String dataKey;

    /**
     * 业务数据
     */
    private String dataStr;

    /**
     * 业务数据
     */
    private String dataText;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 备注
     */
    private String remark;

    private static final long serialVersionUID = 1L;
}