package com.pttl.mobile.manager.domain.dto;

import com.pttl.mobile.manager.domain.entity.UserDO;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户dto
 *
 * <AUTHOR>
 * @date 2022/10/17
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "用户信息DTO实体")
public class UserDTO extends UserDO {

    private static final long serialVersionUID = -3238065432878725673L;
}
