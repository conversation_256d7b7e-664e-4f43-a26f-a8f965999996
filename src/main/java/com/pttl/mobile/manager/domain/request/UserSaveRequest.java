package com.pttl.mobile.manager.domain.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 用户表
 *
 * <AUTHOR>
 */
@ApiModel(value = "新增用户信息实体")
@Data
public class UserSaveRequest implements Serializable {

    /**
     * 用户所属部门id
     */
    @ApiModelProperty(value = "用户所属部门id")
    private Long departmentId;

    /**
     * 用户姓名
     */
    @ApiModelProperty(value = "用户姓名")
    private String name;

    /**
     * 用户email
     */
    @ApiModelProperty(value = "用户email")
    private String email;

    /**
     * 手机
     */
    @ApiModelProperty(value = "手机")
    private String mobile;

    /**
     * 用户登录账户
     */
    @ApiModelProperty(value = "用户登录账户")
    private String loginName;

    /**
     * 岗位名称
     */
    @ApiModelProperty(value = "岗位名称")
    private String positionName;


    private static final long serialVersionUID = 1L;
}