package com.pttl.mobile.manager.domain.po;

import java.io.Serializable;

public class Swa<PERSON>eep<PERSON> implements Serializable {
    /**
     * swa外键
     */
    private String swaId;

    /**
     * 用户名的xpath
     */
    private String usernameXpath;

    /**
     * 密码的xpath
     */
    private String passwordXpath;

    /**
     * 登录按钮的xpath
     */
    private String loginButtonXpath;

    /**
     * 页面登录类型（1：常规登录；2：iframe嵌套登录）
     */
    private Integer loginPageType;

    /**
     * iframe的xpath
     */
    private String iframeXpath;

    /**
     * swa_keeper
     */
    private static final long serialVersionUID = 1L;

    /**
     * swa外键
     * @return swa_id swa外键
     */
    public String getSwaId() {
        return swaId;
    }

    /**
     * swa外键
     * @param swaId swa外键
     */
    public void setSwaId(String swaId) {
        this.swaId = swaId == null ? null : swaId.trim();
    }

    /**
     * 用户名的xpath
     * @return username_xpath 用户名的xpath
     */
    public String getUsernameXpath() {
        return usernameXpath;
    }

    /**
     * 用户名的xpath
     * @param usernameXpath 用户名的xpath
     */
    public void setUsernameXpath(String usernameXpath) {
        this.usernameXpath = usernameXpath == null ? null : usernameXpath.trim();
    }

    /**
     * 密码的xpath
     * @return password_xpath 密码的xpath
     */
    public String getPasswordXpath() {
        return passwordXpath;
    }

    /**
     * 密码的xpath
     * @param passwordXpath 密码的xpath
     */
    public void setPasswordXpath(String passwordXpath) {
        this.passwordXpath = passwordXpath == null ? null : passwordXpath.trim();
    }

    /**
     * 登录按钮的xpath
     * @return login_button_xpath 登录按钮的xpath
     */
    public String getLoginButtonXpath() {
        return loginButtonXpath;
    }

    /**
     * 登录按钮的xpath
     * @param loginButtonXpath 登录按钮的xpath
     */
    public void setLoginButtonXpath(String loginButtonXpath) {
        this.loginButtonXpath = loginButtonXpath == null ? null : loginButtonXpath.trim();
    }

    /**
     * 页面登录类型（1：常规登录；2：iframe嵌套登录）
     * @return login_page_type 页面登录类型（1：常规登录；2：iframe嵌套登录）
     */
    public Integer getLoginPageType() {
        return loginPageType;
    }

    /**
     * 页面登录类型（1：常规登录；2：iframe嵌套登录）
     * @param loginPageType 页面登录类型（1：常规登录；2：iframe嵌套登录）
     */
    public void setLoginPageType(Integer loginPageType) {
        this.loginPageType = loginPageType;
    }

    /**
     * iframe的xpath
     * @return iframe_xpath iframe的xpath
     */
    public String getIframeXpath() {
        return iframeXpath;
    }

    /**
     * iframe的xpath
     * @param iframeXpath iframe的xpath
     */
    public void setIframeXpath(String iframeXpath) {
        this.iframeXpath = iframeXpath == null ? null : iframeXpath.trim();
    }
}