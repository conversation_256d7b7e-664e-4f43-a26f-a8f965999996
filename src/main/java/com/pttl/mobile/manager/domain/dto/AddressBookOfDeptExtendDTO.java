package com.pttl.mobile.manager.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 移动端-通讯录扩展dto
 *
 * <AUTHOR>
 */
@ApiModel(value = "通讯录 部门 扩展dto")
@Data
public class AddressBookOfDeptExtendDTO implements Serializable {

    /**
     * 部门id(dept_id)
     */
    @ApiModelProperty(value = "部门id(dept_id)")
    private String deptId;

    /**
     * 自定义头像
     */
    @ApiModelProperty(value = "自定义头像")
    private String customAvatar;

    private static final long serialVersionUID = 1L;
}