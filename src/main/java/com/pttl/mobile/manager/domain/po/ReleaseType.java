package com.pttl.mobile.manager.domain.po;

import java.io.Serializable;

public class ReleaseType implements Serializable {
    /**
     * 客户端更新：1； 适配包更新：2
     */
    private Integer id;

    /**
     * 客户端更新或者适配包更新或者其他...
     */
    private String name;

    /**
     * release_type
     */
    private static final long serialVersionUID = 1L;

    /**
     * 客户端更新：1； 适配包更新：2
     * @return id 客户端更新：1； 适配包更新：2
     */
    public Integer getId() {
        return id;
    }

    /**
     * 客户端更新：1； 适配包更新：2
     * @param id 客户端更新：1； 适配包更新：2
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * 客户端更新或者适配包更新或者其他...
     * @return name 客户端更新或者适配包更新或者其他...
     */
    public String getName() {
        return name;
    }

    /**
     * 客户端更新或者适配包更新或者其他...
     * @param name 客户端更新或者适配包更新或者其他...
     */
    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }
}