package com.pttl.mobile.manager.domain.request;

import com.pttl.mobile.manager.domain.entity.FunctionalAttributesDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 创建配置参数 dto
 *
 * <AUTHOR>
 * @date 2022/10/25
 **/
@Data
@EqualsAndHashCode
@ApiModel(value = "创建配置参数vo")
public class FunctionalAttributesCreateRequest {

    /**
     * 操作系统 1-Android 2-IOS 3-Android&IOS
     */
    @ApiModelProperty(value = "操作系统 1-Android  2-IOS  3-Android&IOS")
    private Integer operatingSystem;

    /**
     * 环境标识 1:正式环境  2:测试环境 用户发版测试
     */
    /*@ApiModelProperty(value = "环境标识 1:正式环境  2:测试环境 用户发版测试")
    private Integer env;*/

    /**
     * 属于哪种数据类型: 1:环境参数 2:Webview拦截系统加载地址 3:系统应用描述（/）区分 ; 后续可自己定义
     *
     * @see FunctionalAttributesDO#DATA_TYPE_ENVIRONMENTAL_PARAMETERS
     */
    @ApiModelProperty(value = "属于哪种数据类型: 1:环境参数 2:Webview拦截系统加载地址 3:系统应用描述（/）区分 ; 后续可自己定义")
    private Integer dataType;

    /**
     * 功能属性id
     */
    @ApiModelProperty(value = "功能属性id")
    private String functionalAttributes;

    /**
     * 属性值
     */
    @ApiModelProperty(value = "属性值")
    private String attributeValue;

    /**
     * 功能属性, 例:消息推送：http://mpttl.com
     */
    @ApiModelProperty(value = "功能属性, 例:消息推送：http://mpttl.com")
    private String remark;
}
