package com.pttl.mobile.manager.domain.po;

import java.io.Serializable;

public class StrategyApplication implements Serializable {
    /**
     * 
     */
    private String id;

    /**
     * 策略组id
     */
    private String strategyId;

    /**
     * 应用id
     */
    private String applicationId;

    /**
     * strategy_application
     */
    private static final long serialVersionUID = 1L;

    /**
     * 
     * @return id 
     */
    public String getId() {
        return id;
    }

    /**
     * 
     * @param id 
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * 策略组id
     * @return strategy_id 策略组id
     */
    public String getStrategyId() {
        return strategyId;
    }

    /**
     * 策略组id
     * @param strategyId 策略组id
     */
    public void setStrategyId(String strategyId) {
        this.strategyId = strategyId == null ? null : strategyId.trim();
    }

    /**
     * 应用id
     * @return application_id 应用id
     */
    public String getApplicationId() {
        return applicationId;
    }

    /**
     * 应用id
     * @param applicationId 应用id
     */
    public void setApplicationId(String applicationId) {
        this.applicationId = applicationId == null ? null : applicationId.trim();
    }
}