package com.pttl.mobile.manager.domain.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 策略配置修改vo
 *
 * <AUTHOR>
 */
@ApiModel(value = "策略配置用户修改vo")
@Data
public class StrategyUpdateUserConfigurationRequest implements Serializable {


    /**
     * 策略id
     */
    @ApiModelProperty(value = "策略id")
    private Long id;

    /**
     * 策略关联 用户id集合
     */
    @ApiModelProperty(value = "策略关联 用户id集合")
    private List<Long> userIds;

    /**
     * 策略关联 部门id集合
     */
    @ApiModelProperty(value = "策略关联 部门id集合")
    private List<Long> departmentIds;

    private static final long serialVersionUID = 1L;
}