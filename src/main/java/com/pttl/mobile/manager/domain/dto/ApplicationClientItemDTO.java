package com.pttl.mobile.manager.domain.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description: 客户端应用集合对象
 * @date 2022/3/11 14:24
 */
@Data
public class ApplicationClientItemDTO implements Serializable {
    private String id;

    private String name;

    private String description;

    private String address;

    private String innerAddress;

    private String logoUrl;

    private String packageName;

    private String packageUrl;

    private Integer type;

    private Boolean isVisible = false;

    private String version;

    @JsonIgnore
    private String applicationGroupId;

    @JsonIgnore
    private String applicationGroupName;

    @JsonIgnore
    private Integer weight;
}
