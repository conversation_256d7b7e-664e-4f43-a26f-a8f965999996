package com.pttl.mobile.manager.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

/**
 * ios-code兑换统计结果
 *
 * <AUTHOR>
 * @date 2023/3/21
 **/
@Data
@ToString
@ApiModel(value = "ios-code兑换统计结果")
public class ExchangeStatisticsDTO {

    /**
     * 已兑换
     */
    @ApiModelProperty(value = "已兑换")
    private Integer converted;

    /**
     * 未兑换
     */
    @ApiModelProperty(value = "未兑换")
    private Integer unconverted;
}
