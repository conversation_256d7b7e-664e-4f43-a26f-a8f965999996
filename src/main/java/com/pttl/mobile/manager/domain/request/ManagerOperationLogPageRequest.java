package com.pttl.mobile.manager.domain.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.pttl.mobile.manager.domain.entity.ManagerOperationLogDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "分页查询参数vo")
public class ManagerOperationLogPageRequest extends BasePageRequest implements Serializable {

    private static final long serialVersionUID = 854364688788972058L;

    /**
     * 操作员名称
     */
    @ApiModelProperty(value = "操作员名称")
    private String operationUserName;

    /**
     * 当前操作成功与否状态, 0: 异常, 1:成功
     *
     * @see ManagerOperationLogDO#OPERATION_STATUS_SUCCESS
     */
    @ApiModelProperty(value = "日志类型: 0: 异常, 1:成功")
    private Integer operationStatus;

    /**
     * 操作类型
     */
    @ApiModelProperty(value = "操作类型")
    private String operationType;


    /**
     * 操作开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "操作开始时间范围")
    private Date startTime;

    /**
     * 操作截止时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "操作截止时间范围")
    private Date endTime;


}
