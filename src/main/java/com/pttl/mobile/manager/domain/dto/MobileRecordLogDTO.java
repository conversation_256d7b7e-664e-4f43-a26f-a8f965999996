package com.pttl.mobile.manager.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 移动端业务操作记录日志dto
 *
 * <AUTHOR>
 * @date 2022/11/11
 **/

@EqualsAndHashCode()
@Data
public class MobileRecordLogDTO {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * app版本
     */
    @ApiModelProperty(value = "app版本")
    private String appVersion;

    /**
     * 登陆名称
     */
    @ApiModelProperty(value = "登陆名称")
    private String loginName;

    /**
     * 登陆时间
     */
    @ApiModelProperty(value = "登陆时间")
    private Date loginTime;

    /**
     * 模块
     */
    @ApiModelProperty(value = "模块")
    private String module;

    /**
     * 平台 在线商城/在线办公
     */
    @ApiModelProperty(value = "平台 在线商城/在线办公")
    private String platform;


    /**
     * 子模块
     */
    @ApiModelProperty(value = "子模块")
    private String subModule;

    /**
     * 类型 Info/Error/success
     */
    @ApiModelProperty(value = "类型 Info/Error/success")
    private String type;

}
