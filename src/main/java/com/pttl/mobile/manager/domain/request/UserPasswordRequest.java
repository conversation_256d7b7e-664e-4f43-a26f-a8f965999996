package com.pttl.mobile.manager.domain.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 用户表
 *
 * <AUTHOR>
 */
@ApiModel(value = "用户密码修改信息实体")
@Data
public class UserPasswordRequest implements Serializable {

    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID")
    private Long id;

    /**
     * 用户密码
     */
    @ApiModelProperty(value = "用户密码")
    private String password;


    private static final long serialVersionUID = 1L;
}