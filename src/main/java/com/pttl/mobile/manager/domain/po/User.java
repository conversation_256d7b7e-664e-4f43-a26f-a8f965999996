package com.pttl.mobile.manager.domain.po;

import java.io.Serializable;
import java.util.Date;

public class User implements Serializable {
    /**
     * 
     */
    private String id;

    /**
     * 部门id
     */
    private String departmentId;

    /**
     * 用户姓名
     */
    private String name;

    /**
     * 用户密码
     */
    private String password;

    /**
     * 用户email
     */
    private String email;

    /**
     * 手机
     */
    private String mobile;

    /**
     * 用户登录账户
     */
    private String loginName;

    /**
     * 岗位名称
     */
    private String title;

    /**
     * 密码修改错误次数
     */
    private Boolean modifyPasswordWrongCount;

    /**
     * 密码修改错误时间
     */
    private Date modifyPasswordWrongTime;

    /**
     * 第一次登录时间
     */
    private Date firstLoginTime;

    /**
     * 是否被禁用
     */
    private Boolean isDisabled;

    /**
     * 禁用时间
     */
    private Date disabledTime;

    /**
     * 最近登录时间
     */
    private Date lastLoginTime;

    /**
     * 失败认证次数
     */
    private Boolean failedLoginTimes;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date lastUpdate;

    /**
     * 外键 当前数据生效的系统平台
     */
    private Boolean systemPlatform;

    /**
     * 环境标识 1:正式环境  2:预生产环境 用户发版测试
     */
    private Boolean env;

    /**
     * user
     */
    private static final long serialVersionUID = 1L;

    /**
     * 
     * @return id 
     */
    public String getId() {
        return id;
    }

    /**
     * 
     * @param id 
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * 部门id
     * @return department_id 部门id
     */
    public String getDepartmentId() {
        return departmentId;
    }

    /**
     * 部门id
     * @param departmentId 部门id
     */
    public void setDepartmentId(String departmentId) {
        this.departmentId = departmentId == null ? null : departmentId.trim();
    }

    /**
     * 用户姓名
     * @return name 用户姓名
     */
    public String getName() {
        return name;
    }

    /**
     * 用户姓名
     * @param name 用户姓名
     */
    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    /**
     * 用户密码
     * @return password 用户密码
     */
    public String getPassword() {
        return password;
    }

    /**
     * 用户密码
     * @param password 用户密码
     */
    public void setPassword(String password) {
        this.password = password == null ? null : password.trim();
    }

    /**
     * 用户email
     * @return email 用户email
     */
    public String getEmail() {
        return email;
    }

    /**
     * 用户email
     * @param email 用户email
     */
    public void setEmail(String email) {
        this.email = email == null ? null : email.trim();
    }

    /**
     * 手机
     * @return mobile 手机
     */
    public String getMobile() {
        return mobile;
    }

    /**
     * 手机
     * @param mobile 手机
     */
    public void setMobile(String mobile) {
        this.mobile = mobile == null ? null : mobile.trim();
    }

    /**
     * 用户登录账户
     * @return login_name 用户登录账户
     */
    public String getLoginName() {
        return loginName;
    }

    /**
     * 用户登录账户
     * @param loginName 用户登录账户
     */
    public void setLoginName(String loginName) {
        this.loginName = loginName == null ? null : loginName.trim();
    }

    /**
     * 岗位名称
     * @return title 岗位名称
     */
    public String getTitle() {
        return title;
    }

    /**
     * 岗位名称
     * @param title 岗位名称
     */
    public void setTitle(String title) {
        this.title = title == null ? null : title.trim();
    }

    /**
     * 密码修改错误次数
     * @return modify_password_wrong_count 密码修改错误次数
     */
    public Boolean getModifyPasswordWrongCount() {
        return modifyPasswordWrongCount;
    }

    /**
     * 密码修改错误次数
     * @param modifyPasswordWrongCount 密码修改错误次数
     */
    public void setModifyPasswordWrongCount(Boolean modifyPasswordWrongCount) {
        this.modifyPasswordWrongCount = modifyPasswordWrongCount;
    }

    /**
     * 密码修改错误时间
     * @return modify_password_wrong_time 密码修改错误时间
     */
    public Date getModifyPasswordWrongTime() {
        return modifyPasswordWrongTime;
    }

    /**
     * 密码修改错误时间
     * @param modifyPasswordWrongTime 密码修改错误时间
     */
    public void setModifyPasswordWrongTime(Date modifyPasswordWrongTime) {
        this.modifyPasswordWrongTime = modifyPasswordWrongTime;
    }

    /**
     * 第一次登录时间
     * @return first_login_time 第一次登录时间
     */
    public Date getFirstLoginTime() {
        return firstLoginTime;
    }

    /**
     * 第一次登录时间
     * @param firstLoginTime 第一次登录时间
     */
    public void setFirstLoginTime(Date firstLoginTime) {
        this.firstLoginTime = firstLoginTime;
    }

    /**
     * 是否被禁用
     * @return is_disabled 是否被禁用
     */
    public Boolean getIsDisabled() {
        return isDisabled;
    }

    /**
     * 是否被禁用
     * @param isDisabled 是否被禁用
     */
    public void setIsDisabled(Boolean isDisabled) {
        this.isDisabled = isDisabled;
    }

    /**
     * 禁用时间
     * @return disabled_time 禁用时间
     */
    public Date getDisabledTime() {
        return disabledTime;
    }

    /**
     * 禁用时间
     * @param disabledTime 禁用时间
     */
    public void setDisabledTime(Date disabledTime) {
        this.disabledTime = disabledTime;
    }

    /**
     * 最近登录时间
     * @return last_login_time 最近登录时间
     */
    public Date getLastLoginTime() {
        return lastLoginTime;
    }

    /**
     * 最近登录时间
     * @param lastLoginTime 最近登录时间
     */
    public void setLastLoginTime(Date lastLoginTime) {
        this.lastLoginTime = lastLoginTime;
    }

    /**
     * 失败认证次数
     * @return failed_login_times 失败认证次数
     */
    public Boolean getFailedLoginTimes() {
        return failedLoginTimes;
    }

    /**
     * 失败认证次数
     * @param failedLoginTimes 失败认证次数
     */
    public void setFailedLoginTimes(Boolean failedLoginTimes) {
        this.failedLoginTimes = failedLoginTimes;
    }

    /**
     * 创建时间
     * @return create_date 创建时间
     */
    public Date getCreateDate() {
        return createDate;
    }

    /**
     * 创建时间
     * @param createDate 创建时间
     */
    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    /**
     * 更新时间
     * @return last_update 更新时间
     */
    public Date getLastUpdate() {
        return lastUpdate;
    }

    /**
     * 更新时间
     * @param lastUpdate 更新时间
     */
    public void setLastUpdate(Date lastUpdate) {
        this.lastUpdate = lastUpdate;
    }

    /**
     * 外键 当前数据生效的系统平台
     * @return system_platform 外键 当前数据生效的系统平台
     */
    public Boolean getSystemPlatform() {
        return systemPlatform;
    }

    /**
     * 外键 当前数据生效的系统平台
     * @param systemPlatform 外键 当前数据生效的系统平台
     */
    public void setSystemPlatform(Boolean systemPlatform) {
        this.systemPlatform = systemPlatform;
    }

    /**
     * 环境标识 1:正式环境  2:预生产环境 用户发版测试
     * @return env 环境标识 1:正式环境  2:预生产环境 用户发版测试
     */
    public Boolean getEnv() {
        return env;
    }

    /**
     * 环境标识 1:正式环境  2:预生产环境 用户发版测试
     * @param env 环境标识 1:正式环境  2:预生产环境 用户发版测试
     */
    public void setEnv(Boolean env) {
        this.env = env;
    }
}