package com.pttl.mobile.manager.domain.dto;

import com.pttl.mobile.manager.domain.entity.MobileRecordLogDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 移动端业务操作记录日志详情dto
 *
 * <AUTHOR>
 * @date 2022/11/11
 **/

@EqualsAndHashCode(callSuper = true)
@Data
public class MobileRecordLogDetailsDTO extends MobileRecordLogDO {


    private static final long serialVersionUID = -8161759153819967864L;
}
