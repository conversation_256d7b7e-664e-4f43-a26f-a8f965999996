package com.pttl.mobile.manager.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 策略部门配置-查询dto
 *
 * <AUTHOR>
 * @date 2022/10/17
 **/
@Data
public class PolicyDepartmentConfigurationDTO {

    /**
     * 部门ID
     */
    @ApiModelProperty(value = "部门ID")
    private Long id;

    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称")
    private String name;

    /**
     * 部门全路径(记录当前目录的所有父级ID)
     */
    @ApiModelProperty(value = "部门全路径(记录当前目录的所有父级ID)")
    private String fullDepartmentId;


    /**
     * 部门全路径
     */
    @ApiModelProperty(value = "部门全路径")
    private String fullDepartmentName;

    /**
     * 当前部门下人数
     */
    @ApiModelProperty(value = "当前部门下人数")
    private Integer departmentUserNum;


    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

}
