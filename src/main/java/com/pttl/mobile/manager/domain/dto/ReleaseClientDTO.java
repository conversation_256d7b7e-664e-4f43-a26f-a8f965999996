package com.pttl.mobile.manager.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class ReleaseClientDTO implements Serializable {
    /**
     * 发布的版本
     */
    private String version;

    /**
     * 下载地址
     */
    private String downloadUrl;

    /**
     * 是否强制更新
     */
    private Boolean isForced;

    /**
     * 发布版本的日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date releaseDate;

    /**
     * 更新内容
     */
    private String description;
}