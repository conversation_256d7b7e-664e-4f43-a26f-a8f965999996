package com.pttl.mobile.manager.domain.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 分页查询基础类
 *
 * <AUTHOR>
 * @date 2022/10/14
 **/
@Data
public class BasePageRequest {
    /**
     * 默认页码和默认分页大小
     */
    public static final int PAGE_NUM_DEFAULT = 1;
    public static final int PAGE_SIZE_DEFAULT = 15;

    /**
     * 当前页
     */
    @ApiModelProperty(value = "当前页")
    private Integer pageNum = PAGE_NUM_DEFAULT;

    /**
     * 每页数量
     */
    @ApiModelProperty(value = "每页数量")
    private Integer pageSize = PAGE_SIZE_DEFAULT;
}
