package com.pttl.mobile.manager.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 用户使用数量统计dto
 *
 * <AUTHOR>
 * @date 2023/1/30
 **/
@ApiModel(value = "ios QRCode (ios二维码数据使用统计)")
@Data
public class UserUsageStatisticsDTO {

    /**
     * 使用数量
     */
    @ApiModelProperty(value = "使用数量")
    private Integer countNum;

    /**
     * 失效时间(使用日期)
     */
    @ApiModelProperty(value = "失效时间(使用日期), 当状态为无效时, 这里才会有值")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date failureTime;
}
