package com.pttl.mobile.manager.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * ios-code兑换详情结果
 *
 * <AUTHOR>
 * @date 2023/3/21
 **/
@Data
@ToString
@ApiModel(value = "ios-code兑换详情结果")
@AllArgsConstructor
@NoArgsConstructor
public class ExchangeDetailsDTO {

    /**
     * dataType: 1今日 2近一周 3近一个月 4近一年 5所有
     */
    public static final int DATA_TYPE_DAY_1 = 1;
    public static final int DATA_TYPE_DAY_7 = 2;
    public static final int DATA_TYPE_DAY_30 = 3;
    public static final int DATA_TYPE_MONTH_12 = 4;
    public static final int DATA_TYPE_ALL = 5;


    /**
     * 已兑换数量
     */
    @ApiModelProperty(value = "已兑换数量")
    private Integer total;

    /**
     * 剩余数量
     */
    /*@ApiModelProperty(value = "剩余数量")
    private Integer residue;*/

    /**
     * 兑换时间
     */
    @ApiModelProperty(value = "兑换时间")
    private String failureTime;
}
