package com.pttl.mobile.manager.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 管理端系统用户表
 *
 * <AUTHOR>
 */
@ApiModel(value = "管理端系统用户")
@Data
public class SystemUserDO implements Serializable {
    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID")
    private Long id;

    /**
     * 用户姓名
     */
    @ApiModelProperty(value = "用户姓名")
    private String name;

    /**
     * 用户密码
     */
    @ApiModelProperty(value = "用户密码")
    private String password;

    /**
     * 用户email
     */
    @ApiModelProperty(value = "用户email")
    private String email;

    /**
     * 手机
     */
    @ApiModelProperty(value = "手机")
    private String mobile;

    /**
     * 用户登录账户
     */
    @ApiModelProperty(value = "用户登录账户")
    private String loginName;

    /**
     * 头像路径
     */
    @ApiModelProperty(value = "头像路径")
    private String avatarPath;

    /**
     * 员工工号
     */
    @ApiModelProperty(value = "员工工号")
    private String employeeNo;

    /**
     * 人员级别
     */
    @ApiModelProperty(value = "人员级别")
    private String personLevel;

    /**
     * 工作地点
     */
    @ApiModelProperty(value = "工作地点")
    private String locality;

    /**
     * 办公电话
     */
    @ApiModelProperty(value = "办公电话")
    private String telephone;

    /**
     * 数据来源(1: 手动创建; 2: 批量导入; 3: 定制导入; 4: AD导入)
     */
    @ApiModelProperty(value = "数据来源(1: 手动创建; 2: 批量导入; 3: 定制导入; 4: AD导入)")
    private Integer source;

    /**
     * 是否删除(0:未删除; 1: 已删除, 默认0:未删除)
     */
    @ApiModelProperty(value = "是否删除(0:未删除; 1: 已删除, 默认0:未删除)")
    private Integer validStatus;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 扩展字段
     */
    @ApiModelProperty(value = "扩展字段")
    private String extension1;

    private static final long serialVersionUID = 1L;
}