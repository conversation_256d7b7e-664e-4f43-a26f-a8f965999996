package com.pttl.mobile.manager.domain.request;

import com.pttl.mobile.manager.util.XssUtil;
import lombok.Data;

import java.io.Serializable;

@Data
public class SwaCreateRequest implements Serializable {
    private String id;

    private String applicationId;

    private String iframeXpath;

    public String getIframeXpath() {
        return XssUtil.filterScript(iframeXpath);
    }

    private Boolean isCustomizedCharUsed = false;

    private Integer loginField;

    private Integer loginPageType;

    private Integer loginType;

    private Integer type;

    private String usernameXpath;

    public String getUsernameXpath() {
        return XssUtil.filterScript(usernameXpath);
    }

    private String passwordXpath;

    public String getPasswordXpath() {
        return XssUtil.filterScript(passwordXpath);
    }

    private String prefix;

    public String getPrefix() {
        return XssUtil.filterScript(prefix);
    }

    private String suffix;

    public String getSuffix() {
        return XssUtil.filterScript(suffix);
    }

    private String url;

    public String getUrl() {
        return XssUtil.filterScript(url);
    }

    private String loginButtonXpath;
}
