package com.pttl.mobile.manager.domain.dto;

import com.pttl.mobile.manager.domain.entity.Strategy2DO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 策略分页数据dto
 *
 * <AUTHOR>
 * @date 2022/10/17
 **/
@Data
@ApiModel(value = "用户关联策略数据信息dto")
@EqualsAndHashCode(callSuper = true)
public class UserStrategyInfoDTO extends Strategy2DO {
    private static final long serialVersionUID = 3061759056725749281L;

    /**
     * 策略 应用数量
     */
    @ApiModelProperty(value = "应用数量")
    private Integer applicationCount;
}
