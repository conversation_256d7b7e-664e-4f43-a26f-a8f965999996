package com.pttl.mobile.manager.domain.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 策略用户配置-查询dto
 *
 * <AUTHOR>
 * @date 2022/10/17
 **/
@Data
public class PolicyUserAndDepartmentConfigurationDTO {

    /**
     * 数据类型 1 部门 2 用户
     */
    public static final Integer DATA_TYPE_DEPARTMENT = 1;
    public static final Integer DATA_TYPE_USER = 2;

    /**
     * 数据ID
     */
    @ApiModelProperty(value = "数据ID, 部门id/用户id")
    private Long id;

    /**
     * 数据名称
     */
    @ApiModelProperty(value = "数据名称")
    private String name;

    /**
     * 所属组织
     */
    @ApiModelProperty(value = "所属组织")
    private String organization;

    /**
     * 数据类型(1:部门, 2:用户)
     *
     * @see PolicyUserAndDepartmentConfigurationDTO#DATA_TYPE_DEPARTMENT
     * @see PolicyUserAndDepartmentConfigurationDTO#DATA_TYPE_USER
     */
    @ApiModelProperty(value = "数据类型(1:部门, 2:用户)")
    private Integer dataType;


}
