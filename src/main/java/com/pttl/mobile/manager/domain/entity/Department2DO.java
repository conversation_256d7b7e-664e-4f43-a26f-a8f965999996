package com.pttl.mobile.manager.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.Date;

import lombok.Data;

/**
 * 部门表
 *
 * <AUTHOR>
 */
@ApiModel(value = "部门实体")
@Data
public class Department2DO implements Serializable {

    /**
     * 树的跟节点
     */
    public static final long ROOT_PARENT_DEPARTMENT_ID = 0L;

    /**
     * 数据来源(1: 手动创建; 2: 批量导入; 3: 定制导入; 4: AD导入)
     */
    public static final int SOURCE_TYPE_MANUAL_CONTROL = 1;
    public static final int SOURCE_TYPE_BATCH_IMPORT = 2;
    public static final int SOURCE_TYPE_CUSTOM_IMPORT = 3;


    /**
     * 是否删除(0:未删除; 1: 已删除, 默认0:未删除)
     */
    public static final int VALID_STATUS_VALID = 0;
    public static final int VALID_STATUS_DEL = 1;

    /**
     * 部门内用户最大设备数 默认为3
     */
    public static final int USER_DEVICE_NUM_3 = 3;

    /**
     * 是否开启最大设备限制数
     */
    public static final int DEVICE_SWITCH_STATUS_ON = 1;


    /**
     * 部门ID
     */
    @ApiModelProperty(value = "部门ID")
    private Long id;

    /**
     * 部门名称
     */
    @ApiModelProperty(value = "部门名称")
    private String name;

    /**
     * 部门描述
     */
    @ApiModelProperty(value = "部门描述")
    private String description;

    /**
     * 父部门ID
     *
     * @see Department2DO#ROOT_PARENT_DEPARTMENT_ID
     */
    @ApiModelProperty(value = "父部门ID")
    private Long parentDepartmentId;

    /**
     * 数据来源(1: 手动创建; 2: 批量导入; 3: 定制导入; 4: AD导入)
     *
     * @see Department2DO#SOURCE_TYPE_MANUAL_CONTROL
     */
    @ApiModelProperty(value = "数据来源(1: 手动创建; 2: 批量导入; 3: 定制导入; 4: AD导入)")
    private Integer source;

    /**
     * 部门全路径(记录当前目录的所有父级ID)
     */
    @ApiModelProperty(value = "部门全路径(记录当前目录的所有父级ID)")
    private String fullDepartmentId;

    /**
     * 部门内用户最大设备数, (暂且保留)
     */
    @ApiModelProperty(value = "部门内用户最大设备数, (暂且保留)")
    private Integer userDeviceNum;

    /**
     * 是否开启最大设备限制数,暂且保留
     */
    @ApiModelProperty(value = "是否开启最大设备限制数,暂且保留")
    private Integer deviceSwitchStatus;

    /**
     * 是否删除(0:未删除; 1: 已删除, 默认0:未删除)
     *
     * @see Department2DO#VALID_STATUS_VALID
     */
    @ApiModelProperty(value = "是否删除(0:未删除; 1: 已删除, 默认0:未删除)")
    private Integer validStatus;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    private static final long serialVersionUID = 1L;
}