package com.pttl.mobile.manager.domain.entity;

import lombok.Data;

import java.io.Serializable;

/**
 * 短信告警关联关系表
 * <AUTHOR>
 */
@Data
public class SmsRelationDO implements Serializable {
    private Long id;

    /**
    * 人员信息关联id
    */
    private Long personnelId;

    /**
    * 短信模板关联id
    */
    private Long modelId;

    /**
    * 告警类型, 如 ios-code
     * @see com.pttl.mobile.manager.constant.SmsWarnTypeEnum
    */
    private String smsType;

    /**
    * 条件或者阀值
    */
    private String smsCondition;

    private static final long serialVersionUID = 1L;
}