package com.pttl.mobile.manager.domain.po;

import java.io.Serializable;
import java.util.Date;

public class ApplicationGroup implements Serializable {
    /**
     * 应用组id
     */
    private String id;

    /**
     * 应用组名称
     */
    private String name;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间
     */
    private Date lastUpdate;

    /**
     * 分组类型 1.默认  0标准
     */
    private Boolean isDefault;

    /**
     * 权重级别 越小排在越前面
     */
    private Integer weight;

    /**
     * 外键 当前数据生效的系统平台
     */
    private Integer systemPlatform;

    /**
     * 环境标识 1:正式环境  2:预生产环境 用户发版测试
     */
    private Integer env;

    /**
     * application_group
     */
    private static final long serialVersionUID = 1L;

    /**
     * 应用组id
     *
     * @return id 应用组id
     */
    public String getId() {
        return id;
    }

    /**
     * 应用组id
     *
     * @param id 应用组id
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * 应用组名称
     *
     * @return name 应用组名称
     */
    public String getName() {
        return name;
    }

    /**
     * 应用组名称
     *
     * @param name 应用组名称
     */
    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    /**
     * 创建时间
     *
     * @return create_date 创建时间
     */
    public Date getCreateDate() {
        return createDate;
    }

    /**
     * 创建时间
     *
     * @param createDate 创建时间
     */
    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    /**
     * 更新时间
     *
     * @return last_update 更新时间
     */
    public Date getLastUpdate() {
        return lastUpdate;
    }

    /**
     * 更新时间
     *
     * @param lastUpdate 更新时间
     */
    public void setLastUpdate(Date lastUpdate) {
        this.lastUpdate = lastUpdate;
    }

    /**
     * 分组类型 1.默认  0标准
     *
     * @return is_default 分组类型 1.默认  0标准
     */
    public Boolean getIsDefault() {
        return isDefault;
    }

    /**
     * 分组类型 1.默认  0标准
     *
     * @param isDefault 分组类型 1.默认  0标准
     */
    public void setIsDefault(Boolean isDefault) {
        this.isDefault = isDefault;
    }

    /**
     * 权重级别 越小排在越前面
     *
     * @return 权重级别 越小排在越前面
     */
    public Integer getWeight() {
        return weight;
    }

    /**
     * 权重级别 越小排在越前面
     *
     * @param weight 权重级别 越小排在越前面
     */
    public void setWeight(Integer weight) {
        this.weight = weight;
    }

    /**
     * 外键 当前数据生效的系统平台
     *
     * @return system_platform 外键 当前数据生效的系统平台
     */
    public Integer getSystemPlatform() {
        return systemPlatform;
    }

    /**
     * 外键 当前数据生效的系统平台
     *
     * @param systemPlatform 外键 当前数据生效的系统平台
     */
    public void setSystemPlatform(Integer systemPlatform) {
        this.systemPlatform = systemPlatform;
    }

    /**
     * 环境标识 1:正式环境  2:预生产环境 用户发版测试
     *
     * @return env 环境标识 1:正式环境  2:预生产环境 用户发版测试
     */
    public Integer getEnv() {
        return env;
    }

    /**
     * 环境标识 1:正式环境  2:预生产环境 用户发版测试
     *
     * @param env 环境标识 1:正式环境  2:预生产环境 用户发版测试
     */
    public void setEnv(Integer env) {
        this.env = env;
    }
}