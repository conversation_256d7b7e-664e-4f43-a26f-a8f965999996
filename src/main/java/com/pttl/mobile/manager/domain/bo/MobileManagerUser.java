package com.pttl.mobile.manager.domain.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.util.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

/**
 * 登陆参数
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "管理端系统用户登陆vo")
public class MobileManagerUser {

    public static String ATTR_USER_NAME = "userName";
    public static String ATTR_CURRENT_ENV = "currentEnv";
    public static String ATTR_SYSTEM_PLAT_FORM = "systemPlatform";

    @ApiModelProperty(value = "用户登陆名称, base64编码")
    private String userName;

    @ApiModelProperty(value = "用户密码, 需rsa加密")
    private String password;

    private Integer currentEnv;

    private Integer systemPlatform;

    @ApiModelProperty(value = "短信验证码")
    private String verifyCode;

    public static boolean isLoggedIn() {
        MobileManagerUser mobileManagerUser = getCurrentUser();
        return mobileManagerUser != null && !StringUtils.isEmpty(mobileManagerUser.getUserName());
    }

    public static MobileManagerUser getCurrentUser() {
        MobileManagerUser mobileManagerUser = new MobileManagerUser();

        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        if (request == null) {
            return mobileManagerUser;
        }

        HttpSession session = request.getSession(false);
        if (session == null) {
            return mobileManagerUser;
        }

        String userName = (String) session.getAttribute(ATTR_USER_NAME);
        Integer currentEnv = (Integer) session.getAttribute(ATTR_CURRENT_ENV);
        Integer systemPlatform = (Integer) session.getAttribute(ATTR_SYSTEM_PLAT_FORM);

        mobileManagerUser.setUserName(userName);
        mobileManagerUser.setCurrentEnv(currentEnv);
        mobileManagerUser.setSystemPlatform(systemPlatform);

        return mobileManagerUser;
    }
}
