package com.pttl.mobile.manager.domain.po;

import java.io.Serializable;
import java.util.Date;

public class Department implements Serializable {
    /**
     * 部门ID
     */
    private String id;

    /**
     * 部门名称
     */
    private String name;

    /**
     * 部门描述
     */
    private String description;

    /**
     * 父部门ID
     */
    private String parentDepartmentId;

    /**
     * 部门全路径
     */
    private String fullDepartmentId;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新时间戳
     */
    private Date lastUpdate;

    /**
     * 外键 当前数据生效的系统平台
     */
    private Boolean systemPlatform;

    /**
     * 环境标识 1:正式环境  2:预生产环境 用户发版测试
     */
    private Boolean env;

    /**
     * department
     */
    private static final long serialVersionUID = 1L;

    /**
     * 部门ID
     * @return id 部门ID
     */
    public String getId() {
        return id;
    }

    /**
     * 部门ID
     * @param id 部门ID
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * 部门名称
     * @return name 部门名称
     */
    public String getName() {
        return name;
    }

    /**
     * 部门名称
     * @param name 部门名称
     */
    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    /**
     * 部门描述
     * @return description 部门描述
     */
    public String getDescription() {
        return description;
    }

    /**
     * 部门描述
     * @param description 部门描述
     */
    public void setDescription(String description) {
        this.description = description == null ? null : description.trim();
    }

    /**
     * 父部门ID
     * @return parent_department_id 父部门ID
     */
    public String getParentDepartmentId() {
        return parentDepartmentId;
    }

    /**
     * 父部门ID
     * @param parentDepartmentId 父部门ID
     */
    public void setParentDepartmentId(String parentDepartmentId) {
        this.parentDepartmentId = parentDepartmentId == null ? null : parentDepartmentId.trim();
    }

    /**
     * 部门全路径
     * @return full_department_id 部门全路径
     */
    public String getFullDepartmentId() {
        return fullDepartmentId;
    }

    /**
     * 部门全路径
     * @param fullDepartmentId 部门全路径
     */
    public void setFullDepartmentId(String fullDepartmentId) {
        this.fullDepartmentId = fullDepartmentId == null ? null : fullDepartmentId.trim();
    }

    /**
     * 创建时间
     * @return create_date 创建时间
     */
    public Date getCreateDate() {
        return createDate;
    }

    /**
     * 创建时间
     * @param createDate 创建时间
     */
    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    /**
     * 更新时间戳
     * @return last_update 更新时间戳
     */
    public Date getLastUpdate() {
        return lastUpdate;
    }

    /**
     * 更新时间戳
     * @param lastUpdate 更新时间戳
     */
    public void setLastUpdate(Date lastUpdate) {
        this.lastUpdate = lastUpdate;
    }

    /**
     * 外键 当前数据生效的系统平台
     * @return system_platform 外键 当前数据生效的系统平台
     */
    public Boolean getSystemPlatform() {
        return systemPlatform;
    }

    /**
     * 外键 当前数据生效的系统平台
     * @param systemPlatform 外键 当前数据生效的系统平台
     */
    public void setSystemPlatform(Boolean systemPlatform) {
        this.systemPlatform = systemPlatform;
    }

    /**
     * 环境标识 1:正式环境  2:预生产环境 用户发版测试
     * @return env 环境标识 1:正式环境  2:预生产环境 用户发版测试
     */
    public Boolean getEnv() {
        return env;
    }

    /**
     * 环境标识 1:正式环境  2:预生产环境 用户发版测试
     * @param env 环境标识 1:正式环境  2:预生产环境 用户发版测试
     */
    public void setEnv(Boolean env) {
        this.env = env;
    }
}