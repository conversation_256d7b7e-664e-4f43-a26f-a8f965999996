package com.pttl.mobile.manager.domain.po;

import java.io.Serializable;
import java.util.Date;

public class ApplicationAdapter implements Serializable {
    /**
     * 应用id
     */
    private String applicationId;

    /**
     * studio中为适配包设置的唯一名称
     */
    private String scope;

    /**
     * manifest文件内容
     */
    private String manifest;

    /**
     * 适配包对应的运行时主版本号
     */
    private String runtimePrimaryVersion;

    /**
     * 适配包对应的运行时具体版本
     */
    private String runtimeVersion;

    /**
     * 是否使用指定的运行时（0：不是，如果有新的运行时，客户端需要更新；1：是，不进行更新）
     */
    private Boolean isSpecificRuntimeUsed;

    /**
     * 更新时间戳
     */
    private Date lastUpdate;

    /**
     * 外键 当前数据生效的系统平台
     */
    private Integer systemPlatform;

    /**
     * 环境标识 1:正式环境  2:预生产环境 用户发版测试
     */
    private Integer env;

    /**
     * application_adapter
     */
    private static final long serialVersionUID = 1L;

    /**
     * 应用id
     * @return application_id 应用id
     */
    public String getApplicationId() {
        return applicationId;
    }

    /**
     * 应用id
     * @param applicationId 应用id
     */
    public void setApplicationId(String applicationId) {
        this.applicationId = applicationId == null ? null : applicationId.trim();
    }

    /**
     * studio中为适配包设置的唯一名称
     * @return scope studio中为适配包设置的唯一名称
     */
    public String getScope() {
        return scope;
    }

    /**
     * studio中为适配包设置的唯一名称
     * @param scope studio中为适配包设置的唯一名称
     */
    public void setScope(String scope) {
        this.scope = scope == null ? null : scope.trim();
    }

    /**
     * manifest文件内容
     * @return manifest manifest文件内容
     */
    public String getManifest() {
        return manifest;
    }

    /**
     * manifest文件内容
     * @param manifest manifest文件内容
     */
    public void setManifest(String manifest) {
        this.manifest = manifest == null ? null : manifest.trim();
    }

    /**
     * 适配包对应的运行时主版本号
     * @return runtime_primary_version 适配包对应的运行时主版本号
     */
    public String getRuntimePrimaryVersion() {
        return runtimePrimaryVersion;
    }

    /**
     * 适配包对应的运行时主版本号
     * @param runtimePrimaryVersion 适配包对应的运行时主版本号
     */
    public void setRuntimePrimaryVersion(String runtimePrimaryVersion) {
        this.runtimePrimaryVersion = runtimePrimaryVersion == null ? null : runtimePrimaryVersion.trim();
    }

    /**
     * 适配包对应的运行时具体版本
     * @return runtime_version 适配包对应的运行时具体版本
     */
    public String getRuntimeVersion() {
        return runtimeVersion;
    }

    /**
     * 适配包对应的运行时具体版本
     * @param runtimeVersion 适配包对应的运行时具体版本
     */
    public void setRuntimeVersion(String runtimeVersion) {
        this.runtimeVersion = runtimeVersion == null ? null : runtimeVersion.trim();
    }

    /**
     * 是否使用指定的运行时（0：不是，如果有新的运行时，客户端需要更新；1：是，不进行更新）
     * @return is_specific_runtime_used 是否使用指定的运行时（0：不是，如果有新的运行时，客户端需要更新；1：是，不进行更新）
     */
    public Boolean getIsSpecificRuntimeUsed() {
        return isSpecificRuntimeUsed;
    }

    /**
     * 是否使用指定的运行时（0：不是，如果有新的运行时，客户端需要更新；1：是，不进行更新）
     * @param isSpecificRuntimeUsed 是否使用指定的运行时（0：不是，如果有新的运行时，客户端需要更新；1：是，不进行更新）
     */
    public void setIsSpecificRuntimeUsed(Boolean isSpecificRuntimeUsed) {
        this.isSpecificRuntimeUsed = isSpecificRuntimeUsed;
    }

    /**
     * 更新时间戳
     * @return last_update 更新时间戳
     */
    public Date getLastUpdate() {
        return lastUpdate;
    }

    /**
     * 更新时间戳
     * @param lastUpdate 更新时间戳
     */
    public void setLastUpdate(Date lastUpdate) {
        this.lastUpdate = lastUpdate;
    }

    /**
     * 外键 当前数据生效的系统平台
     * @return system_platform 外键 当前数据生效的系统平台
     */
    public Integer getSystemPlatform() {
        return systemPlatform;
    }

    /**
     * 外键 当前数据生效的系统平台
     * @param systemPlatform 外键 当前数据生效的系统平台
     */
    public void setSystemPlatform(Integer systemPlatform) {
        this.systemPlatform = systemPlatform;
    }

    /**
     * 环境标识 1:正式环境  2:预生产环境 用户发版测试
     * @return env 环境标识 1:正式环境  2:预生产环境 用户发版测试
     */
    public Integer getEnv() {
        return env;
    }

    /**
     * 环境标识 1:正式环境  2:预生产环境 用户发版测试
     * @param env 环境标识 1:正式环境  2:预生产环境 用户发版测试
     */
    public void setEnv(Integer env) {
        this.env = env;
    }
}