package com.pttl.mobile.manager.domain.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 移动端业务操作记录日志表
 *
 * <AUTHOR>
 */
@ApiModel(value = "移动端业务操作记录日志do")
@Data
public class MobileRecordLogDO implements Serializable {
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * app版本
     */
    @ApiModelProperty(value = "app版本")
    private String appVersion;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String description;

    /**
     * 登陆名称
     */
    @ApiModelProperty(value = "登陆名称")
    private String loginName;

    /**
     * 登陆时间
     */
    @ApiModelProperty(value = "登陆时间")
    private Date loginTime;

    /**
     * 系统版本
     */
    @ApiModelProperty(value = "系统版本")
    private String model;

    /**
     * 模块
     */
    @ApiModelProperty(value = "模块")
    private String module;

    /**
     * 发生时间
     */
    @ApiModelProperty(value = "发生时间")
    private Date occurTime;

    /**
     * 平台 在线商城/在线办公
     */
    @ApiModelProperty(value = "平台 在线商城/在线办公")
    private String platform;

    /**
     * 手机系统 Android/IOS
     */
    @ApiModelProperty(value = "手机系统 Android/IOS")
    private String source;

    /**
     * 子模块
     */
    @ApiModelProperty(value = "子模块")
    private String subModule;

    /**
     * 系统登陆名称
     */
    @ApiModelProperty(value = "系统登陆名称")
    private String systemLoginName;

    /**
     * vpn登陆名称
     */
    @ApiModelProperty(value = "vpn登陆名称")
    private String vpnLoginName;

    /**
     * 使用次数
     */
    @ApiModelProperty(value = "使用次数")
    private Integer timeUsed;

    /**
     * 类型 Info/Error/success
     */
    @ApiModelProperty(value = "类型 Info/Error/success")
    private String type;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 扩展字段
     */
    @ApiModelProperty(value = "扩展字段")
    private String extension;

    private static final long serialVersionUID = 1L;
}