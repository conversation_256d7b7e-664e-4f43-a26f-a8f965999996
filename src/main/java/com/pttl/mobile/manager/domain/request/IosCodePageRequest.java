package com.pttl.mobile.manager.domain.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.pttl.mobile.manager.domain.entity.IosCodeDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "分页查询参数vo")
public class IosCodePageRequest extends BasePageRequest implements Serializable {

    private static final long serialVersionUID = 5961592854851528252L;

    /**
     * @see IosCodeDO#VALID_STATUS_VALID
     */
    @ApiModelProperty(value = "是否有效: 0 : 有效, 1 : 有效")
    private Integer validStatus;

    /**
     * 导入开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "导入开始时间范围")
    private Date importStartTime;

    /**
     * 导入截止时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "导入截止时间范围")
    private Date importEndTime;

    /**
     * 失效开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "失效开始时间范围")
    private Date failureStartTime;

    /**
     * 失效截止时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "失效截止时间范围")
    private Date failureEndTime;


}
