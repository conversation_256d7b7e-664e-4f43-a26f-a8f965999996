package com.pttl.mobile.manager.domain.dto;

import com.pttl.mobile.manager.domain.entity.SmsPersonnelDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 人员信息dto
 *
 * <AUTHOR>
 * @date 2024/11/25
 */

@EqualsAndHashCode(callSuper = true)
@Data
public class SmsPersonnelDTO extends SmsPersonnelDO {

    /**
     * 短信模板关联id
     */
    private Long modelId;

    /**
     * 条件或者阀值
     */
    private String smsCondition;
}
