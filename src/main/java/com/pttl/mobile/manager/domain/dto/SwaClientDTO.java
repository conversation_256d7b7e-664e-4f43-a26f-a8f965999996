package com.pttl.mobile.manager.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class SwaClientDTO implements Serializable {
    // 需要通过应用id查一下应用补充字段
    private String logoUrl;

    // 需要通过应用id查一下应用补充字段
    private String name;

    private String applicationId;

    private String applicationName;

    private String iframeXpath;

    private Boolean isCustomizedCharUsed = false;

    private Integer loginField;

    private String loginButtonXpath;

    private String usernameXpath;

    private String passwordXpath;

    private String prefix;

    private String suffix;

    private Integer type;

    private Integer loginType;

    private Integer loginPageType;

    private String url;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastUpdate;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;
}
