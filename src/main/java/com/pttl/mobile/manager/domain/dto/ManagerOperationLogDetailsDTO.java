package com.pttl.mobile.manager.domain.dto;

import com.pttl.mobile.manager.domain.entity.ManagerOperationLogDO;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 管理端 操作日志详情结果
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "管理端 操作日志详情结果dto")
@EqualsAndHashCode(callSuper = false)
public class ManagerOperationLogDetailsDTO extends ManagerOperationLogDO
        implements Serializable {
    private static final long serialVersionUID = 1L;

}