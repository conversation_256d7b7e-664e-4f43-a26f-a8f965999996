package com.pttl.mobile.manager.domain.dto;

import java.io.Serializable;
import java.util.Date;

public class PolicyApplicationConfigurationDTO implements Serializable {
    /**
     * 应用id
     */
    private String id;

    /**
     * 应用组id
     */
    private String applicationGroupId;

    /**
     * 应用名称
     */
    private String name;

    /**
     * 应用描述
     */
    private String description;

    /**
     * 应用图标地址
     */
    private String logoUrl;

    /**
     * 应用包上传地址
     */
    private String packageUrl;

    /**
     * 应用包的名称(包括扩展名)
     */
    private String packageName;

    /**
     * 在线应用,适配应用地址
     */
    private String address;

    /**
     * 应用内网IP地址
     */
    private String innerAddress;

    /**
     * 应用类型(1:在线应用; 2: 适配应用;), 不同的类型有可能存在子表
     */
    private Integer type;

    /**
     * 应用状态(0:隐藏 1:显示)
     */
    private Boolean isVisible;

    /**
     *
     */
    private Date createDate;

    /**
     *
     */
    private Date lastUpdate;

    /**
     * 唯一id 每次更新和新建都会变
     */
    private String version;

    /**
     * 外键 当前数据生效的系统平台
     */
    private Integer systemPlatform;

    /**
     * 环境标识 1:正式环境  2:预生产环境 用户发版测试
     */
    private Integer env;

    /**
     * application
     */
    private static final long serialVersionUID = 1L;

    /**
     * 应用id
     *
     * @return id 应用id
     */
    public String getId() {
        return id;
    }

    /**
     * 应用id
     *
     * @param id 应用id
     */
    public void setId(String id) {
        this.id = id == null ? null : id.trim();
    }

    /**
     * 应用组id
     *
     * @return application_group_id 应用组id
     */
    public String getApplicationGroupId() {
        return applicationGroupId;
    }

    /**
     * 应用组id
     *
     * @param applicationGroupId 应用组id
     */
    public void setApplicationGroupId(String applicationGroupId) {
        this.applicationGroupId = applicationGroupId == null ? null : applicationGroupId.trim();
    }

    /**
     * 应用名称
     *
     * @return name 应用名称
     */
    public String getName() {
        return name;
    }

    /**
     * 应用名称
     *
     * @param name 应用名称
     */
    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    /**
     * 应用描述
     *
     * @return description 应用描述
     */
    public String getDescription() {
        return description;
    }

    /**
     * 应用描述
     *
     * @param description 应用描述
     */
    public void setDescription(String description) {
        this.description = description == null ? null : description.trim();
    }

    /**
     * 应用图标地址
     *
     * @return logo_url 应用图标地址
     */
    public String getLogoUrl() {
        return logoUrl;
    }

    /**
     * 应用图标地址
     *
     * @param logoUrl 应用图标地址
     */
    public void setLogoUrl(String logoUrl) {
        this.logoUrl = logoUrl == null ? null : logoUrl.trim();
    }

    /**
     * 应用包上传地址
     *
     * @return package_url 应用包上传地址
     */
    public String getPackageUrl() {
        return packageUrl;
    }

    /**
     * 应用包上传地址
     *
     * @param packageUrl 应用包上传地址
     */
    public void setPackageUrl(String packageUrl) {
        this.packageUrl = packageUrl == null ? null : packageUrl.trim();
    }

    /**
     * 应用包的名称(包括扩展名)
     *
     * @return package_name 应用包的名称(包括扩展名)
     */
    public String getPackageName() {
        return packageName;
    }

    /**
     * 应用包的名称(包括扩展名)
     *
     * @param packageName 应用包的名称(包括扩展名)
     */
    public void setPackageName(String packageName) {
        this.packageName = packageName == null ? null : packageName.trim();
    }

    /**
     * 在线应用,适配应用地址
     *
     * @return address 在线应用,适配应用地址
     */
    public String getAddress() {
        return address;
    }

    /**
     * 在线应用,适配应用地址
     *
     * @param address 在线应用,适配应用地址
     */
    public void setAddress(String address) {
        this.address = address == null ? null : address.trim();
    }

    /**
     * 应用内网IP地址
     *
     * @return inner_address 应用内网IP地址
     */
    public String getInnerAddress() {
        return innerAddress;
    }

    /**
     * 应用内网IP地址
     *
     * @param innerAddress 应用内网IP地址
     */
    public void setInnerAddress(String innerAddress) {
        this.innerAddress = innerAddress == null ? null : innerAddress.trim();
    }

    /**
     * 应用类型(1:在线应用; 2: 适配应用;), 不同的类型有可能存在子表
     *
     * @return type 应用类型(1:在线应用; 2: 适配应用;), 不同的类型有可能存在子表
     */
    public Integer getType() {
        return type;
    }

    /**
     * 应用类型(1:在线应用; 2: 适配应用;), 不同的类型有可能存在子表
     *
     * @param type 应用类型(1:在线应用; 2: 适配应用;), 不同的类型有可能存在子表
     */
    public void setType(Integer type) {
        this.type = type;
    }

    /**
     * 应用状态(0:隐藏 1:显示)
     *
     * @return is_visible 应用状态(0:隐藏 1:显示)
     */
    public Boolean getIsVisible() {
        return isVisible;
    }

    /**
     * 应用状态(0:隐藏 1:显示)
     *
     * @param isVisible 应用状态(0:隐藏 1:显示)
     */
    public void setIsVisible(Boolean isVisible) {
        this.isVisible = isVisible;
    }

    /**
     * @return create_date
     */
    public Date getCreateDate() {
        return createDate;
    }

    /**
     * @param createDate
     */
    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    /**
     * @return last_update
     */
    public Date getLastUpdate() {
        return lastUpdate;
    }

    /**
     * @param lastUpdate
     */
    public void setLastUpdate(Date lastUpdate) {
        this.lastUpdate = lastUpdate;
    }

    /**
     * @return version
     */
    public String getVersion() {
        return version;
    }

    /**
     * @param version
     */
    public void setVersion(String version) {
        this.version = version;
    }

    /**
     * 外键 当前数据生效的系统平台
     *
     * @return system_platform 外键 当前数据生效的系统平台
     */
    public Integer getSystemPlatform() {
        return systemPlatform;
    }

    /**
     * 外键 当前数据生效的系统平台
     *
     * @param systemPlatform 外键 当前数据生效的系统平台
     */
    public void setSystemPlatform(Integer systemPlatform) {
        this.systemPlatform = systemPlatform;
    }

    /**
     * 环境标识 1:正式环境  2:预生产环境 用户发版测试
     *
     * @return env 环境标识 1:正式环境  2:预生产环境 用户发版测试
     */
    public Integer getEnv() {
        return env;
    }

    /**
     * 环境标识 1:正式环境  2:预生产环境 用户发版测试
     *
     * @param env 环境标识 1:正式环境  2:预生产环境 用户发版测试
     */
    public void setEnv(Integer env) {
        this.env = env;
    }
}