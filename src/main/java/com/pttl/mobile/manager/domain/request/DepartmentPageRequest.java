package com.pttl.mobile.manager.domain.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 部门分页查询参数dto
 *
 * <AUTHOR>
 * @date 2023/2/23
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "部门分页请求参数")
public class DepartmentPageRequest extends BasePageRequest implements Serializable {

    @ApiModelProperty(value = "部门ID")
    private Long departmentId;
}
