package com.pttl.mobile.manager.domain.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;


/**
 * 用户关联应用 查询结果vo
 *
 * <AUTHOR>
 * @date 2023/2/6
 **/

@Data
@EqualsAndHashCode
@ApiModel(value = "用户关联应用-查询结果dto")
public class ApplicationInfoForUserDTO {

    /**
     * 应用id
     */
    @ApiModelProperty(value = "应用id")
    private String id;

    /**
     * 应用组id
     */
    @ApiModelProperty(value = "应用组id")
    @JsonIgnore
    private String applicationGroupId;

    /**
     * 应用名称
     */
    @ApiModelProperty(value = "应用名称")
    private String name;

    /**
     * 应用描述
     */
    @ApiModelProperty(value = "应用描述")
    private String description;

    /**
     * 应用图标地址
     */
    @ApiModelProperty(value = "应用图标地址")
    private String logoUrl;

    /**
     * 应用包上传地址
     */
    @ApiModelProperty(value = "应用包上传地址")
    private String packageUrl;

    /**
     * 应用包的名称(包括扩展名)
     */
    @ApiModelProperty(value = "应用包的名称(包括扩展名)")
    private String packageName;

    /**
     * 在线应用,适配应用地址
     */
    @ApiModelProperty(value = "在线应用,适配应用地址")
    private String address;

    /**
     * 应用内网IP地址
     */
    @ApiModelProperty(value = "应用内网IP地址")
    private String innerAddress;

    /**
     * 应用类型(1:在线应用; 2: 适配应用;), 不同的类型有可能存在子表
     */
    @ApiModelProperty(value = "应用类型(1:在线应用; 2: 适配应用;), 不同的类型有可能存在子表")
    private Integer type;

    /**
     * 应用状态(0:隐藏 1:显示)
     */
    @ApiModelProperty(value = "应用状态(0:隐藏 1:显示)")
    private Boolean isVisible;

    /**
     * 当前数据生效的系统平台
     */
    @ApiModelProperty(value = "当前数据生效的系统平台")
    private Integer systemPlatform;

    /**
     * 更新修改时间
     */
    @ApiModelProperty(value = "更新修改时间")
    private Date lastUpdate;

    /**
     * 应用组名
     */
    @ApiModelProperty(value = "应用组名")
    private String applicationGroupName;


    /**
     * 排序级别 越小排在越前面
     */
    @ApiModelProperty(value = "排序级别 越小排在越前面")
    private Integer weight;


}