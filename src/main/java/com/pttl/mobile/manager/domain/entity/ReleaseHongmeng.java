package com.pttl.mobile.manager.domain.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 发布版本表
 */
@Data
public class ReleaseHongmeng implements Serializable {
    /**
    * 发布版本id
    */
    private String id;

    /**
    * 发布的版本
    */
    private String version;

    /**
    * 下载地址
    */
    private String downloadUrl;

    /**
    * 外键 发版的系统平台 1在线办公 2商城
    */
    private Integer systemPlatform;

    /**
    * 外键 更新的类型 1 客户端或者 2 适配包
    */
    private Integer type;

    /**
    * 外键 操作系统 1 Android或者 2 IOS
    */
    private Integer operatingSystem;

    /**
    * 是否强制更新
    */
    private Boolean isForced;

    /**
    * 发布版本的日期
    */
    private Date releaseDate;

    /**
    * 更新内容
    */
    private String description;

    /**
    * 环境标识 1:正式环境  2:预生产环境 用户发版测试
    */
    private Integer env;

    private static final long serialVersionUID = 1L;
}