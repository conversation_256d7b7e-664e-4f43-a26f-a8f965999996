package com.pttl.mobile.manager.domain.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 前端日志dto
 *
 * <AUTHOR>
 * @date 2024/6/6
 */

@NoArgsConstructor
@Data
public class LogDataDTO {

    @JsonProperty("logId")
    private Long logId;
    @JsonProperty("serverName")
    private String serverName;
    @JsonProperty("userName")
    private String userName;
    @JsonProperty("logDate")
    private Long logDate;
    @JsonProperty("status")
    private String status;
    @JsonProperty("appVersion")
    private String appVersion;
    @JsonProperty("deviceInfo")
    private String deviceInfo;
    @JsonProperty("ihrName")
    private String ihrName;
    @JsonProperty("logStartTime")
    private Long logStartTime;
    @JsonProperty("systemType")
    private String systemType;
    @JsonProperty("systemVersion")
    private String systemVersion;
    @JsonProperty("nwePageLoad")
    private Object nwePageLoad;
    @JsonProperty("logDetail")
    private Object logDetail;
}
