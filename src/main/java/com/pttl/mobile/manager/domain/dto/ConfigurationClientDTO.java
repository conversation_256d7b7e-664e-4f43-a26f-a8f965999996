package com.pttl.mobile.manager.domain.dto;

import com.pttl.mobile.manager.domain.po.Runtime;
import com.pttl.mobile.manager.domain.dto.OnlinePreviewDTO;
import lombok.Data;

import java.util.List;

@Data
public class ConfigurationClientDTO {
    private List<RuntimeClientDTO> runtimes;

    private OnlinePreviewDTO docOnlinePreview;

    // 当前环境是否为预生产 预生产:关闭外网 使用内网进行测试
    private Boolean isInternal = false;
}
