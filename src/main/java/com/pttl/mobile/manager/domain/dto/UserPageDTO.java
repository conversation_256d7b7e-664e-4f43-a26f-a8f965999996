package com.pttl.mobile.manager.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 用户分页DTO
 *
 * <AUTHOR>
 * @date 2022/10/14
 **/
@Data
@ApiModel(value = "用户分页信息DTO实体")
public class UserPageDTO {

    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID")
    private Long id;

    /**
     * 用户email
     */
    @ApiModelProperty(value = "用户email")
    private String email;

    /**
     * 用户登录账户
     */
    @ApiModelProperty(value = "用户登录账户")
    private String loginName;

    /**
     * 是否已经激活(0:未激活, 1:已激活)
     */
    @ApiModelProperty(value = "是否已经激活(0:未激活, 1:已激活)")
    private Integer activatedStatus;

    /**
     * 是否被禁用(0:未禁用,1:已禁用)
     */
    @ApiModelProperty(value = "是否被禁用(0:未禁用,1:已禁用)")
    private Integer disabledStatus;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
}
