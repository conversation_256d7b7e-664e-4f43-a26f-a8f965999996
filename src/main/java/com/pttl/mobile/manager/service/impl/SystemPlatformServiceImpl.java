package com.pttl.mobile.manager.service.impl;

import com.pttl.mobile.manager.dao.SystemPlatformMapper;
import com.pttl.mobile.manager.lib.ResponseMessage;
import com.pttl.mobile.manager.domain.po.SystemPlatform;
import com.pttl.mobile.manager.service.SystemPlatformService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class SystemPlatformServiceImpl implements SystemPlatformService {

    @Autowired
    private SystemPlatformMapper systemPlatformMapper;

    @Override
    public ResponseMessage<List<SystemPlatform>> getSystemPlatform() {
        return ResponseMessage.ok(systemPlatformMapper.getSystemPlatform());
    }
}
