package com.pttl.mobile.manager.service;

import com.github.pagehelper.PageInfo;
import com.pttl.mobile.manager.domain.dto.UserDTO;
import com.pttl.mobile.manager.domain.dto.UserDetailsDTO;
import com.pttl.mobile.manager.domain.dto.UserPageDTO;
import com.pttl.mobile.manager.domain.entity.UserDO;
import com.pttl.mobile.manager.domain.request.UserPageRequest;
import com.pttl.mobile.manager.domain.request.UserSaveRequest;
import com.pttl.mobile.manager.domain.request.UserUpdateRequest;
import com.pttl.mobile.manager.excel.dto.ImportUserDataDTO;

import java.util.List;

public interface UserService {


    int deleteByPrimaryKey(Long id);

    int insert(UserDO record);

    int insertSelective(UserDO record);

    UserDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(UserDO record);

    int updateByPrimaryKey(UserDO record);

    int updateBatch(List<UserDO> list);

    int updateBatchSelective(List<UserDO> list);

    int batchInsert(List<UserDO> list);

    /**
     * 分页查询用户信息
     *
     * @param pageRequest
     * @return
     */
    PageInfo<UserPageDTO> pageInfo(UserPageRequest pageRequest);

    /**
     * 根据用户id获取用户详情信息
     *
     * @param userId
     * @return
     */
    UserDetailsDTO getUserDetails(Long userId);

    /**
     * 禁用/恢复 用户
     *
     * @param userId
     */
    void userDisabled(Long userId);

    /**
     * 检查用户并激活
     *
     * @param userId
     */
    void userCheckAndActivate(Long userId);

    /**
     * 删除用户(真实删除)
     *
     * @param ids
     */
    void userRemove(List<Long> ids);

    /**
     * 创建用户
     *
     * @param userSaveRequest
     */
    void createUser(UserSaveRequest userSaveRequest);

    /**
     * 更新用户信息
     *
     * @param userUpdateRequest
     */
    void updateUser(UserUpdateRequest userUpdateRequest);

    /**
     * 根据用户登录名称获取用户信息
     *
     * @param loginName
     * @return
     */
    UserDTO selectByLoginName(String loginName);

    /**
     * 根据部门id统计部门内人数
     *
     * @param id
     * @return
     */
    Integer countByDepartmentId(Long id);

    /**
     * 保存导入的用户信息
     *
     * @param importUserList
     */
    void saveImportUserList(List<ImportUserDataDTO> importUserList);

    /**
     * 修改密码
     *
     * @param id
     * @param decrypt 明文密码
     */
    void updatePassword(Long id, String decrypt);
}
