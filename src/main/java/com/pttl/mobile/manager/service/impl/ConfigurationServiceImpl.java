package com.pttl.mobile.manager.service.impl;

import com.pttl.mobile.manager.dao.ConfigurationMapper;
import com.pttl.mobile.manager.lib.ResponseMessage;
import com.pttl.mobile.manager.domain.po.Configuration;
import com.pttl.mobile.manager.domain.dto.ConfigurationDTO;
import com.pttl.mobile.manager.service.ConfigurationService;
import com.pttl.mobile.manager.util.CollectionUtil;
import com.pttl.mobile.manager.util.JsonUtil;
import com.pttl.mobile.manager.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ConfigurationServiceImpl implements ConfigurationService {
    @Autowired
    private ConfigurationMapper configurationMapper;

    @Override
    public ResponseMessage<List<ConfigurationDTO<Object>>> getConfiguration() {
        List<ConfigurationDTO<Object>> res = null;
        // 获取所有configuration
        List<Configuration> configurations = configurationMapper.getConfiguration();
        if (!CollectionUtil.isEmpty(configurations)) {
            res = configurations.stream().map(configuration -> {
                ConfigurationDTO<Object> cd = new ConfigurationDTO<>();
                cd.setName(configuration.getName());
                cd.setDescription(configuration.getDescription());
                cd.setValue(JsonUtil.fromJson(configuration.getValue(), Object.class));

                return cd;
            }).collect(Collectors.toList());
        }

        return ResponseMessage.ok(res);
    }

    @Override
    public <T> ConfigurationDTO<T> getConfigurationByName(String name, Class<T> clazz) {
        ConfigurationDTO<T> res = new ConfigurationDTO<>();
        if (!StringUtil.isEmpty(name)) {
            Configuration configuration = configurationMapper.getConfigurationByName(name);
            if (configuration != null) {
                res.setName(configuration.getName());
                res.setDescription(configuration.getDescription());
                res.setValue(JsonUtil.fromJson(configuration.getValue(), clazz));
            }
        }

        return res;
    }

    @Override
    public ResponseMessage<Boolean> insertOrUpdate(ConfigurationDTO<Object> setting) {
        if (setting != null && !StringUtil.isEmpty(setting.getName()) && setting.getValue() != null) {
            Configuration configuration = new Configuration();
            configuration.setName(setting.getName());
            configuration.setDescription(setting.getDescription());
            configuration.setValue(JsonUtil.toJson(setting.getValue()));

            // 如果存在则更新 如果不存在则插入 name+systemPlatform联合主键
            configurationMapper.insertOrUpdate(configuration);

            return ResponseMessage.ok(true);
        }

        return ResponseMessage.error("参数错误");
    }
}
