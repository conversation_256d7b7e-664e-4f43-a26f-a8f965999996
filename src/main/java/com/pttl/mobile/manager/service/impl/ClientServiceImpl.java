package com.pttl.mobile.manager.service.impl;

import com.pttl.mobile.manager.constant.EnvEnum;
import com.pttl.mobile.manager.dao.ClientMapper;
import com.pttl.mobile.manager.dao.ReleaseTypeMapper;
import com.pttl.mobile.manager.domain.dto.*;
import com.pttl.mobile.manager.domain.po.ReleaseType;
import com.pttl.mobile.manager.lib.ContextUtil;
import com.pttl.mobile.manager.lib.ResponseMessage;
import com.pttl.mobile.manager.service.ClientService;
import com.pttl.mobile.manager.util.CollectionUtil;
import com.pttl.mobile.manager.util.JsonUtil;
import com.pttl.mobile.manager.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ClientServiceImpl implements ClientService {

    @Autowired
    private ReleaseTypeMapper releaseTypeMapper;

    @Autowired
    private ClientMapper clientMapper;

    @Value("${deployUrl}")
    private String deployUrl;

    @Override
    public ResponseMessage<ReleaseClientDTO> getReleaseLastversion(Integer type, Integer operatingSystem) {
        List<ReleaseType> releaseTypes = releaseTypeMapper.getType();
        Optional<ReleaseType> releaseTypeOptional = releaseTypes.stream().filter(releaseType -> releaseType.getId().equals(type)).findFirst();
        boolean isClientUpdate = false;
        if (releaseTypeOptional.isPresent()) {
            String name = releaseTypeOptional.get().getName();
            if (!StringUtil.isEmpty(name) && name.contains("客户端")) {
                isClientUpdate = true;
            }
        }
        ReleaseClientDTO releaseClientDTO;
        // 如果是客户端更新的话 操作系统是必填参数
        if (isClientUpdate) {
            if (operatingSystem == null) {
                return ResponseMessage.error("请求参数参数有误,参数operatingSystem不能为空");
            }

            releaseClientDTO = clientMapper.getReleaseLastVersion(type, operatingSystem);
        } else {
            releaseClientDTO = clientMapper.getReleaseLastVersion(type, null);
        }

        if (releaseClientDTO != null) {
            releaseClientDTO.setDownloadUrl(getStaticUrl(releaseClientDTO.getDownloadUrl()));
        }

        return ResponseMessage.ok(releaseClientDTO);
    }

    @Override
    public ResponseMessage<ReleaseClientDTO> getReleaseMallLastversion(Integer type, Integer operatingSystem) {
        List<ReleaseType> releaseTypes = releaseTypeMapper.getType();
        Optional<ReleaseType> releaseTypeOptional = releaseTypes.stream().filter(releaseType -> releaseType.getId().equals(type)).findFirst();
        boolean isClientUpdate = false;
        if (releaseTypeOptional.isPresent()) {
            String name = releaseTypeOptional.get().getName();
            if (!StringUtil.isEmpty(name) && name.contains("客户端")) {
                isClientUpdate = true;
            }
        }
        ReleaseClientDTO releaseClientDTO;
        // 如果是客户端更新的话 操作系统是必填参数
        if (isClientUpdate) {
            if (operatingSystem == null) {
                return ResponseMessage.error("请求参数参数有误,参数operatingSystem不能为空");
            }

            releaseClientDTO = clientMapper.getReleaseMallLastVersion(type, operatingSystem);
        } else {
            releaseClientDTO = clientMapper.getReleaseMallLastVersion(type, null);
        }

        if (releaseClientDTO != null) {
            releaseClientDTO.setDownloadUrl(getStaticUrl(releaseClientDTO.getDownloadUrl()));
        }

        return ResponseMessage.ok(releaseClientDTO);
    }

    @Override
    public ResponseMessage<ReleaseClientDTO> getReleaseHongmengLastversion(Integer type, Integer operatingSystem) {
        List<ReleaseType> releaseTypes = releaseTypeMapper.getType();
        Optional<ReleaseType> releaseTypeOptional = releaseTypes.stream().filter(releaseType -> releaseType.getId().equals(type)).findFirst();
        boolean isClientUpdate = false;
        if (releaseTypeOptional.isPresent()) {
            String name = releaseTypeOptional.get().getName();
            if (!StringUtil.isEmpty(name) && name.contains("客户端")) {
                isClientUpdate = true;
            }
        }
        ReleaseClientDTO releaseClientDTO;
        // 如果是客户端更新的话 操作系统是必填参数
        if (isClientUpdate) {
            if (operatingSystem == null) {
                return ResponseMessage.error("请求参数参数有误,参数operatingSystem不能为空");
            }

            releaseClientDTO = clientMapper.getReleaseHongmengLastVersion(type, operatingSystem);
        } else {
            releaseClientDTO = clientMapper.getReleaseHongmengLastVersion(type, null);
        }

        return ResponseMessage.ok(releaseClientDTO);
    }

    @Override
    public ResponseMessage<ConfigurationClientDTO> getConfiguration() {
        ConfigurationClientDTO cr = new ConfigurationClientDTO();
        List<RuntimeClientDTO> runtimes = clientMapper.getRuntime();
        if (!CollectionUtil.isEmpty(runtimes)) {
            runtimes.forEach(runtime -> runtime.setPackageUrl(getStaticUrl(runtime.getPackageUrl())));
        }
        cr.setRuntimes(runtimes);
        String value = clientMapper.getConfigurationValueByName("onlinePreview");
        cr.setDocOnlinePreview(JsonUtil.fromJson(value, OnlinePreviewDTO.class));
        cr.setIsInternal(ContextUtil.currentEnv() == EnvEnum.PRE.getEnv());

        return ResponseMessage.ok(cr);
    }

    @Override
    public ResponseMessage<List<ApplicationClientDTO>> getApplication() {
        List<ApplicationClientDTO> res = new ArrayList<>();
        List<ApplicationClientItemDTO> allApplications = clientMapper.getApplication();
        if (!CollectionUtil.isEmpty(allApplications)) {
            // 根据分组ID进行分组
            Map<String, List<ApplicationClientItemDTO>> allApplicationGroupMap = allApplications.stream().collect(Collectors.groupingBy(ApplicationClientItemDTO::getApplicationGroupId));
            // 循环分组
            for (Map.Entry<String, List<ApplicationClientItemDTO>> allApplicationGroupEntry : allApplicationGroupMap.entrySet()) {
                String mapKey = allApplicationGroupEntry.getKey();
                List<ApplicationClientItemDTO> applications = allApplicationGroupEntry.getValue();
                ApplicationClientDTO clientDTO = new ApplicationClientDTO();
                // 分组ID
                clientDTO.setId(mapKey);
                // 分组名称
                clientDTO.setName(applications.get(0).getApplicationGroupName());
                // 分组权重
                clientDTO.setWeight(applications.get(0).getWeight());
                applications.forEach(value -> {
                    value.setLogoUrl(getStaticUrl(value.getLogoUrl()));
                    value.setPackageUrl(getStaticUrl(value.getPackageUrl()));
                });
                // 属于当前分组的应用列表
                clientDTO.setApplicationList(applications);
                res.add(clientDTO);
            }
        }

        res = res.stream().sorted(Comparator.comparing(ApplicationClientDTO::getWeight)).collect(Collectors.toList());
        return ResponseMessage.ok(res);
    }

    @Override
    public ResponseMessage<List<ApplicationAdapterClientDTO>> getApplicationManifest(List<String> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return ResponseMessage.error("ids不能为空.");
        }

        List<ApplicationAdapterClientDTO> applicationAdapters = clientMapper.getApplicationAdapterByIds(ids);
        if (!CollectionUtil.isEmpty(applicationAdapters)) {
            applicationAdapters.forEach(applicationAdapter -> applicationAdapter.setManifest(JsonUtil.fromJson(applicationAdapter.getManifestStr(), ManifestDTO.class)));
        }

        return ResponseMessage.ok(applicationAdapters);
    }

    @Override
    public ResponseMessage<List<SwaClientDTO>> getSwa() {
        List<SwaClientDTO> swas = clientMapper.getSwa();
        if (!CollectionUtil.isEmpty(swas)) {
            List<ApplicationClientItemDTO> allApplications = clientMapper.getApplication();
            if (!CollectionUtil.isEmpty(allApplications)) {
                swas.forEach(swa -> {
                    swa.setUrl(getStaticUrl(swa.getUrl()));
                    Optional<ApplicationClientItemDTO> applicationExtendOptional = allApplications.stream().filter(application ->
                            StringUtil.equalsIgoneUpper(application.getId(), swa.getApplicationId())).findFirst();

                    if (applicationExtendOptional.isPresent()) {
                        ApplicationClientItemDTO applicationClientItemDTO = applicationExtendOptional.get();
                        swa.setLogoUrl(getStaticUrl(applicationClientItemDTO.getLogoUrl()));
                        swa.setName(applicationClientItemDTO.getName());
                    }
                });
            }
        }

        return ResponseMessage.ok(swas);
    }

    private String getStaticUrl(String fileUrl) {
        if (StringUtil.isEmpty(fileUrl) || StringUtil.isEmpty(deployUrl)) {
            return fileUrl;
        }

        if (fileUrl.startsWith("http")) {
            return fileUrl;
        }

        return deployUrl + fileUrl;
    }
}
