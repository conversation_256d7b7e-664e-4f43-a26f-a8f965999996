package com.pttl.mobile.manager.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.pttl.mobile.manager.dao.SmsModelMapper;
import com.pttl.mobile.manager.domain.dto.SmsModelDTO;
import com.pttl.mobile.manager.domain.dto.SmsModelResponseDTO;
import com.pttl.mobile.manager.domain.entity.SmsModelDO;
import com.pttl.mobile.manager.service.SmsModelService;
import com.pttl.mobile.manager.util.BeanMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
@Service
public class SmsModelServiceImpl implements SmsModelService{

    @Autowired
    private SmsModelMapper smsModelMapper;

    @Override
    public int deleteByPrimaryKey(Long id) {
        return smsModelMapper.deleteByPrimaryKey(id);
    }

    @Override
    public int insert(SmsModelDO record) {
        return smsModelMapper.insert(record);
    }

    @Override
    public int insertSelective(SmsModelDO record) {
        return smsModelMapper.insertSelective(record);
    }

    @Override
    public SmsModelDO selectByPrimaryKey(Long id) {
        return smsModelMapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(SmsModelDO record) {
        return smsModelMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(SmsModelDO record) {
        return smsModelMapper.updateByPrimaryKey(record);
    }

    @Override
    public int updateBatch(List<SmsModelDO> list) {
        return smsModelMapper.updateBatch(list);
    }

    @Override
    public int updateBatchSelective(List<SmsModelDO> list) {
        return smsModelMapper.updateBatchSelective(list);
    }

    @Override
    public int batchInsert(List<SmsModelDO> list) {
        return smsModelMapper.batchInsert(list);
    }

    @Override
    public void saveOrUpdate(SmsModelDTO record) {
        SmsModelDO smsModelDO = BeanMapper.map(record, SmsModelDO.class);
        if (smsModelDO.getId() == null) {
            smsModelMapper.insertSelective(smsModelDO);
        } else {
            smsModelMapper.updateByPrimaryKeySelective(smsModelDO);
        }
    }

    @Override
    public List<SmsModelResponseDTO> list() {
        List<SmsModelDO> list = smsModelMapper.list();
        if (CollUtil.isNotEmpty(list)) {
            return BeanMapper.mapList(list, SmsModelResponseDTO.class);
        }
        return CollUtil.newArrayList();
    }
}
