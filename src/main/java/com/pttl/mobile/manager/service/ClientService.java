package com.pttl.mobile.manager.service;

import com.pttl.mobile.manager.domain.dto.*;
import com.pttl.mobile.manager.lib.ResponseMessage;

import java.util.List;

public interface ClientService {
    ResponseMessage<ReleaseClientDTO> getReleaseLastversion(Integer type, Integer operatingSystem);

    ResponseMessage<ReleaseClientDTO> getReleaseMallLastversion(Integer type, Integer operatingSystem);

    ResponseMessage<ReleaseClientDTO> getReleaseHongmengLastversion(Integer type, Integer operatingSystem);

    ResponseMessage<ConfigurationClientDTO> getConfiguration();

    ResponseMessage<List<ApplicationClientDTO>> getApplication();

    ResponseMessage<List<ApplicationAdapterClientDTO>> getApplicationManifest(List<String> ids);

    ResponseMessage<List<SwaClientDTO>> getSwa();
}
