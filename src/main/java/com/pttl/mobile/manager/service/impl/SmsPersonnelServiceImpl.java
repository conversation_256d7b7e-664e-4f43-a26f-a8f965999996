package com.pttl.mobile.manager.service.impl;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.pttl.mobile.manager.constant.SmsWarnTypeEnum;
import com.pttl.mobile.manager.dao.SmsPersonnelMapper;
import com.pttl.mobile.manager.domain.dto.SmsPersonnelDTO;
import com.pttl.mobile.manager.domain.dto.SmsPersonnelDetailsResponseDTO;
import com.pttl.mobile.manager.domain.dto.SmsPersonnelRequestDTO;
import com.pttl.mobile.manager.domain.dto.SmsPersonnelResponseDTO;
import com.pttl.mobile.manager.domain.entity.SmsPersonnelDO;
import com.pttl.mobile.manager.service.SmsPersonnelService;
import com.pttl.mobile.manager.service.SmsRelationService;
import com.pttl.mobile.manager.util.BeanMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SmsPersonnelServiceImpl implements SmsPersonnelService{

    @Autowired
    private SmsPersonnelMapper smsPersonnelMapper;

    @Autowired
    private SmsRelationService smsRelationService;


    @Override
    public int deleteByPrimaryKey(Long id) {
        return smsPersonnelMapper.deleteByPrimaryKey(id);
    }

    @Override
    public int insert(SmsPersonnelDO record) {
        return smsPersonnelMapper.insert(record);
    }

    @Override
    public int insertSelective(SmsPersonnelDO record) {
        return smsPersonnelMapper.insertSelective(record);
    }

    @Override
    public SmsPersonnelDO selectByPrimaryKey(Long id) {
        return smsPersonnelMapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(SmsPersonnelDO record) {
        return smsPersonnelMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(SmsPersonnelDO record) {
        return smsPersonnelMapper.updateByPrimaryKey(record);
    }

    @Override
    public int updateBatch(List<SmsPersonnelDO> list) {
        return smsPersonnelMapper.updateBatch(list);
    }

    @Override
    public int updateBatchSelective(List<SmsPersonnelDO> list) {
        return smsPersonnelMapper.updateBatchSelective(list);
    }

    @Override
    public int batchInsert(List<SmsPersonnelDO> list) {
        return smsPersonnelMapper.batchInsert(list);
    }

    @Override
    public void saveOrUpdate(SmsPersonnelDTO record) {
        SmsPersonnelDO smsPersonnelDO = BeanMapper.map(record, SmsPersonnelDO.class);

        if (record.getId() == null) {
            smsPersonnelMapper.insertSelective(smsPersonnelDO);
        } else {
            smsRelationService.deleteByPersonId(smsPersonnelDO.getId());
            smsPersonnelMapper.updateByPrimaryKeySelective(smsPersonnelDO);
        }
        smsRelationService.save(smsPersonnelDO.getId(), record.getModelId(), record.getSmsCondition(), SmsWarnTypeEnum.IOS_CODE);
    }

    @Override
    public PageInfo<SmsPersonnelResponseDTO> pageInfo(SmsPersonnelRequestDTO smsPersonnelRequestDTO) {
        PageMethod.startPage(smsPersonnelRequestDTO.getPageNum(), smsPersonnelRequestDTO.getPageSize());
        List<SmsPersonnelResponseDTO> dataList = smsPersonnelMapper.listByCondition(smsPersonnelRequestDTO.getName(), smsPersonnelRequestDTO.getPhoneNum());
        return new PageInfo<>(dataList);
    }

    @Override
    public void deleteById(Long id) {
        smsPersonnelMapper.deleteByPrimaryKey(id);
        // 删除管理关系
        smsRelationService.deleteByPersonId(id);
    }

    @Override
    public List<SmsPersonnelResponseDTO> getNeedSendWarningSMSMessagesPhoneBySmsType(SmsWarnTypeEnum smsWarnTypeEnum) {
        return smsPersonnelMapper.getNeedSendWarningSMSMessagesPhoneBySmsType(smsWarnTypeEnum.getType());
    }

    @Override
    public SmsPersonnelDetailsResponseDTO detailsById(Long id) {
        return smsPersonnelMapper.detailsById(id);
    }

}
