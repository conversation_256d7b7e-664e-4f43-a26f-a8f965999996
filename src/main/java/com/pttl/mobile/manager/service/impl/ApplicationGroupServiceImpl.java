package com.pttl.mobile.manager.service.impl;

import com.pttl.mobile.manager.dao.ApplicationGroupMapper;
import com.pttl.mobile.manager.lib.ResponseMessage;
import com.pttl.mobile.manager.domain.po.ApplicationGroup;
import com.pttl.mobile.manager.service.ApplicationGroupService;
import com.pttl.mobile.manager.service.ApplicationService;
import com.pttl.mobile.manager.util.CollectionUtil;
import com.pttl.mobile.manager.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.util.Date;
import java.util.List;
import java.util.UUID;

@Slf4j
@Service
public class ApplicationGroupServiceImpl implements ApplicationGroupService {
    @Autowired
    private ApplicationGroupMapper applicationGroupMapper;

    @Autowired
    private ApplicationService applicationService;

    @Override
    public ResponseMessage<Boolean> createApplicationGroup(String name) {
        // 检查需要插入的数据是否已经存在了
        int groupExistRows = applicationGroupMapper.isGroupExists(name);

        // 如果存在的话 抛出异常 否则 添加到数据库
        if (groupExistRows > 0) {
            return ResponseMessage.error("应用组已存在");
        }

        // 将业务模型转换为数据库模型 方便插入
        ApplicationGroup ag = new ApplicationGroup();
        ag.setId(UUID.randomUUID().toString());
        ag.setName(name);
        ag.setIsDefault(false);
        ag.setCreateDate(new Date());
        ag.setLastUpdate(new Date());
        applicationGroupMapper.insert(ag);

        return ResponseMessage.ok(true);
    }

    @Override
    public ResponseMessage<List<ApplicationGroup>> getApplicationGroup() {
        List<ApplicationGroup> applicationGroups = applicationGroupMapper.getApplicationGroup();

        return ResponseMessage.ok(applicationGroups);
    }

    @Transactional
    @Override
    public ResponseMessage<Boolean> deleteApplicationGroupByIds(List<String> ids) {
        if (!CollectionUtil.isEmpty(ids)) {
            // 由于主外键关系 删除应用组需要先删除相关应用
            ResponseMessage<Boolean> rb = applicationService.deleteByGroupIds(ids);
            // 如果应用删除成功 才会删除对应的应用组
            if (rb != null && rb.isOk()) {
                for (String id : ids) {
                    applicationGroupMapper.deleteByPrimaryKey(id);

                    return ResponseMessage.ok(true);
                }
            } else {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return ResponseMessage.error("删除应用组失败");
            }
        }

        return ResponseMessage.error("参数不能为空");
    }

    @Override
    public ResponseMessage<Boolean> updateApplicationGroup(String id, String name) {
        if (!StringUtil.isEmpty(id) && !StringUtil.isEmpty(name)) {
            // 检查需要插入的数据是否已经存在了
            int groupExistRows = applicationGroupMapper.isGroupExists(name);
            if (groupExistRows > 0) {
                return ResponseMessage.error("应用组名称已存在");
            }

            ApplicationGroup ag = new ApplicationGroup();
            ag.setId(id);
            ag.setName(name);
            ag.setLastUpdate(new Date());
            applicationGroupMapper.updateByPrimaryKeySelective(ag);

            return ResponseMessage.ok(true);
        }

        return ResponseMessage.error("参数不能为空");
    }

    @Override
    @Transactional
    public ResponseMessage<Boolean> updateApplicationGroupWeight(List<String> groupIds) {
        if(CollectionUtil.isEmpty(groupIds)){
            return ResponseMessage.error("参数ids不能为空");
        }

        int weight = 1;
        for (String groupId : groupIds) {
            ApplicationGroup ag = new ApplicationGroup();
            ag.setId(groupId);
            ag.setWeight(weight);
            ag.setLastUpdate(new Date());
            applicationGroupMapper.updateByPrimaryKeySelective(ag);
            weight++;
        }

        return ResponseMessage.ok(true);
    }
}
