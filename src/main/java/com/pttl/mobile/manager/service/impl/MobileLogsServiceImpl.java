package com.pttl.mobile.manager.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.pttl.mobile.manager.dao.MobileLogsMapper;
import com.pttl.mobile.manager.domain.dto.FrontLogsQueryDTO;
import com.pttl.mobile.manager.domain.dto.FrontLogsResultDTO;
import com.pttl.mobile.manager.domain.entity.MobileLogs;
import com.pttl.mobile.manager.mobile.ws.service.MobilAddressBookExtendService;
import com.pttl.mobile.manager.service.MobileLogsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class MobileLogsServiceImpl implements MobileLogsService{

    @Autowired
    private MobileLogsMapper mobileLogsMapper;

    /**
     * 通讯录扩展字段
     */
    @Resource
    private MobilAddressBookExtendService mobilAddressBookExtendService;

    @Override
    public int deleteByPrimaryKey(Long id) {
        return mobileLogsMapper.deleteByPrimaryKey(id);
    }

    @Override
    public Integer insert(MobileLogs record) {
        boolean contactsSwitchStatus = mobilAddressBookExtendService.getContactsSwitchStatus(record.getIhrName());
        if (contactsSwitchStatus) {
            return mobileLogsMapper.insert(record);
        }

        log.info("MobileLogs saveMobileLog contactsSwitchStatus is not open, customerId: {}", record.getIhrName());
        return 0;
    }

    @Override
    public int insertSelective(MobileLogs record) {
        return mobileLogsMapper.insertSelective(record);
    }

    @Override
    public MobileLogs selectByPrimaryKey(Long id) {
        return mobileLogsMapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(MobileLogs record) {
        return mobileLogsMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(MobileLogs record) {
        return mobileLogsMapper.updateByPrimaryKey(record);
    }

    @Override
    public int updateBatch(List<MobileLogs> list) {
        return mobileLogsMapper.updateBatch(list);
    }

    @Override
    public int updateBatchSelective(List<MobileLogs> list) {
        return mobileLogsMapper.updateBatchSelective(list);
    }

    @Override
    public int batchInsert(List<MobileLogs> list) {
        return mobileLogsMapper.batchInsert(list);
    }

    @Override
    public PageInfo<FrontLogsResultDTO> pageInfo(FrontLogsQueryDTO frontLogsQuery) {
        PageMethod.startPage(frontLogsQuery.getPageNum(), frontLogsQuery.getPageSize());
        PageMethod.orderBy("log_date desc");

        List<FrontLogsResultDTO> dataList = mobileLogsMapper.pageInfo(frontLogsQuery);
        if (CollUtil.isEmpty(dataList)) {
            return new PageInfo<>();
        }

        return new PageInfo<>(dataList);
    }

    @Override
    @Async("saveAsync")
    public void saveInBulk(List<MobileLogs> mobileLogs) {
        boolean contactsSwitchStatusFlag = false;
        boolean contactsSwitchStatus = false;
        for (MobileLogs mobileLog : mobileLogs) {
            if (Objects.isNull(mobileLog.getIhrName()) || Objects.isNull(mobileLog.getServerName())) {
                log.info("current data IhrName or ServerName is null , {}", mobileLog);
                continue;
            }

            // 检查日志开关
            if (!contactsSwitchStatusFlag) {
                contactsSwitchStatusFlag = true;
                contactsSwitchStatus = mobilAddressBookExtendService.getContactsSwitchStatus(mobileLog.getIhrName());
            }

            if (contactsSwitchStatusFlag && !contactsSwitchStatus) {
                log.info("MobileLogs saveMobileLog contactsSwitchStatus is not open, customerId: {}", mobileLog.getIhrName());
                return;
            }

            insert(mobileLog);
        }
    }

}
