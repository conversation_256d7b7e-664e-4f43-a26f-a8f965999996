package com.pttl.mobile.manager.service;

import java.util.List;
import com.pttl.mobile.manager.domain.entity.SmsLogs;
public interface SmsLogsService{

    int deleteByPrimaryKey(Long id);

    int insert(SmsLogs record);

    int insertSelective(SmsLogs record);

    SmsLogs selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(SmsLogs record);

    int updateByPrimaryKey(SmsLogs record);

    int updateBatch(List<SmsLogs> list);

    int updateBatchSelective(List<SmsLogs> list);

    int batchInsert(List<SmsLogs> list);

}
