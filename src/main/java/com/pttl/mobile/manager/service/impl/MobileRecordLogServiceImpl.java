package com.pttl.mobile.manager.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.pttl.mobile.manager.constant.CommonConstants;
import com.pttl.mobile.manager.dao.MobileRecordLogMapper;
import com.pttl.mobile.manager.domain.dto.MobileRecordLogDetailsDTO;
import com.pttl.mobile.manager.domain.entity.MobileRecordLogDO;
import com.pttl.mobile.manager.domain.request.MobileRecordLogDetailsRequest;
import com.pttl.mobile.manager.domain.request.MobileRecordLogPageRequest;
import com.pttl.mobile.manager.mobile.dto.DateRange;
import com.pttl.mobile.manager.mobile.dto.LoginStatisticsResult;
import com.pttl.mobile.manager.mobile.dto.LoginStatisticsResultDTO;
import com.pttl.mobile.manager.mobile.dto.MobileRecordLogQueryDTO;
import com.pttl.mobile.manager.mobile.util.TableNameDateUtil;
import com.pttl.mobile.manager.mobile.ws.dao.PersonInfoMapper;
import com.pttl.mobile.manager.mobile.ws.entity.PersonInfoDO;
import com.pttl.mobile.manager.mobile.ws.util.MonthlyDateRangeCalculator;
import com.pttl.mobile.manager.service.MobileRecordLogService;
import com.pttl.mobile.manager.util.BeanMapper;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Service
public class MobileRecordLogServiceImpl implements MobileRecordLogService {

    @Resource
    private MobileRecordLogMapper mobileRecordLogMapper;

    @Resource
    private PersonInfoMapper personInfoMapper;

    @Override
    public int deleteByPrimaryKey(Long id) {
        return mobileRecordLogMapper.deleteByPrimaryKey(id);
    }

    @Override
    @Async(value = "saveAsync")
    @Transactional(rollbackFor = Exception.class)
    public void insert(MobileRecordLogDO record) {
        // 处理登陆用户名信息绑定 反查通讯录
        processingLogUserNamesAndEmailInformation(record);

        mobileRecordLogMapper.insert(record, TableNameDateUtil.getCurrentYearMonth());
    }

    /**
     * 处理登陆用户名信息绑定 反查通讯录
     *
     * @param record 上报日志新
     */
    private void processingLogUserNamesAndEmailInformation(MobileRecordLogDO record) {
        // 判断用户登陆名称还是工号 然后进行反查用户信息
        String loginName = record.getLoginName();

        // 工号登陆
        if (loginName.toLowerCase().startsWith(CommonConstants.JOB_NUMBER_PREFIX2)) {
            PersonInfoDO personInfoDO = personInfoMapper.selectOneByEmployeeId(loginName);
            if (Objects.nonNull(personInfoDO)) {
                // 预留的扩展字段 这里用来存邮箱地址
                record.setExtension(personInfoDO.getEmailAddress());
                // 备注字段这里用来存 用户名称
                record.setRemark(personInfoDO.getName());
                return;
            }
            record.setExtension("未知");
            record.setRemark("未知");
            return;
        }

        // 用户名登陆
        PersonInfoDO personInfoDO = personInfoMapper.selectByNamePinYin(loginName + "@cetctaili.com");
        if (Objects.nonNull(personInfoDO)) {
            // 预留的扩展字段 这里用来存邮箱地址
            record.setExtension(personInfoDO.getEmailAddress());
            // 备注字段这里用来存 用户名称
            record.setRemark(personInfoDO.getName());
            // 登陆名称字段 为用户工号
            record.setLoginName(personInfoDO.getEmployeeId());
            return;
        }

        // 都查询不到 则工号设置未知 邮箱设置为输入项
        if(!"商城".equals(record.getModule())) {
            record.setLoginName("未知");
        }
        record.setExtension(loginName);
        record.setRemark("未知");
    }

    @Override
    public int insertSelective(MobileRecordLogDO record) {
        return mobileRecordLogMapper.insertSelective(record);
    }

    @Override
    public MobileRecordLogDO selectByPrimaryKey(Long id, String yearMonth) {
        return mobileRecordLogMapper.selectByPrimaryKey(id, yearMonth);
    }

    @Override
    public int updateByPrimaryKeySelective(MobileRecordLogDO record) {
        return mobileRecordLogMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(MobileRecordLogDO record) {
        return mobileRecordLogMapper.updateByPrimaryKey(record);
    }

    @Override
    public List<MobileRecordLogDO> selectAllOrderByIdDesc() {
        return mobileRecordLogMapper.selectAllOrderByIdDesc();
    }

    @Override
    public PageInfo<MobileRecordLogDO> pageInfo(MobileRecordLogPageRequest pageRequest) {
        PageMethod.startPage(pageRequest.getPageNum(), pageRequest.getPageSize());

        List<MobileRecordLogDO> dataList = mobileRecordLogMapper.listByPageParameter(pageRequest);
        return new PageInfo<>(dataList);
    }

    @Override
    public MobileRecordLogDetailsDTO getDetailsById(MobileRecordLogDetailsRequest request) {
        MobileRecordLogDO mobileRecordLogDO = this.selectByPrimaryKey(request.getId(), request.getYearMonth());
        if (Objects.isNull(mobileRecordLogDO)) {
            return null;
        }

        return BeanMapper.map(mobileRecordLogDO, MobileRecordLogDetailsDTO.class);
    }

    @Override
    public LoginStatisticsResult getLoginStatistics(String dayType) {
        Date endDate = new Date();
        Date startDate = getStartDate(dayType, endDate);

        if (Objects.nonNull(startDate)) {
            // 获取月份及开始/截止时间
            List<DateRange> monthlyRanges = MonthlyDateRangeCalculator.calculateMonthlyRanges(startDate, endDate);
            List<LoginStatisticsResultDTO> results = new ArrayList<>();
            for (DateRange range : monthlyRanges) {
                List<LoginStatisticsResultDTO> currentResults = mobileRecordLogMapper.listLoginStatisticsByDateRange(range);
                results.addAll(currentResults);
            }

            // 计算汇总结果
            if (CollUtil.isNotEmpty(results)) {
                LoginStatisticsResult loginStatisticsResult = new LoginStatisticsResult();
                for (LoginStatisticsResultDTO currentResult : results) {
                    if (CommonConstants.SOURCE_TYPE_ANDROID.equalsIgnoreCase(currentResult.getSource())) {
                        int androidCount = loginStatisticsResult.getAndroidCount();
                        loginStatisticsResult.setAndroidCount(androidCount + currentResult.getCount());
                    } else {
                        int iosCount = loginStatisticsResult.getIosCount();
                        loginStatisticsResult.setIosCount(iosCount + currentResult.getCount());
                    }
                }
                return loginStatisticsResult;
            }
        }

        return new LoginStatisticsResult();
    }

    @Override
    public PageInfo<MobileRecordLogDO> pageInfoForMobile(MobileRecordLogQueryDTO mobileRecordLogQueryDTO) {
        PageMethod.startPage(mobileRecordLogQueryDTO.getPageNum(), mobileRecordLogQueryDTO.getPageSize());

        // 获取当前查询时间的表名月份
        String mouthDate = MonthlyDateRangeCalculator.formatDate(mobileRecordLogQueryDTO.getStartLoginTime() == null ? new Date()
                : mobileRecordLogQueryDTO.getStartLoginTime());

        List<MobileRecordLogDO> dataList = mobileRecordLogMapper.listByPageParameterForMobile(mouthDate, mobileRecordLogQueryDTO);
        return new PageInfo<>(dataList);
    }

    /**
     * 开始时间获取
     *
     * @param dayType 时间类型
     * @param endDate 当前时间
     * @return 计算后时间
     */
    private Date getStartDate(String dayType, Date endDate) {
        // 近一天
        if (CommonConstants.DAY_TYPE_ONE_DAY.equals(dayType)) {
            return DateUtil.offsetDay(endDate, -1).toJdkDate();
        }

        // 近一周
        if (CommonConstants.DAY_TYPE_ONE_WEEK.equals(dayType)) {
            return DateUtil.offsetDay(endDate, -6).toJdkDate();
        }

        // 近一月
        if (CommonConstants.DAY_TYPE_ONE_MOUTH.equals(dayType)) {
            return DateUtil.offsetDay(endDate, -30).toJdkDate();
        }

        // 近三个月
        if (CommonConstants.DAY_TYPE_THREE_MOUTH.equals(dayType)) {
            return DateUtil.offsetMonth(endDate, -2).toJdkDate();
        }

        return null;
    }

}
