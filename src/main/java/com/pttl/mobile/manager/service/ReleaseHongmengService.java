package com.pttl.mobile.manager.service;

import com.pttl.mobile.manager.domain.dto.ReleaseExtendDTO;
import com.pttl.mobile.manager.domain.entity.ReleaseHongmeng;
import com.pttl.mobile.manager.domain.po.Release;
import com.pttl.mobile.manager.domain.po.ReleaseType;
import com.pttl.mobile.manager.lib.ResponseMessage;

import java.util.Date;
import java.util.List;
public interface ReleaseHongmengService{

    int deleteByPrimaryKey(String id);

    int insert(Release record);

    int insertSelective(ReleaseHongmeng record);

    int updateByPrimaryKeySelective(ReleaseHongmeng record);

    int updateByPrimaryKey(ReleaseHongmeng record);

    int updateBatch(List<ReleaseHongmeng> list);

    int updateBatchSelective(List<ReleaseHongmeng> list);

    int batchInsert(List<ReleaseHongmeng> list);

    ResponseMessage<List<ReleaseType>> getReleaseType();

    ResponseMessage<List<ReleaseExtendDTO>> getReleaseHistory(Integer type, String version, String keyword, Date startDate, Date endDate);

    ResponseMessage<Boolean> createRelease(Release release);

    ResponseMessage<Boolean> deleteReleaseByIds(List<String> ids);

}
