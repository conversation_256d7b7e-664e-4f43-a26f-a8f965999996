package com.pttl.mobile.manager.service;

import com.pttl.mobile.manager.domain.dto.ApplicationDTO;
import com.pttl.mobile.manager.domain.entity.StrategyApplication2DO;

import java.util.List;

public interface StrategyApplication2Service {


    int deleteByPrimaryKey(Integer id);

    int insert(StrategyApplication2DO strategyApplication2DO);

    int insertSelective(StrategyApplication2DO strategyApplication2DO);

    StrategyApplication2DO selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(StrategyApplication2DO strategyApplication2DO);

    int updateByPrimaryKey(StrategyApplication2DO strategyApplication2DO);

    int updateBatch(List<StrategyApplication2DO> list);

    int updateBatchSelective(List<StrategyApplication2DO> list);

    int batchInsert(List<StrategyApplication2DO> list);


    /**
     * 根据策略id获取关联应用
     *
     * @param id
     * @return
     */
    List<ApplicationDTO> getApplicationByStrategyId(Long id);

    /**
     * 根据策略id删除
     *
     * @param strategyId
     */
    void deleteByStrategyId(Long strategyId);
}
