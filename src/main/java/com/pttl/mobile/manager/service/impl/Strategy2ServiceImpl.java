package com.pttl.mobile.manager.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.pttl.mobile.manager.dao.Strategy2Mapper;
import com.pttl.mobile.manager.domain.dto.*;
import com.pttl.mobile.manager.domain.entity.*;
import com.pttl.mobile.manager.domain.request.*;
import com.pttl.mobile.manager.mobile.exception.ErrorMessageException;
import com.pttl.mobile.manager.service.*;
import com.pttl.mobile.manager.util.BeanMapper;
import com.pttl.mobile.manager.util.PageUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class Strategy2ServiceImpl implements Strategy2Service {

    @Resource
    private Strategy2Mapper strategy2Mapper;

    /**
     * 策略关联应用 服务层
     */
    @Resource
    private StrategyApplication2Service strategyApplication2Service;

    /**
     * 策略用户关联 服务层
     */
    @Resource
    private StrategyUser2Service strategyUser2Service;

    /**
     * 策略部门关联 服务层
     */
    @Resource
    private StrategyDepartment2Service strategyDepartment2Service;

    /**
     * 部门服务层
     */
    @Resource
    private Department2Service department2Service;

    @Override
    public int deleteByPrimaryKey(Long id) {
        return strategy2Mapper.deleteByPrimaryKey(id);
    }

    @Override
    public int insert(Strategy2DO record) {
        return strategy2Mapper.insert(record);
    }

    @Override
    public int insertSelective(Strategy2DO record) {
        return strategy2Mapper.insertSelective(record);
    }

    @Override
    public Strategy2DO selectByPrimaryKey(Long id) {
        return strategy2Mapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(Strategy2DO record) {
        return strategy2Mapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(Strategy2DO record) {
        return strategy2Mapper.updateByPrimaryKey(record);
    }

    @Override
    public int updateBatch(List<Strategy2DO> list) {
        return strategy2Mapper.updateBatch(list);
    }

    @Override
    public int updateBatchSelective(List<Strategy2DO> list) {
        return strategy2Mapper.updateBatchSelective(list);
    }

    @Override
    public int batchInsert(List<Strategy2DO> list) {
        return strategy2Mapper.batchInsert(list);
    }

    @Override
    public PageInfo<StrategyPageDTO> pageInfo(StrategyPageRequest pageRequest) {
        PageMethod.startPage(pageRequest.getPageNum(), pageRequest.getPageSize());
        List<Strategy2DO> dataList = strategy2Mapper.listData(pageRequest.getName());
        if (CollUtil.isEmpty(dataList)) {
            return new PageInfo<>();
        }

        return PageUtil.page2PageVo(new PageInfo<>(dataList), StrategyPageDTO.class);
    }

    @Override
    public StrategyDetailDTO getStrategyDetails(Long id) {
        Strategy2DO strategy2DO = this.selectByPrimaryKey(id);
        StrategyDetailDTO strategyDetailDTO = BeanMapper.map(strategy2DO, StrategyDetailDTO.class);
        // 设置使用该策略的人数
        strategyDetailDTO.setStrategyNum(strategyUser2Service.countByStrategyId(id));
        return strategyDetailDTO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeByIds(List<Long> ids) {
        ids.forEach(strategyId -> {
            Strategy2DO strategy2DO = strategy2Mapper.selectByPrimaryKey(strategyId);
            strategy2DO.setIsDeleted(Strategy2DO.DELETED_YES);
            strategy2Mapper.updateByPrimaryKey(strategy2DO);

            // 同时删除 策略关联的 用户/部门/应用 物理删除
            strategyApplication2Service.deleteByStrategyId(strategyId);
            strategyDepartment2Service.deleteByStrategyId(strategyId);
            strategyUser2Service.deleteByStrategyId(strategyId);
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cloneById(Long id) {
        Strategy2DO strategy2DO = strategy2Mapper.selectByPrimaryKey(id);
        String strategyName = strategy2DO.getName();
        strategy2DO.setName(strategyName + "_克隆");
        strategy2DO.setId(null);
        strategy2DO.setCreateDate(new Date());
        strategy2DO.setLastUpdate(new Date());
        strategy2DO.setCurrentStep(Strategy2DO.CURRENT_STEP_BASE);
        strategy2DO.setWeight(0);
        strategy2Mapper.insert(strategy2DO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStrategy(StrategyUpdateRequest dataRequest) {
        Strategy2DO strategy2DO = this.selectByPrimaryKey(dataRequest.getId());
        // 检查策略权重 权重不能重复
        checkPolicyWeights(strategy2DO.getId(), dataRequest.getWeight());

        strategy2DO.setName(dataRequest.getName());
        strategy2DO.setWeight(dataRequest.getWeight());
        if (!StrUtil.isEmpty(dataRequest.getDescription())) {
            strategy2DO.setDescription(dataRequest.getDescription());
        }
        strategy2DO.setLastUpdate(new Date());

        strategy2Mapper.updateByPrimaryKey(strategy2DO);
    }

    /**
     * 检查策略权重 权重不能重复 默认为0 可重复
     *
     * @param weight
     * @param strategyId
     */
    private void checkPolicyWeights(Long strategyId, Integer weight) {
        // 检查是否有相同权重的的 默认为0
        List<Strategy2DO> strategyList = strategy2Mapper.listAllByWeight(weight);
        if (CollUtil.isNotEmpty(strategyList)) {
            strategyList.forEach(strategy2DO1 -> {
                if (!strategy2DO1.getId().equals(strategyId)) {
                    throw new ErrorMessageException("已有相同权重策略");
                }
            });
        }
    }

    @Override
    public List<ApplicationDTO> getApplicationByStrategyId(Long id) {
        return strategyApplication2Service.getApplicationByStrategyId(id);
    }

    @Override
    public List<PolicyUserAndDepartmentConfigurationDTO> getUserByStrategyId(Long strategyId) {
        List<PolicyUserAndDepartmentConfigurationDTO> resultList = new ArrayList<>();
        // 根据策略id 获取相关联用户信息
        userDataListByStrategyId(resultList, strategyId);

        // 根据策略id 获取相关联部门信息
        departmentDataListByStrategyId(resultList, strategyId);

        return resultList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createStrategy(StrategyCreateRequest dataRequest) {
        // 创建策略信息
        Long strategyInfoId = createStrategyBaseInfo(dataRequest);

        // 关联应用 用户 部门
        processStrategyAssociatedApps(strategyInfoId, dataRequest.getApplicationIds());

        processStrategyAssociatedUsers(strategyInfoId, dataRequest.getUserIds());

        processStrategyAssociatedDepartment(strategyInfoId, dataRequest.getDepartmentIds());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStrategyAppConfiguration(StrategyUpdateAppConfigurationRequest dataRequest) {
        // 这里采用先删后加
        strategyApplication2Service.deleteByStrategyId(dataRequest.getId());
        // 后增加
        processStrategyAssociatedApps(dataRequest.getId(), dataRequest.getApplicationIds());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateStrategyUserConfiguration(StrategyUpdateUserConfigurationRequest dataRequest) {
        // 策略关联用户
        if (CollUtil.isNotEmpty(dataRequest.getUserIds())) {
            strategyUser2Service.deleteByStrategyId(dataRequest.getId());
            processStrategyAssociatedUsers(dataRequest.getId(), dataRequest.getUserIds());
        }

        // 策略关联部门
        if (CollUtil.isNotEmpty(dataRequest.getDepartmentIds())) {
            strategyDepartment2Service.deleteByStrategyId(dataRequest.getId());
            processStrategyAssociatedDepartment(dataRequest.getId(), dataRequest.getDepartmentIds());
        }
    }

    @Override
    public List<UserStrategyInfoDTO> userStrategyByUserId(Long userId) {
        return strategyUser2Service.userStrategyByUserId(userId);
    }

    /**
     * 处理策略关联应用-批量处理
     *
     * @param strategyInfoId
     * @param applicationIds
     */
    private void processStrategyAssociatedApps(Long strategyInfoId, List<String> applicationIds) {
        if (CollUtil.isNotEmpty(applicationIds)) {
            List<StrategyApplication2DO> list = new ArrayList<>(applicationIds.size());
            applicationIds.forEach(appId -> {
                StrategyApplication2DO strategyApplication2DO = new StrategyApplication2DO();
                strategyApplication2DO.setStrategyId(strategyInfoId);
                strategyApplication2DO.setApplicationId(appId);
                list.add(strategyApplication2DO);
            });
            strategyApplication2Service.batchInsert(list);
        }
    }

    /**
     * 处理策略关联用户-批量处理
     *
     * @param strategyInfoId
     * @param userIds
     */
    private void processStrategyAssociatedUsers(Long strategyInfoId, List<Long> userIds) {
        if (CollUtil.isNotEmpty(userIds)) {
            List<StrategyUser2DO> list = new ArrayList<>(userIds.size());
            userIds.forEach(userId -> {
                StrategyUser2DO strategyUser2DO = new StrategyUser2DO();
                strategyUser2DO.setStrategyId(strategyInfoId);
                strategyUser2DO.setUserId(userId);
                list.add(strategyUser2DO);
            });
            strategyUser2Service.batchInsert(list);
        }
    }

    /**
     * 处理策略关联部门-批量处理
     *
     * @param strategyInfoId
     * @param departmentIds
     */
    private void processStrategyAssociatedDepartment(Long strategyInfoId, List<Long> departmentIds) {
        if (CollUtil.isNotEmpty(departmentIds)) {
            List<StrategyDepartment2DO> list = new ArrayList<>(departmentIds.size());
            departmentIds.forEach(departmentId -> {
                StrategyDepartment2DO strategyDepartment2DO = new StrategyDepartment2DO();
                strategyDepartment2DO.setStrategyId(strategyInfoId);
                strategyDepartment2DO.setDepartmentId(departmentId);
                list.add(strategyDepartment2DO);
            });
            strategyDepartment2Service.batchInsert(list);
        }
    }

    /**
     * 创建策略基础信息
     *
     * @param dataRequest
     * @return
     */
    private Long createStrategyBaseInfo(StrategyCreateRequest dataRequest) {
        List<Strategy2DO> strategy2DOS = strategy2Mapper.listAllByWeight(dataRequest.getWeight());
        if (CollUtil.isNotEmpty(strategy2DOS)) {
            throw new ErrorMessageException("已有相同权重策略");
        }
        // 基本信息
        Strategy2DO strategy2DO = BeanMapper.map(dataRequest, Strategy2DO.class);
        strategy2DO.setLastUpdate(new Date());
        strategy2DO.setCreateDate(new Date());
        strategy2DO.setIsDeleted(Strategy2DO.DELETED_NO);
        strategy2DO.setCurrentStep(Strategy2DO.CURRENT_STEP_BASE);
        insert(strategy2DO);
        return strategy2DO.getId();
    }

    /**
     * 根据策略id 获取相关联部门信息
     *
     * @param resultList
     * @param strategyId
     */
    private void departmentDataListByStrategyId(List<PolicyUserAndDepartmentConfigurationDTO> resultList, Long strategyId) {
        // 获取关联部门
        List<Department2DO> department2DOList = strategyDepartment2Service.listByStrategyId(strategyId);
        if (!CollUtil.isEmpty(department2DOList)) {
            department2DOList.forEach(departmentDO -> {
                PolicyUserAndDepartmentConfigurationDTO departmentDTO = new PolicyUserAndDepartmentConfigurationDTO();
                departmentDTO.setId(departmentDO.getId());
                departmentDTO.setName(departmentDO.getName());
                departmentDTO.setDataType(PolicyUserAndDepartmentConfigurationDTO.DATA_TYPE_DEPARTMENT);
                // 设置部门全路径
                String fullDepartmentName = department2Service.getFullDepartmentName(departmentDTO.getId());
                departmentDTO.setOrganization(fullDepartmentName);
                resultList.add(departmentDTO);
            });
        }
    }

    /**
     * 根据策略id 获取相关联用户信息
     *
     * @param resultList
     * @param strategyId
     */
    private void userDataListByStrategyId(List<PolicyUserAndDepartmentConfigurationDTO> resultList, Long strategyId) {
        // 获取关联用户
        List<UserDO> user2DOList = strategyUser2Service.listByStrategyId(strategyId);
        if (!CollUtil.isEmpty(user2DOList)) {
            user2DOList.forEach(userDO -> {
                PolicyUserAndDepartmentConfigurationDTO userDTO = new PolicyUserAndDepartmentConfigurationDTO();
                userDTO.setId(userDO.getId());
                userDTO.setName(userDO.getName());
                userDTO.setDataType(PolicyUserAndDepartmentConfigurationDTO.DATA_TYPE_USER);
                // 设置部门全路径
                String fullDepartmentName = department2Service.getFullDepartmentName(userDO.getDepartmentId());
                userDTO.setOrganization(fullDepartmentName);
                resultList.add(userDTO);
            });
        }
    }
}
