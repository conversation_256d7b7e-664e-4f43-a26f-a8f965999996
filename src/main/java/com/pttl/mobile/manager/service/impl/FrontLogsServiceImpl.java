package com.pttl.mobile.manager.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.pttl.mobile.manager.dao.FrontLogsMapper;
import com.pttl.mobile.manager.domain.dto.FrontLogsQueryDTO;
import com.pttl.mobile.manager.domain.dto.FrontLogsResultDTO;
import com.pttl.mobile.manager.domain.dto.LogDataDTO;
import com.pttl.mobile.manager.domain.entity.FrontLogs;
import com.pttl.mobile.manager.mobile.ws.service.MobilAddressBookExtendService;
import com.pttl.mobile.manager.service.FrontLogsService;
import com.pttl.mobile.manager.service.MobileLogsService;
import com.pttl.mobile.manager.util.BeanMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class FrontLogsServiceImpl implements FrontLogsService{

    @Autowired
    private FrontLogsMapper frontLogsMapper;

    @Autowired
    private MobileLogsService mobileLogsService;

    /**
     * 通讯录扩展字段
     */
    @Resource
    private MobilAddressBookExtendService mobilAddressBookExtendService;

    @Override
    public int deleteByPrimaryKey(Long id) {
        return frontLogsMapper.deleteByPrimaryKey(id);
    }

    @Override
    public int insert(FrontLogs record) {
        return frontLogsMapper.insert(record);
    }

    @Override
    public int insertSelective(FrontLogs record) {
        return frontLogsMapper.insertSelective(record);
    }

    @Override
    public FrontLogs selectByPrimaryKey(Long id) {
        return frontLogsMapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(FrontLogs record) {
        return frontLogsMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(FrontLogs record) {
        return frontLogsMapper.updateByPrimaryKey(record);
    }

    @Override
    public int updateBatch(List<FrontLogs> list) {
        return frontLogsMapper.updateBatch(list);
    }

    @Override
    public int updateBatchSelective(List<FrontLogs> list) {
        return frontLogsMapper.updateBatchSelective(list);
    }

    @Override
    public int batchInsert(List<FrontLogs> list) {
        return frontLogsMapper.batchInsert(list);
    }

    @Override
    @Async("saveAsync")
    public void savefrontLog(List<LogDataDTO> logDatas) {
        boolean contactsSwitchStatusFlag = false;
        boolean contactsSwitchStatus = false;
        for (LogDataDTO logData : logDatas) {
            // 关键数据为空时跳过 不保存
            if (Objects.isNull(logData.getIhrName()) || Objects.isNull(logData.getServerName())) {
                log.info("current data IhrName or ServerName is null , {}", logData);
                continue;
            }

            // 检查日志开关
            if (!contactsSwitchStatusFlag) {
                contactsSwitchStatusFlag = true;
                contactsSwitchStatus = mobilAddressBookExtendService.getContactsSwitchStatus(logData.getIhrName());
            }

            if (contactsSwitchStatusFlag && !contactsSwitchStatus) {
                log.info("MobileLogs savefrontLog contactsSwitchStatus is not open, customerId: {}", logData.getIhrName());
                return;
            }

            // 处理数据
            FrontLogs frontLogs = BeanMapper.map(logData, FrontLogs.class);
            if (Objects.nonNull(logData.getNwePageLoad())) {
                frontLogs.setNwePageLoad(JSON.toJSONString(logData.getNwePageLoad()));
            }

            if (Objects.nonNull(logData.getLogDetail())) {
                frontLogs.setLogDetail(JSON.toJSONString(logData.getLogDetail()));
            }

            frontLogsMapper.insertSelective(frontLogs);
        }

    }

    @Override
    public PageInfo<FrontLogsResultDTO> pageInfo(FrontLogsQueryDTO frontLogsQuery) {
        if (frontLogsQuery.getDatatype() == 1) {
            return pageInfoFrontLogs(frontLogsQuery);
        }

        return mobileLogsService.pageInfo(frontLogsQuery);
    }

    public PageInfo<FrontLogsResultDTO> pageInfoFrontLogs(FrontLogsQueryDTO frontLogsQuery) {
        PageMethod.startPage(frontLogsQuery.getPageNum(), frontLogsQuery.getPageSize());
        PageMethod.orderBy("log_date desc");

        List<FrontLogsResultDTO> dataList = frontLogsMapper.pageInfo(frontLogsQuery);
        if (CollUtil.isEmpty(dataList)) {
            return new PageInfo<>();
        }

        return new PageInfo<>(dataList);
    }

    @Override
    public PageInfo<FrontLogs> exportPageInfoFrontLogs(FrontLogsQueryDTO frontLogsQuery) {
        PageMethod.startPage(frontLogsQuery.getPageNum(), frontLogsQuery.getPageSize());
        PageMethod.orderBy("log_date desc");

        List<FrontLogs> dataList = frontLogsMapper.exportPageInfo(frontLogsQuery);
        if (CollUtil.isEmpty(dataList)) {
            return new PageInfo<>();
        }

        return new PageInfo<>(dataList);
    }

}
