package com.pttl.mobile.manager.service.impl;

import org.springframework.stereotype.Service;

import org.springframework.beans.factory.annotation.Autowired;

import com.pttl.mobile.manager.dao.SmsLogsMapper;
import java.util.List;
import com.pttl.mobile.manager.domain.entity.SmsLogs;
import com.pttl.mobile.manager.service.SmsLogsService;
@Service
public class SmsLogsServiceImpl implements SmsLogsService{

    @Autowired
    private SmsLogsMapper smsLogsMapper;

    @Override
    public int deleteByPrimaryKey(Long id) {
        return smsLogsMapper.deleteByPrimaryKey(id);
    }

    @Override
    public int insert(SmsLogs record) {
        return smsLogsMapper.insert(record);
    }

    @Override
    public int insertSelective(SmsLogs record) {
        return smsLogsMapper.insertSelective(record);
    }

    @Override
    public SmsLogs selectByPrimaryKey(Long id) {
        return smsLogsMapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(SmsLogs record) {
        return smsLogsMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(SmsLogs record) {
        return smsLogsMapper.updateByPrimaryKey(record);
    }

    @Override
    public int updateBatch(List<SmsLogs> list) {
        return smsLogsMapper.updateBatch(list);
    }

    @Override
    public int updateBatchSelective(List<SmsLogs> list) {
        return smsLogsMapper.updateBatchSelective(list);
    }

    @Override
    public int batchInsert(List<SmsLogs> list) {
        return smsLogsMapper.batchInsert(list);
    }

}
