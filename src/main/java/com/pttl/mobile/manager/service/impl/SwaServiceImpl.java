package com.pttl.mobile.manager.service.impl;

import com.pttl.mobile.manager.dao.ApplicationMapper;
import com.pttl.mobile.manager.dao.SwaKeeperMapper;
import com.pttl.mobile.manager.dao.SwaMapper;
import com.pttl.mobile.manager.domain.dto.SwaExtendDTO;
import com.pttl.mobile.manager.lib.ResponseMessage;
import com.pttl.mobile.manager.domain.po.Swa;
import com.pttl.mobile.manager.domain.po.SwaKeeper;
import com.pttl.mobile.manager.domain.request.SwaCreateRequest;
import com.pttl.mobile.manager.service.SwaService;
import com.pttl.mobile.manager.util.BeanUtil;
import com.pttl.mobile.manager.util.CollectionUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.UUID;

@Slf4j
@Service
public class SwaServiceImpl implements SwaService {
    @Autowired
    private ApplicationMapper applicationMapper;

    @Autowired
    private SwaMapper swaMapper;

    @Autowired
    private SwaKeeperMapper swaKeeperMapper;

    @Override
    public ResponseMessage<List<SwaExtendDTO>> getSwa() {
        // 获取Swa列表数据
        return ResponseMessage.ok(swaMapper.getSwa());
    }

    @Override
    @Transactional
    public ResponseMessage<Boolean> createSwa(SwaCreateRequest swaCreateParam) {
        // 检查当前的应用是否存在 如果不存在的话抛出异常
        int applicationExistRows = applicationMapper.isApplicationExists(swaCreateParam.getApplicationId());
        if (applicationExistRows <= 0) {
            return ResponseMessage.error("创建失败，选中的应用不存在");
        }

        // 检查SWA是否存在
        int swaExistRows = swaMapper.isSwaExists(swaCreateParam.getApplicationId());
        if (swaExistRows > 0) {
            return ResponseMessage.error("创建失败，同一个应用只能创建一种类型的SWA认证");
        }

        Swa swa = new Swa();
        BeanUtil.copy(swaCreateParam, swa);
        swa.setId(UUID.randomUUID().toString());
        swa.setCreateDate(new Date());
        swa.setLastUpdate(new Date());
        swaMapper.insert(swa);

        SwaKeeper swaKeeper = new SwaKeeper();
        BeanUtil.copy(swaCreateParam, swaKeeper);
        swaKeeper.setSwaId(swa.getId());
        swaKeeperMapper.insert(swaKeeper);

        return ResponseMessage.ok(true);
    }

    @Override
    public ResponseMessage<Boolean> deleteSwaByIds(List<String> ids) {
        if (!CollectionUtil.isEmpty(ids)) {
            for (String id : ids) {
                swaMapper.deleteByPrimaryKey(id);
            }

            return ResponseMessage.ok(true);
        }

        return ResponseMessage.error("参数不能为空");
    }

    @Override
    public ResponseMessage<Boolean> updateSwa(SwaCreateRequest swaCreateParam) {
        int applicationExistRows = applicationMapper.isApplicationExists(swaCreateParam.getApplicationId());
        if (applicationExistRows <= 0) {
            return ResponseMessage.error("修改失败，选中的应用不存在");
        }

        Swa swa = swaMapper.selectByPrimaryKey(swaCreateParam.getId());
        BeanUtil.copy(swaCreateParam, swa);
        swa.setLastUpdate(new Date());
        swaMapper.updateByPrimaryKey(swa);

        SwaKeeper swaKeeper = swaKeeperMapper.selectByPrimaryKey(swaCreateParam.getId());
        BeanUtil.copy(swaCreateParam, swaKeeper);
        swaKeeperMapper.updateByPrimaryKey(swaKeeper);

        return ResponseMessage.ok(true);
    }
}
