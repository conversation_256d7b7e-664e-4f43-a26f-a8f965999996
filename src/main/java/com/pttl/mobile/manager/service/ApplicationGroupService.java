package com.pttl.mobile.manager.service;

import com.pttl.mobile.manager.lib.ResponseMessage;
import com.pttl.mobile.manager.domain.po.ApplicationGroup;

import java.util.List;

public interface ApplicationGroupService {
    ResponseMessage<Boolean> createApplicationGroup(String name);

    ResponseMessage<List<ApplicationGroup>> getApplicationGroup();

    ResponseMessage<Boolean> deleteApplicationGroupByIds(List<String> ids);

    ResponseMessage<Boolean> updateApplicationGroup(String id, String name);

    ResponseMessage<Boolean> updateApplicationGroupWeight(List<String> groupIds);
}
