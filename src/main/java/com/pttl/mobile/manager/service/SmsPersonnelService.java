package com.pttl.mobile.manager.service;

import com.github.pagehelper.PageInfo;
import com.pttl.mobile.manager.constant.SmsWarnTypeEnum;
import com.pttl.mobile.manager.domain.dto.SmsPersonnelDTO;
import com.pttl.mobile.manager.domain.dto.SmsPersonnelDetailsResponseDTO;
import com.pttl.mobile.manager.domain.dto.SmsPersonnelRequestDTO;
import com.pttl.mobile.manager.domain.dto.SmsPersonnelResponseDTO;
import com.pttl.mobile.manager.domain.entity.SmsPersonnelDO;

import java.util.List;
public interface SmsPersonnelService{

    int deleteByPrimaryKey(Long id);

    int insert(SmsPersonnelDO record);

    int insertSelective(SmsPersonnelDO record);

    SmsPersonnelDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(SmsPersonnelDO record);

    int updateByPrimaryKey(SmsPersonnelDO record);

    int updateBatch(List<SmsPersonnelDO> list);

    int updateBatchSelective(List<SmsPersonnelDO> list);

    int batchInsert(List<SmsPersonnelDO> list);

    void saveOrUpdate(SmsPersonnelDTO record);

    PageInfo<SmsPersonnelResponseDTO> pageInfo(SmsPersonnelRequestDTO smsPersonnelRequestDTO);

    void deleteById(Long id);

    List<SmsPersonnelResponseDTO> getNeedSendWarningSMSMessagesPhoneBySmsType(SmsWarnTypeEnum smsWarnTypeEnum);

    SmsPersonnelDetailsResponseDTO detailsById(Long id);
}
