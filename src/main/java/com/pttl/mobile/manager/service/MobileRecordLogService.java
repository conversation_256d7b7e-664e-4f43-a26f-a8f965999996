package com.pttl.mobile.manager.service;

import com.github.pagehelper.PageInfo;
import com.pttl.mobile.manager.domain.dto.MobileRecordLogDetailsDTO;
import com.pttl.mobile.manager.domain.entity.MobileRecordLogDO;
import com.pttl.mobile.manager.domain.request.MobileRecordLogDetailsRequest;
import com.pttl.mobile.manager.domain.request.MobileRecordLogPageRequest;
import com.pttl.mobile.manager.mobile.dto.LoginStatisticsResult;
import com.pttl.mobile.manager.mobile.dto.MobileRecordLogQueryDTO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface MobileRecordLogService {


    int deleteByPrimaryKey(Long id);

    void insert(MobileRecordLogDO record);

    int insertSelective(MobileRecordLogDO record);

    /**
     * 根据主键获取
     *
     * @param id        id
     * @param yearMonth 年月份
     * @return 结果
     */
    MobileRecordLogDO selectByPrimaryKey(Long id, String yearMonth);

    int updateByPrimaryKeySelective(MobileRecordLogDO record);

    int updateByPrimaryKey(MobileRecordLogDO record);

    List<MobileRecordLogDO> selectAllOrderByIdDesc();

    /**
     * 分页获取
     *
     * @param pageRequest 入参
     * @return 结果集
     */
    PageInfo<MobileRecordLogDO> pageInfo(MobileRecordLogPageRequest pageRequest);

    /**
     * 获取详情
     *
     * @param request id和年月
     * @return 详情
     */
    MobileRecordLogDetailsDTO getDetailsById(MobileRecordLogDetailsRequest request);

    /**
     * 获取登陆统计
     *
     * @param dayType 类型
     * @return 返回统计结果
     */
    LoginStatisticsResult getLoginStatistics(String dayType);

    /**
     * 移动端分页查询
     *
     * @return 结果集
     */
    PageInfo<MobileRecordLogDO> pageInfoForMobile(MobileRecordLogQueryDTO mobileRecordLogQueryDTO);
}
