package com.pttl.mobile.manager.service;

import com.github.pagehelper.PageInfo;
import com.pttl.mobile.manager.domain.dto.FunctionalAttributesPageInfoDTO;
import com.pttl.mobile.manager.domain.entity.FunctionalAttributesDO;
import com.pttl.mobile.manager.domain.request.FunctionalAttributesCreateRequest;
import com.pttl.mobile.manager.domain.request.FunctionalAttributesPageRequest;
import com.pttl.mobile.manager.domain.request.FunctionalAttributesUpdateRequest;
import com.pttl.mobile.manager.mobile.dto.FunctionalAttributesInfoDTO;
import com.pttl.mobile.manager.mobile.vo.FunctionalAttributesVO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface FunctionalAttributesService {


    int deleteByPrimaryKey(Long id);

    int insert(FunctionalAttributesDO functionalAttributesDO);

    int insertSelective(FunctionalAttributesDO functionalAttributesDO);

    FunctionalAttributesDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(FunctionalAttributesDO functionalAttributesDO);

    int updateByPrimaryKey(FunctionalAttributesDO functionalAttributesDO);

    List<FunctionalAttributesDO> selectAllOrderByIdDesc();

    int updateBatch(List<FunctionalAttributesDO> list);

    int updateBatchSelective(List<FunctionalAttributesDO> list);

    int batchInsert(List<FunctionalAttributesDO> list);

    /**
     * 分页获取属性配置
     *
     * @param pageRequest 参数
     * @return 分页数据
     */
    PageInfo<FunctionalAttributesPageInfoDTO> pageInfo(FunctionalAttributesPageRequest pageRequest);

    /**
     * 根据id 获取详情
     *
     * @param id id
     * @return 详情信息
     */
    FunctionalAttributesPageInfoDTO getDetailsById(Long id);

    /**
     * 根据id物理删除
     *
     * @param ids id
     */
    void removeByIds(List<Long> ids);

    /**
     * 创建
     *
     * @param dataRequest 数据信息
     */
    void createData(FunctionalAttributesCreateRequest dataRequest);

    /**
     * 更新数据
     *
     * @param dataRequest 数据信息
     * @return
     */
    String updateData(FunctionalAttributesUpdateRequest dataRequest);

    /**
     * 移动端获取
     *
     * @param functionalAttributesVO
     * @return
     */
    List<FunctionalAttributesInfoDTO> infoListForMobile(FunctionalAttributesVO functionalAttributesVO);

    /**
     * 检查数据 属性id 是否重复
     *
     * @param id
     * @param functionalAttributes
     * @return
     */
    boolean checkIdOnly(Long id, String functionalAttributes);
}
