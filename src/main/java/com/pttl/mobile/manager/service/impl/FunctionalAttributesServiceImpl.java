package com.pttl.mobile.manager.service.impl;

import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.pttl.mobile.manager.constant.CommonConstants;
import com.pttl.mobile.manager.dao.FunctionalAttributesMapper;
import com.pttl.mobile.manager.domain.dto.FunctionalAttributesPageInfoDTO;
import com.pttl.mobile.manager.domain.entity.FunctionalAttributesDO;
import com.pttl.mobile.manager.domain.request.FunctionalAttributesCreateRequest;
import com.pttl.mobile.manager.domain.request.FunctionalAttributesPageRequest;
import com.pttl.mobile.manager.domain.request.FunctionalAttributesUpdateRequest;
import com.pttl.mobile.manager.mobile.dto.FunctionalAttributesInfoDTO;
import com.pttl.mobile.manager.mobile.vo.FunctionalAttributesVO;
import com.pttl.mobile.manager.service.FunctionalAttributesService;
import com.pttl.mobile.manager.util.BeanMapper;
import com.pttl.mobile.manager.util.PageUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Service
public class FunctionalAttributesServiceImpl implements FunctionalAttributesService {

    @Resource
    private FunctionalAttributesMapper functionalAttributesMapper;

    @Override
    public int deleteByPrimaryKey(Long id) {
        return functionalAttributesMapper.deleteByPrimaryKey(id);
    }

    @Override
    public int insert(FunctionalAttributesDO functionalAttributesDO) {
        return functionalAttributesMapper.insert(functionalAttributesDO);
    }

    @Override
    public int insertSelective(FunctionalAttributesDO functionalAttributesDO) {
        return functionalAttributesMapper.insertSelective(functionalAttributesDO);
    }

    @Override
    public FunctionalAttributesDO selectByPrimaryKey(Long id) {
        return functionalAttributesMapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(FunctionalAttributesDO functionalAttributesDO) {
        return functionalAttributesMapper.updateByPrimaryKeySelective(functionalAttributesDO);
    }

    @Override
    public int updateByPrimaryKey(FunctionalAttributesDO functionalAttributesDO) {
        return functionalAttributesMapper.updateByPrimaryKey(functionalAttributesDO);
    }

    @Override
    public List<FunctionalAttributesDO> selectAllOrderByIdDesc() {
        return functionalAttributesMapper.selectAllOrderByIdDesc();
    }

    @Override
    public int updateBatch(List<FunctionalAttributesDO> list) {
        return functionalAttributesMapper.updateBatch(list);
    }

    @Override
    public int updateBatchSelective(List<FunctionalAttributesDO> list) {
        return functionalAttributesMapper.updateBatchSelective(list);
    }

    @Override
    public int batchInsert(List<FunctionalAttributesDO> list) {
        return functionalAttributesMapper.batchInsert(list);
    }

    @Override
    public PageInfo<FunctionalAttributesPageInfoDTO> pageInfo(FunctionalAttributesPageRequest pageRequest) {
        PageMethod.startPage(pageRequest.getPageNum(), pageRequest.getPageSize());

        List<FunctionalAttributesDO> list = functionalAttributesMapper.
                listByFunctionalAttributes(pageRequest.getFunctionalAttributes(),
                        pageRequest.getDataType(), pageRequest.getOperatingSystem());

        return PageUtil.page2PageVo(new PageInfo<>(list), FunctionalAttributesPageInfoDTO.class);
    }

    @Override
    public FunctionalAttributesPageInfoDTO getDetailsById(Long id) {
        FunctionalAttributesDO functionalAttributesDO = functionalAttributesMapper.selectByPrimaryKey(id);
        return BeanMapper.map(functionalAttributesDO, FunctionalAttributesPageInfoDTO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeByIds(List<Long> ids) {
        ids.forEach(id -> functionalAttributesMapper.deleteByPrimaryKey(id));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createData(FunctionalAttributesCreateRequest dataRequest) {
        FunctionalAttributesDO functionalAttributesDO = initFunctionalAttributes(dataRequest);
        this.insert(functionalAttributesDO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String updateData(FunctionalAttributesUpdateRequest dataRequest) {
        FunctionalAttributesDO functionalAttributesDO = this.selectByPrimaryKey(dataRequest.getId());
        if (Objects.nonNull(functionalAttributesDO)) {
            functionalAttributesDO.setFunctionalAttributes(dataRequest.getFunctionalAttributes());
            functionalAttributesDO.setAttributeValue(dataRequest.getAttributeValue());
            functionalAttributesDO.setRemark(dataRequest.getRemark());
            functionalAttributesDO.setUpdateTime(new Date());
            functionalAttributesDO.setOperatingSystem(dataRequest.getOperatingSystem());
            functionalAttributesDO.setDataType(dataRequest.getDataType());
            this.updateByPrimaryKey(functionalAttributesDO);

            return null;
        }

        return CommonConstants.ERROR_MESSAGE_DATA_NOT_EXIST;
    }

    @Override
    public List<FunctionalAttributesInfoDTO> infoListForMobile(FunctionalAttributesVO functionalAttributesVO) {
        List<FunctionalAttributesDO> list = functionalAttributesMapper.
                listByFunctionalAttributesForMobile(null,
                        functionalAttributesVO.getDataType(), functionalAttributesVO.getOperatingSystem());
        return BeanMapper.mapList(list, FunctionalAttributesInfoDTO.class);
    }

    @Override
    public boolean checkIdOnly(Long id, String functionalAttributes) {
        // 不存在 时
        if (StrUtil.isEmpty(functionalAttributes)) {
            return true;
        }


        FunctionalAttributesDO functionalAttributesDO = functionalAttributesMapper
                .getByFunctionalAttributes(functionalAttributes.trim());
        // 新增时 只检查属性id
        if (Objects.isNull(id)) {
            return Objects.isNull(functionalAttributesDO);
        }

        // 不存在 时
        if (Objects.isNull(functionalAttributesDO)) {
            return true;
        }

        // 修改时 需要检验数据id和属性id
        return Objects.equals(id, functionalAttributesDO.getId());
    }

    /**
     * 初始化
     *
     * @param dataRequest 数据
     * @return
     */
    private FunctionalAttributesDO initFunctionalAttributes(FunctionalAttributesCreateRequest dataRequest) {
        FunctionalAttributesDO functionalAttributesDO = BeanMapper.map(dataRequest, FunctionalAttributesDO.class);
        functionalAttributesDO.setCreateTime(new Date());
        functionalAttributesDO.setUpdateTime(new Date());
        return functionalAttributesDO;
    }

}
