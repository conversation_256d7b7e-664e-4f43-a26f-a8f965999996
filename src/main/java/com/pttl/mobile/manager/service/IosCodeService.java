package com.pttl.mobile.manager.service;

import com.github.pagehelper.PageInfo;
import com.pttl.mobile.manager.domain.dto.ExchangeDetailsDTO;
import com.pttl.mobile.manager.domain.dto.ExchangeStatisticsDTO;
import com.pttl.mobile.manager.domain.dto.IosCodePageDTO;
import com.pttl.mobile.manager.domain.dto.UserUsageStatisticsDTO;
import com.pttl.mobile.manager.domain.entity.IosCodeDO;
import com.pttl.mobile.manager.domain.request.IosCodePageRequest;
import com.pttl.mobile.manager.excel.dto.ImportIosCodeDataDTO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface IosCodeService {


    int deleteByPrimaryKey(Long id);

    int insert(IosCodeDO record);

    int insertSelective(IosCodeDO record);

    IosCodeDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(IosCodeDO record);

    int updateByPrimaryKey(IosCodeDO record);

    int updateBatch(List<IosCodeDO> list);

    int batchInsert(List<IosCodeDO> list);

    /**
     * 批量保存导入用户
     *
     * @param importIosCodeList 数据
     */
    void saveImportList(List<ImportIosCodeDataDTO> importIosCodeList);

    /**
     * 分页获取数据
     *
     * @param pageRequest 分页参数
     * @return 结果
     */
    PageInfo<IosCodePageDTO> pageInfo(IosCodePageRequest pageRequest);

    /**
     * 批量删除
     *
     * @param ids id
     */
    void iosCodeRemove(List<Long> ids);

    /**
     * 获取一个ios code url
     *
     * @return url
     */
    String getIosCodeQrUrl(Integer appType);

    /**
     * 统计用户使用情况
     *
     * @return 结果列表
     */
    List<UserUsageStatisticsDTO> getCount();

    /**
     * 统计未使用情况
     *
     * @return 结果列表
     */
    List<UserUsageStatisticsDTO> unusedQuantity();

    /**
     * 兑换统计
     *
     * @return 结果
     */
    ExchangeStatisticsDTO getExchangeStatistics(Integer appType);

    /**
     * 兑换详情
     *
     * @param dataType dataType: 1今日 2近一周 3近一个月 4近一年 5所有
     * @return 结果
     */
    List<ExchangeDetailsDTO> getExchangeDetails(Integer dataType, Integer appType);

    /**
     * 检查ios code数量是否超过预警值
     *
     * @param smsGateway 短信网关
     */
    void checkIosCodeQuantitySendWarningSMSMessages(String smsGateway);
}
