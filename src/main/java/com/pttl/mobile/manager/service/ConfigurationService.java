package com.pttl.mobile.manager.service;

import com.pttl.mobile.manager.lib.ResponseMessage;
import com.pttl.mobile.manager.domain.dto.ConfigurationDTO;

import java.util.List;

public interface ConfigurationService {
    ResponseMessage<List<ConfigurationDTO<Object>>> getConfiguration();

    <T> ConfigurationDTO<T> getConfigurationByName(String name, Class<T> clazz);

    ResponseMessage<Boolean> insertOrUpdate(ConfigurationDTO<Object> setting);
}
