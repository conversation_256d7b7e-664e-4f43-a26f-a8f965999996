package com.pttl.mobile.manager.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.pttl.mobile.manager.constant.CommonConstants;
import com.pttl.mobile.manager.dao.Department2Mapper;
import com.pttl.mobile.manager.domain.dto.DepartmentQueryDTO;
import com.pttl.mobile.manager.domain.dto.DepartmentTreeDTO;
import com.pttl.mobile.manager.domain.dto.PolicyDepartmentConfigurationDTO;
import com.pttl.mobile.manager.domain.dto.SameLevelDepartmentDTO;
import com.pttl.mobile.manager.domain.entity.Department2DO;
import com.pttl.mobile.manager.domain.request.DepartmentPageRequest;
import com.pttl.mobile.manager.domain.request.DepartmentSaveRequest;
import com.pttl.mobile.manager.domain.request.DepartmentUpdateRequest;
import com.pttl.mobile.manager.excel.dto.ExportDepartmentDataDTO;
import com.pttl.mobile.manager.mobile.exception.ErrorMessageException;
import com.pttl.mobile.manager.service.Department2Service;
import com.pttl.mobile.manager.service.UserService;
import com.pttl.mobile.manager.util.BeanMapper;
import com.pttl.mobile.manager.util.PageUtil;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@Service
public class Department2ServiceImpl implements Department2Service {

    @Resource
    private Department2Mapper department2Mapper;

    /**
     * 用户服务层
     */
    @Resource
    private UserService userService;

    /**
     * redis客户端
     */
    @Resource
    private RedisTemplate<String, HashMap<Long, Department2DO>> redisTemplate;

    @Override
    public int deleteByPrimaryKey(Long id) {
        return department2Mapper.deleteByPrimaryKey(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insert(Department2DO record) {
        return department2Mapper.insert(record);
    }

    @Override
    public int insertSelective(Department2DO record) {
        return department2Mapper.insertSelective(record);
    }

    @Override
    public Department2DO selectByPrimaryKey(Long id) {
        return department2Mapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(Department2DO record) {
        return department2Mapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(Department2DO record) {
        return department2Mapper.updateByPrimaryKey(record);
    }

    @Override
    public int updateBatch(List<Department2DO> list) {
        return department2Mapper.updateBatch(list);
    }

    @Override
    public int updateBatchSelective(List<Department2DO> list) {
        return department2Mapper.updateBatchSelective(list);
    }

    @Override
    public int batchInsert(List<Department2DO> list) {
        return department2Mapper.batchInsert(list);
    }

    @Override
    public List<DepartmentTreeDTO> listTree() {
        List<Department2DO> departmentList = department2Mapper.listAll();
        // 如果无数据直接返回
        if (CollUtil.isEmpty(departmentList)) {
            return CollUtil.newArrayList();
        }

        // 有数据进行递归处理为树形数据
        return processDepartmentList2DepartmentTree(departmentList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveDepartment(DepartmentSaveRequest request) {
        // 检查是否 父级是否存在
        Department2DO department2DO = department2Mapper.selectByPrimaryKey(request.getParentDepartmentId());
        if (Objects.isNull(department2DO)) {
            throw new ErrorMessageException("父级部门不存在");
        }
        // 检查是否 同级已存在
        department2DO = department2Mapper.getOneByNameAndParentDepartmentId(request.getName(),
                request.getParentDepartmentId());
        if (!Objects.isNull(department2DO)) {
            throw new ErrorMessageException("同级部门已存在");
        }

        department2DO = BeanMapper.map(request, Department2DO.class);

        // 创建 初始化部门 手动创建
        Department2DO defaultDepartment = createDefaultDepartment(department2DO, Department2DO.SOURCE_TYPE_MANUAL_CONTROL);
        insert(defaultDepartment);

        // 更新内容写入缓存
        redisTemplate.opsForHash().put(CommonConstants.DEPARTMENT_NAME_MAP_NAME, department2DO.getId(), department2DO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateDepartment(DepartmentUpdateRequest request) {
        Department2DO department2DO = selectByPrimaryKey(request.getId());
        // 检查是否 同级已存在
        Department2DO department2 = department2Mapper.getOneByNameAndParentDepartmentId(request.getName(),
                department2DO.getParentDepartmentId());
        if (Objects.nonNull(department2) && !Objects.equals(department2.getId(), department2DO.getId())) {
            throw new ErrorMessageException("同级部门已存在");
        }

        department2DO.setName(request.getName());
        department2DO.setDescription(request.getDescription());
        department2DO.setUpdateTime(new Date());
        updateByPrimaryKey(department2DO);
        // 更新缓存
        redisTemplate.opsForHash().put(CommonConstants.DEPARTMENT_NAME_MAP_NAME, department2DO.getId(), department2DO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchRemoveDepartment(List<Long> ids) {
        ids.forEach(this::deleteByPrimaryKey);
        // 缓存也删除
        ids.forEach(aLong -> redisTemplate.opsForHash().delete(CommonConstants.DEPARTMENT_NAME_MAP_NAME, aLong));
    }

    @Override
    public String getFullDepartmentName(Long departmentId) {
        Department2DO department2DO = selectByPrimaryKey(departmentId);
        if (Objects.isNull(department2DO)) {
            return null;
        }

        // 如果redis中没有 则初始化
        boolean initSuccess = initRedisFullDepartmentNameMap();
        if (initSuccess) {
            // 不存在全路径记录 就一层层往上找
            if (StrUtil.isEmpty(department2DO.getFullDepartmentId())) {
                List<String> list = CollUtil.newArrayList();
                findParentDepartmentName(department2DO.getId(), list);
                Collections.reverse(list);
                StringJoiner fullDepartmentNameJoiner = new StringJoiner("/");
                for (String departmentName : list) {
                    fullDepartmentNameJoiner.add(departmentName.trim());
                }
                return fullDepartmentNameJoiner.toString();
            }
            // 存在全路径 则分割拼接
            return processFullDepartmentName(department2DO.getFullDepartmentId());
        }

        return null;
    }

    @Override
    public PageInfo<PolicyDepartmentConfigurationDTO> pageListByParentDepartmentId(DepartmentPageRequest request) {
        PageMethod.startPage(request.getPageNum(), request.getPageSize());
        List<Department2DO> dataList = department2Mapper.listByParentDepartmentId(request.getDepartmentId());
        if (CollUtil.isEmpty(dataList)) {
            return new PageInfo<>();
        }

        List<PolicyDepartmentConfigurationDTO> policyDepartmentConfigurationDTOS = BeanMapper.mapList(dataList, PolicyDepartmentConfigurationDTO.class);
        policyDepartmentConfigurationDTOS.forEach(policyDepartmentConfigurationDTO -> {
            // 设置全路径
            String fullDepartmentName = this.getFullDepartmentName(policyDepartmentConfigurationDTO.getId());
            policyDepartmentConfigurationDTO.setFullDepartmentName(fullDepartmentName);
            // 统计用户
            Integer departmentUserNum = userService.countByDepartmentId(policyDepartmentConfigurationDTO.getId());
            policyDepartmentConfigurationDTO.setDepartmentUserNum(departmentUserNum);
        });


        return PageUtil.page2PageVo(new PageInfo<>(dataList), policyDepartmentConfigurationDTOS);
    }

    @Override
    public List<Department2DO> listAll() {
        List<Department2DO> departmentList = department2Mapper.listAll();
        // 如果无数据直接返回
        if (CollUtil.isEmpty(departmentList)) {
            return CollUtil.newArrayList();
        }

        return departmentList;
    }

    @Override
    public List<ExportDepartmentDataDTO> exportDepartmentListAll() {
        List<Department2DO> department2DOS = listAll();
        if (CollUtil.isEmpty(department2DOS)) {
            return CollUtil.newArrayList();
        }

        // 处理为导出数据结构
        List<ExportDepartmentDataDTO> resultList = CollUtil.newArrayList();
        department2DOS.forEach(department2DO -> {
            ExportDepartmentDataDTO exportDepartmentDataDTO = new ExportDepartmentDataDTO();
            exportDepartmentDataDTO.setId(department2DO.getId());
            exportDepartmentDataDTO.setName(department2DO.getName());
            String fullDepartmentName = getFullDepartmentName(department2DO.getId());
            exportDepartmentDataDTO.setFullDepartmentName(fullDepartmentName);
            resultList.add(exportDepartmentDataDTO);
        });
        return resultList;
    }

    @Override
    public DepartmentQueryDTO getByDepartmentId(Long departmentId) {
        Department2DO department2DO = this.selectByPrimaryKey(departmentId);

        if (Objects.nonNull(department2DO)) {
            DepartmentQueryDTO departmentQueryDTO = new DepartmentQueryDTO();
            departmentQueryDTO.setId(department2DO.getId());
            departmentQueryDTO.setName(department2DO.getName());
            departmentQueryDTO.setDescription(department2DO.getDescription());
            // 设置部门全路径
            String fullDepartmentName = this.getFullDepartmentName(department2DO.getId());
            departmentQueryDTO.setFullDepartmentName(fullDepartmentName);
            return departmentQueryDTO;
        }

        return null;
    }

    @Override
    public List<SameLevelDepartmentDTO> listSameLevelDepartment(Long departmentId) {
        return department2Mapper.listSameLevelDepartment(departmentId);
    }

    /**
     * 使用全路径进行拼接
     *
     * @param fullDepartmentId
     * @return
     */
    private String processFullDepartmentName(String fullDepartmentId) {
        if (StrUtil.isEmpty(fullDepartmentId)) {
            return null;
        }

        // 根据部门全路径id 分割处理 分别获取部门名称
        String[] splitDepartmentIds = fullDepartmentId.split("/");
        StringJoiner fullDepartmentNameJoiner = new StringJoiner("/");
        for (String splitDepartmentId : splitDepartmentIds) {
            if (Objects.nonNull(splitDepartmentId)) {
                Department2DO department = (Department2DO) redisTemplate.opsForHash()
                        .get(CommonConstants.DEPARTMENT_NAME_MAP_NAME, splitDepartmentId);
                fullDepartmentNameJoiner.add(department.getName().trim());
            }
        }

        return fullDepartmentNameJoiner.toString();
    }

    /**
     * 递归循环一层层的往上找 上级部门
     *
     * @param departmentId
     * @param list
     * @return
     */
    private List<String> findParentDepartmentName(Long departmentId, List<String> list) {
        Department2DO department = (Department2DO) redisTemplate.opsForHash()
                .get(CommonConstants.DEPARTMENT_NAME_MAP_NAME, departmentId);
        if (Objects.nonNull(department) && Objects.nonNull(department.getParentDepartmentId())) {
            list.add(department.getName());
        }

        // 到达根节点时结束继续查找
        if (Objects.isNull(department) || department.getParentDepartmentId() == Department2DO.ROOT_PARENT_DEPARTMENT_ID) {
            return list;
        }

        return findParentDepartmentName(department.getParentDepartmentId(), list);
    }


    /**
     * 初始化redis中的 部门id和名称  map
     *
     * @return
     */
    private boolean initRedisFullDepartmentNameMap() {
        Long mapSize = redisTemplate.opsForHash().size(CommonConstants.DEPARTMENT_NAME_MAP_NAME);
        if (Objects.isNull(mapSize) || mapSize <= 0) {
            List<Department2DO> listAll = department2Mapper.listAll();
            // 没有数据则返回空
            if (CollUtil.isEmpty(listAll)) {
                return false;
            }
            // 初始化
            Map<Long, Department2DO> fullDepartmentNameMap = listAll.stream().collect(Collectors
                    .toMap(Department2DO::getId, department2DO -> department2DO));
            redisTemplate.opsForHash().putAll(CommonConstants.DEPARTMENT_NAME_MAP_NAME, fullDepartmentNameMap);
        }
        return true;
    }

    /**
     * 创建默认部门
     *
     * @param department2DO
     * @return
     */
    private Department2DO createDefaultDepartment(Department2DO department2DO, int source) {
        if (Objects.isNull(department2DO)) {
            department2DO = new Department2DO();
        }

        // 如果父id为空, 则创建为根
        if (Objects.isNull(department2DO.getParentDepartmentId())) {
            department2DO.setParentDepartmentId(Department2DO.ROOT_PARENT_DEPARTMENT_ID);
        }

        // 设置需要初始化的属性
        department2DO.setSource(source);
        department2DO.setValidStatus(Department2DO.VALID_STATUS_VALID);
        department2DO.setUserDeviceNum(Department2DO.USER_DEVICE_NUM_3);
        department2DO.setDeviceSwitchStatus(Department2DO.DEVICE_SWITCH_STATUS_ON);
        department2DO.setCreateTime(new Date());
        department2DO.setUpdateTime(new Date());

        return department2DO;
    }

    /**
     * 处理部门集合转成部门树形结构数据
     *
     * @param departmentList 所有部门数据
     * @return
     */
    private List<DepartmentTreeDTO> processDepartmentList2DepartmentTree(List<Department2DO> departmentList) {
        // 寻找出所有根
        List<DepartmentTreeDTO> rootDepartmentTree = processRootDepartmentList(departmentList);

        // 各个根下子级 每一项都代表一颗树
        List<DepartmentTreeDTO> departmentTree = new ArrayList<>();

        //从最高级别部门开始遍历，递归找到该部门的下级部门，将带有下级的最高级部门放入返回结果中
        for (DepartmentTreeDTO rootDepartment : rootDepartmentTree) {
            buildDepartmentTree(departmentList, rootDepartment);
            departmentTree.add(rootDepartment);
        }

        return departmentTree;
    }

    /**
     * 处理根节点
     *
     * @param departmentList
     * @return
     */
    private List<DepartmentTreeDTO> processRootDepartmentList(List<Department2DO> departmentList) {
        List<DepartmentTreeDTO> rootDepartmentTree = new ArrayList<>();

        // 处理当前数据是否需要进行递归
        for (Department2DO departmentDO : departmentList) {
            // 当前部门不为空 且父节点为根节点
            if (Objects.nonNull(departmentDO) &&
                    departmentDO.getParentDepartmentId() == Department2DO.ROOT_PARENT_DEPARTMENT_ID) {
                DepartmentTreeDTO departmentTreeDTO = BeanMapper.map(departmentDO, DepartmentTreeDTO.class);
                rootDepartmentTree.add(departmentTreeDTO);
            }
        }
        return rootDepartmentTree;
    }


    /**
     * 构建部门树
     *
     * @param departmentList
     * @param rootDepartment
     * @return
     */
    private DepartmentTreeDTO buildDepartmentTree(List<Department2DO> departmentList, DepartmentTreeDTO rootDepartment) {
        List<DepartmentTreeDTO> children = new ArrayList<>();
        // 遍历查到的所有部门
        for (Department2DO department2DO : departmentList) {
            // 0代表根节点，无需重复比较
            if (department2DO.getParentDepartmentId() == Department2DO.ROOT_PARENT_DEPARTMENT_ID) {
                continue;
            }

            // 当前部门的上级编号和传入的部门编号相等，表示该部门是传入部门的下级部门
            if (Objects.equals(department2DO.getParentDepartmentId(), rootDepartment.getId())) {
                // 递归调用，再去寻找该用户的下级部门
                DepartmentTreeDTO departmentTreeDTO = BeanMapper.map(department2DO, DepartmentTreeDTO.class);
                DepartmentTreeDTO childDepartmentTree = buildDepartmentTree(departmentList, departmentTreeDTO);
                // 当前部门是该用户的一个下级用户，放入children集合内
                children.add(childDepartmentTree);
            }
        }

        // 给该部门的children属性赋值，并返回该用户
        rootDepartment.setChildDepartments(children);
        return rootDepartment;
    }

}
