package com.pttl.mobile.manager.service.impl;

import com.pttl.mobile.manager.domain.po.Department;
import com.pttl.mobile.manager.domain.request.DepartmentSaveRequest;
import com.pttl.mobile.manager.lib.ResponseMessage;
import com.pttl.mobile.manager.service.DepartmentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
public class DepartmentServiceImpl implements DepartmentService {
    @Override
    public ResponseMessage<List<Department>> getDepartment() {
        return null;
    }

    @Override
    public ResponseMessage<Boolean> createDepartment(DepartmentSaveRequest request) {
        return null;
    }

    @Override
    public ResponseMessage<Boolean> updateDepartment(DepartmentSaveRequest request) {
        return null;
    }

    @Override
    public ResponseMessage<Boolean> deleteDepartmentByIds(List<String> ids) {
        return null;
    }
}