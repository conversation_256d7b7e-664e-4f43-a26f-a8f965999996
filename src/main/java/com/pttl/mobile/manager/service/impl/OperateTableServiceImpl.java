package com.pttl.mobile.manager.service.impl;

import com.pttl.mobile.manager.dao.OperateTableMapper;
import com.pttl.mobile.manager.service.OperateTableService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 操作数据库表 服务实现
 *
 * <AUTHOR>
 * @date 2022/11/10
 **/
@Service
public class OperateTableServiceImpl implements OperateTableService {

    /**
     * 操作数据库表mapper
     */
    @Resource
    private OperateTableMapper operateTableMapper;

    @Override
    public int existTable(String tableName) {
        return operateTableMapper.existTable(tableName);
    }

    @Override
    public int dropTable(String tableName) {
        return operateTableMapper.dropTable(tableName);
    }

    @Override
    public int createTableOfMobileRecordLog(String tableName) {
        return operateTableMapper.createTableOfMobileRecordLog(tableName);
    }
}
