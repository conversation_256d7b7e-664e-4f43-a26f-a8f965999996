package com.pttl.mobile.manager.service;

import com.pttl.mobile.manager.constant.SmsWarnTypeEnum;
import com.pttl.mobile.manager.domain.dto.SmsRelationDTO;
import com.pttl.mobile.manager.domain.entity.SmsRelationDO;

import java.util.List;
public interface SmsRelationService{

    int deleteByPrimaryKey(Long id);

    int insert(SmsRelationDO record);

    int insertSelective(SmsRelationDO record);

    SmsRelationDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(SmsRelationDO record);

    int updateByPrimaryKey(SmsRelationDO record);

    int updateBatch(List<SmsRelationDO> list);

    int updateBatchSelective(List<SmsRelationDO> list);

    int batchInsert(List<SmsRelationDO> list);

    int saveOrUpdate(SmsRelationDTO record);

    void save(Long id, Long modelId, String smsCondition, SmsWarnTypeEnum smsWarnTypeEnum);

    void deleteByPersonId(Long id);
}
