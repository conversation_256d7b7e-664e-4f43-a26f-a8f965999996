package com.pttl.mobile.manager.service.impl;

import com.pttl.mobile.manager.dao.StrategyApplication2Mapper;
import com.pttl.mobile.manager.domain.dto.ApplicationDTO;
import com.pttl.mobile.manager.domain.entity.StrategyApplication2DO;
import com.pttl.mobile.manager.service.StrategyApplication2Service;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

@Service
public class StrategyApplication2ServiceImpl implements StrategyApplication2Service {

    @Resource
    private StrategyApplication2Mapper strategyApplication2Mapper;

    @Override
    public int deleteByPrimaryKey(Integer id) {
        return strategyApplication2Mapper.deleteByPrimaryKey(id);
    }

    @Override
    public int insert(StrategyApplication2DO record) {
        return strategyApplication2Mapper.insert(record);
    }

    @Override
    public int insertSelective(StrategyApplication2DO record) {
        return strategyApplication2Mapper.insertSelective(record);
    }

    @Override
    public StrategyApplication2DO selectByPrimaryKey(Integer id) {
        return strategyApplication2Mapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(StrategyApplication2DO record) {
        return strategyApplication2Mapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(StrategyApplication2DO record) {
        return strategyApplication2Mapper.updateByPrimaryKey(record);
    }

    @Override
    public int updateBatch(List<StrategyApplication2DO> list) {
        return strategyApplication2Mapper.updateBatch(list);
    }

    @Override
    public int updateBatchSelective(List<StrategyApplication2DO> list) {
        return strategyApplication2Mapper.updateBatchSelective(list);
    }

    @Override
    public int batchInsert(List<StrategyApplication2DO> list) {
        return strategyApplication2Mapper.batchInsert(list);
    }

    @Override
    public List<ApplicationDTO> getApplicationByStrategyId(Long id) {
        return strategyApplication2Mapper.listByStrategyId(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByStrategyId(Long strategyId) {
        strategyApplication2Mapper.deleteByStrategyId(strategyId);
    }

}
