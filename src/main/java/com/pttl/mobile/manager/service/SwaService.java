package com.pttl.mobile.manager.service;

import com.pttl.mobile.manager.domain.dto.SwaExtendDTO;
import com.pttl.mobile.manager.lib.ResponseMessage;
import com.pttl.mobile.manager.domain.po.Swa;
import com.pttl.mobile.manager.domain.request.SwaCreateRequest;

import java.util.List;

public interface SwaService {
    ResponseMessage<List<SwaExtendDTO>> getSwa();

    ResponseMessage<Boolean> createSwa(SwaCreateRequest swaCreateParam);

    ResponseMessage<Boolean> deleteSwaByIds(List<String> ids);

    ResponseMessage<Boolean> updateSwa(SwaCreateRequest scp);
}
