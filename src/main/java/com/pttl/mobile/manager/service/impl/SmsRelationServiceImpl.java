package com.pttl.mobile.manager.service.impl;

import com.pttl.mobile.manager.constant.SmsWarnTypeEnum;
import com.pttl.mobile.manager.dao.SmsRelationMapper;
import com.pttl.mobile.manager.domain.dto.SmsRelationDTO;
import com.pttl.mobile.manager.domain.entity.SmsRelationDO;
import com.pttl.mobile.manager.service.SmsRelationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
@Service
public class SmsRelationServiceImpl implements SmsRelationService{

    @Autowired
    private SmsRelationMapper smsRelationMapper;

    @Override
    public int deleteByPrimaryKey(Long id) {
        return smsRelationMapper.deleteByPrimaryKey(id);
    }

    @Override
    public int insert(SmsRelationDO record) {
        return smsRelationMapper.insert(record);
    }

    @Override
    public int insertSelective(SmsRelationDO record) {
        return smsRelationMapper.insertSelective(record);
    }

    @Override
    public SmsRelationDO selectByPrimaryKey(Long id) {
        return smsRelationMapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(SmsRelationDO record) {
        return smsRelationMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(SmsRelationDO record) {
        return smsRelationMapper.updateByPrimaryKey(record);
    }

    @Override
    public int updateBatch(List<SmsRelationDO> list) {
        return smsRelationMapper.updateBatch(list);
    }

    @Override
    public int updateBatchSelective(List<SmsRelationDO> list) {
        return smsRelationMapper.updateBatchSelective(list);
    }

    @Override
    public int batchInsert(List<SmsRelationDO> list) {
        return smsRelationMapper.batchInsert(list);
    }

    @Override
    public int saveOrUpdate(SmsRelationDTO record) {
        record.setSmsType(SmsWarnTypeEnum.IOS_CODE.getType());
        return 0;
    }

    @Override
    public void save(Long id, Long modelId, String smsCondition, SmsWarnTypeEnum smsWarnTypeEnum) {
        // 先删
        smsRelationMapper.deleteByPrimaryKey(id);

        // 后增
        SmsRelationDO smsRelationDO = new SmsRelationDO();
        smsRelationDO.setModelId(modelId);
        smsRelationDO.setPersonnelId(id);
        smsRelationDO.setSmsCondition(smsCondition);
        smsRelationDO.setSmsType(smsWarnTypeEnum.getType());
        smsRelationMapper.insertSelective(smsRelationDO);
    }

    @Override
    public void deleteByPersonId(Long id) {

        smsRelationMapper.deleteByPersonId(id);
    }

}
