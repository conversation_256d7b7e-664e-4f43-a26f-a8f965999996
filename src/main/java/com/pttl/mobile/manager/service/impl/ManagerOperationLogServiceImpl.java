package com.pttl.mobile.manager.service.impl;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.pttl.mobile.manager.dao.ManagerOperationLogMapper;
import com.pttl.mobile.manager.domain.dto.ManagerOperationLogDTO;
import com.pttl.mobile.manager.domain.dto.ManagerOperationLogDetailsDTO;
import com.pttl.mobile.manager.domain.dto.ManagerOperationLogPageResultDTO;
import com.pttl.mobile.manager.domain.entity.ManagerOperationLogDO;
import com.pttl.mobile.manager.domain.request.ManagerOperationLogPageRequest;
import com.pttl.mobile.manager.service.ManagerOperationLogService;
import com.pttl.mobile.manager.util.BeanMapper;
import com.pttl.mobile.manager.util.PageUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class ManagerOperationLogServiceImpl implements ManagerOperationLogService {

    @Resource
    private ManagerOperationLogMapper managerOperationLogMapper;

    @Override
    public int deleteByPrimaryKey(Long id) {
        return managerOperationLogMapper.deleteByPrimaryKey(id);
    }

    @Override
    public int insert(ManagerOperationLogDO record) {
        return managerOperationLogMapper.insert(record);
    }

    @Override
    public int insertSelective(ManagerOperationLogDO record) {
        return managerOperationLogMapper.insertSelective(record);
    }

    @Override
    public ManagerOperationLogDO selectByPrimaryKey(Long id) {
        return managerOperationLogMapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(ManagerOperationLogDO record) {
        return managerOperationLogMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(ManagerOperationLogDO record) {
        return managerOperationLogMapper.updateByPrimaryKey(record);
    }

    @Override
    public List<ManagerOperationLogDO> selectAllOrderByIdDesc() {
        return managerOperationLogMapper.selectAllOrderByIdDesc();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveManagerOperationLog(ManagerOperationLogDTO managerOperationLogDTO) {
        ManagerOperationLogDO managerOperationLogDO = BeanMapper.map(managerOperationLogDTO, ManagerOperationLogDO.class);
        managerOperationLogDO.setCreateTime(new Date());
        this.insert(managerOperationLogDO);
    }

    @Override
    public PageInfo<ManagerOperationLogPageResultDTO> pageInfo(ManagerOperationLogPageRequest pageRequest) {
        PageMethod.startPage(pageRequest.getPageNum(), pageRequest.getPageSize());

        List<ManagerOperationLogDO> dataList = managerOperationLogMapper.listByPageParameter(pageRequest);
        return PageUtil.page2PageVo(new PageInfo<>(dataList), ManagerOperationLogPageResultDTO.class);
    }

    @Override
    public ManagerOperationLogDetailsDTO getDetailsById(Long id) {
        return BeanMapper.map(this.selectByPrimaryKey(id), ManagerOperationLogDetailsDTO.class);
    }

}
