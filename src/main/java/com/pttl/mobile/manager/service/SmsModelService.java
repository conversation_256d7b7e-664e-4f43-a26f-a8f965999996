package com.pttl.mobile.manager.service;

import com.pttl.mobile.manager.domain.dto.SmsModelDTO;
import com.pttl.mobile.manager.domain.dto.SmsModelResponseDTO;
import com.pttl.mobile.manager.domain.entity.SmsModelDO;

import java.util.List;
public interface SmsModelService{

    int deleteByPrimaryKey(Long id);

    int insert(SmsModelDO record);

    int insertSelective(SmsModelDO record);

    SmsModelDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(SmsModelDO record);

    int updateByPrimaryKey(SmsModelDO record);

    int updateBatch(List<SmsModelDO> list);

    int updateBatchSelective(List<SmsModelDO> list);

    int batchInsert(List<SmsModelDO> list);

    void saveOrUpdate(SmsModelDTO record);

    List<SmsModelResponseDTO> list();

}
