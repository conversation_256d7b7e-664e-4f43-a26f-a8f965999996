package com.pttl.mobile.manager.service;

import com.pttl.mobile.manager.lib.ResponseMessage;
import com.pttl.mobile.manager.domain.po.Release;
import com.pttl.mobile.manager.domain.po.ReleaseType;
import com.pttl.mobile.manager.domain.dto.ReleaseExtendDTO;

import java.util.Date;
import java.util.List;

public interface ReleaseService {
    ResponseMessage<List<ReleaseType>> getReleaseType();

    ResponseMessage<List<ReleaseExtendDTO>> getReleaseHistory(Integer type, String version, String keyword, Date startDate, Date endDate);

    ResponseMessage<Boolean> createRelease(Release release);

    ResponseMessage<Boolean> deleteReleaseByIds(List<String> ids);
}