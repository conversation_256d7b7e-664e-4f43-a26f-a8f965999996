package com.pttl.mobile.manager.service.impl;

import com.pttl.mobile.manager.dao.ReleaseMallMapper;
import com.pttl.mobile.manager.dao.ReleaseTypeMapper;
import com.pttl.mobile.manager.domain.dto.ReleaseExtendDTO;
import com.pttl.mobile.manager.domain.po.Release;
import com.pttl.mobile.manager.domain.po.ReleaseType;
import com.pttl.mobile.manager.lib.ResponseMessage;
import com.pttl.mobile.manager.service.ReleaseMallService;
import com.pttl.mobile.manager.util.CollectionUtil;
import com.pttl.mobile.manager.util.DateUtil;
import com.pttl.mobile.manager.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.UUID;

@Slf4j
@Service
public class ReleaseMallServiceImpl implements ReleaseMallService {

    @Autowired
    private ReleaseMallMapper releaseMallMapper;

    @Autowired
    private ReleaseTypeMapper releaseTypeMapper;

    @Override
    public ResponseMessage<List<ReleaseType>> getReleaseType() {
        // 获取所有的发布类型 例如 适配包更新/客户端更新等..
        List<ReleaseType> releaseTypes = releaseTypeMapper.getType();

        return ResponseMessage.ok(releaseTypes);
    }

    @Override
    public ResponseMessage<List<ReleaseExtendDTO>> getReleaseHistory(Integer type, String version, String keyword, Date startDate, Date endDate) {
        if (endDate != null) {
            endDate = DateUtil.addDays(endDate, 1);
        }

        // 通过一些参数查询数据库
        List<ReleaseExtendDTO> releases = releaseMallMapper.getReleaseHistory(type, version, keyword, startDate, endDate);

        return ResponseMessage.ok(releases);
    }

    @Override
    public ResponseMessage<Boolean> createRelease(Release release) {
        if (release == null) {
            return ResponseMessage.error("参数不能为空");
        }

        if (release.getType() != null && release.getType() == 1 && StringUtil.isEmpty(release.getVersion())) {
            log.error("版本号为空");
            return ResponseMessage.error("版本号不能为空");
        }

        // 检查要添加的Version数据是否存在
        int versionExistRows = releaseMallMapper.isVersionExists(release.getType(), release.getOperatingSystem(), release.getVersion());
        // 如果存在的话 抛出异常 否则 添加到数据库
        if (versionExistRows > 0) {
            return ResponseMessage.error("版本已存在");
        }

        release.setId(UUID.randomUUID().toString());
        release.setReleaseDate(new Date());
        releaseMallMapper.insert(release);

        return ResponseMessage.ok(true);
    }

    @Override
    public ResponseMessage<Boolean> deleteReleaseByIds(List<String> ids) {
        if (!CollectionUtil.isEmpty(ids)) {
            for (String id : ids) {
                releaseMallMapper.deleteByPrimaryKey(id);
            }

            return ResponseMessage.ok(true);
        }

        return ResponseMessage.error("参数ids不能为空");
    }
}


