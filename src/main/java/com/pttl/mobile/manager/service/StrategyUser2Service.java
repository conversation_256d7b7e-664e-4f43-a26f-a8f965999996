package com.pttl.mobile.manager.service;

import com.pttl.mobile.manager.domain.dto.UserStrategyInfoDTO;
import com.pttl.mobile.manager.domain.entity.StrategyUser2DO;
import com.pttl.mobile.manager.domain.entity.UserDO;

import java.util.List;

public interface StrategyUser2Service {


    int deleteByPrimaryKey(Integer id);

    int insert(StrategyUser2DO strategyUser2DO);

    int insertSelective(StrategyUser2DO strategyUser2DO);

    StrategyUser2DO selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(StrategyUser2DO strategyUser2DO);

    int updateByPrimaryKey(StrategyUser2DO strategyUser2DO);

    int updateBatch(List<StrategyUser2DO> list);

    int updateBatchSelective(List<StrategyUser2DO> list);

    int batchInsert(List<StrategyUser2DO> list);

    /**
     * 统计策略使用人数
     *
     * @param id
     * @return
     */
    Integer countByStrategyId(Long id);

    /**
     * 根据策略获取关联用户
     *
     * @param id
     * @return
     */
    List<UserDO> listByStrategyId(Long id);

    /**
     * 根据策略id删除
     *
     * @param id
     */
    void deleteByStrategyId(Long id);

    /**
     * 根据用户id 获取已关联策略
     *
     * @param userId 用户id
     * @return 用户关联策略
     */
    List<UserStrategyInfoDTO> userStrategyByUserId(Long userId);
}
