package com.pttl.mobile.manager.service;

/**
 * 操作表service
 *
 * <AUTHOR>
 * @date 2022/11/10
 **/

public interface OperateTableService {

    /**
     * 检查表是否存在
     *
     * @param tableName 表名
     * @return 0不存在
     */
    int existTable(String tableName);

    /**
     * 删除表及数据
     *
     * @param tableName 表名
     * @return 0 失败
     */
    int dropTable(String tableName);

    /**
     * 创建表移动端操作日志表  规则   mm_mobile_record_log_2022_12   mm_mobile_record_log_{年份_月份}
     *
     * @param tableName 表名
     * @return 0 失败
     */
    int createTableOfMobileRecordLog(String tableName);
}
