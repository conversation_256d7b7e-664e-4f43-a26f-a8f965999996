package com.pttl.mobile.manager.service;

import com.pttl.mobile.manager.constant.ExtendedBusinessEnum;
import com.pttl.mobile.manager.domain.dto.VrSwitchDTO;
import com.pttl.mobile.manager.domain.entity.ExtendedBusiness;

import java.util.List;

public interface ExtendedBusinessService{

    int deleteByPrimaryKey(Long id);

    int insert(ExtendedBusiness record);

    int insertSelective(ExtendedBusiness record);

    ExtendedBusiness selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ExtendedBusiness record);

    int updateByPrimaryKey(ExtendedBusiness record);

    int updateBatch(List<ExtendedBusiness> list);

    int updateBatchSelective(List<ExtendedBusiness> list);

    int batchInsert(List<ExtendedBusiness> list);

    void updateVrSwitch();

    String getByKey(ExtendedBusinessEnum extendedBusinessEnum);

    VrSwitchDTO getSwitchStatusByKey(ExtendedBusinessEnum extendedBusinessEnum);
}
