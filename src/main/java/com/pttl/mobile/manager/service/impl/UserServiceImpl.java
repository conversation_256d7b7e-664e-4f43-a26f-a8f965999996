package com.pttl.mobile.manager.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.MD5;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.pttl.mobile.manager.constant.CommonConstants;
import com.pttl.mobile.manager.dao.UserMapper;
import com.pttl.mobile.manager.domain.dto.UserDTO;
import com.pttl.mobile.manager.domain.dto.UserDetailsDTO;
import com.pttl.mobile.manager.domain.dto.UserPageDTO;
import com.pttl.mobile.manager.domain.entity.UserDO;
import com.pttl.mobile.manager.domain.request.UserPageRequest;
import com.pttl.mobile.manager.domain.request.UserSaveRequest;
import com.pttl.mobile.manager.domain.request.UserUpdateRequest;
import com.pttl.mobile.manager.excel.dto.ImportUserDataDTO;
import com.pttl.mobile.manager.mobile.exception.ErrorMessageException;
import com.pttl.mobile.manager.service.Department2Service;
import com.pttl.mobile.manager.service.UserService;
import com.pttl.mobile.manager.util.BeanMapper;
import com.pttl.mobile.manager.util.PageUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Service
public class UserServiceImpl implements UserService {

    /**
     * 读取2百条数据，批量插入一次。
     */
    private static final int BATCH_COUNT = 200;

    @Resource
    private UserMapper userMapper;

    /**
     * 部门服务层
     */
    @Resource
    private Department2Service department2Service;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteByPrimaryKey(Long id) {
        return userMapper.deleteByPrimaryKey(id);
    }

    @Override
    public int insert(UserDO record) {
        return userMapper.insert(record);
    }

    @Override
    public int insertSelective(UserDO record) {
        return userMapper.insertSelective(record);
    }

    @Override
    public UserDO selectByPrimaryKey(Long id) {
        return userMapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(UserDO record) {
        return userMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(UserDO record) {
        return userMapper.updateByPrimaryKey(record);
    }

    @Override
    public int updateBatch(List<UserDO> list) {
        return userMapper.updateBatch(list);
    }

    @Override
    public int updateBatchSelective(List<UserDO> list) {
        return userMapper.updateBatchSelective(list);
    }

    @Override
    public int batchInsert(List<UserDO> list) {
        return userMapper.batchInsert(list);
    }

    @Override
    public PageInfo<UserPageDTO> pageInfo(UserPageRequest pageRequest) {
        PageMethod.startPage(pageRequest.getPageNum(), pageRequest.getPageSize());
        // 传参并分页查询
        List<UserDO> userList = userMapper.pageInfo(pageRequest.getName(), pageRequest.getDepartmentId());

        if (CollUtil.isEmpty(userList)) {
            return new PageInfo<>();
        }

        return PageUtil.page2PageVo(new PageInfo<>(userList), UserPageDTO.class);
    }

    @Override
    public UserDetailsDTO getUserDetails(Long userId) {
        UserDO userDO = selectByPrimaryKey(userId);
        if (Objects.isNull(userDO)) {
            return new UserDetailsDTO();
        }

        // 处理部门全路径
        String fullDepartmentName = department2Service.getFullDepartmentName(userDO.getDepartmentId());

        UserDetailsDTO userDetailsDTO = BeanMapper.map(userDO, UserDetailsDTO.class);
        userDetailsDTO.setFullDepartmentName(fullDepartmentName);
        return userDetailsDTO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void userDisabled(Long userId) {
        UserDO userDO = selectByPrimaryKey(userId);
        // 未禁用 则改为 禁用
        if (Objects.equals(UserDO.DISABLED_STATUS_NOT_DISABLED, userDO.getDisabledStatus())) {
            userDO.setDisabledStatus(UserDO.DISABLED_STATUS_DISABLED);
            updateByPrimaryKey(userDO);
            return;
        }
        // 禁用 则改为 未禁用
        userDO.setDisabledStatus(UserDO.DISABLED_STATUS_NOT_DISABLED);
        updateByPrimaryKey(userDO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void userCheckAndActivate(Long userId) {
        UserDO userDO = selectByPrimaryKey(userId);
        // 未激活则激活
        if (UserDO.ACTIVATED_STATUS_INACTIVATED == userDO.getActivatedStatus()) {
            userDO.setActivatedStatus(UserDO.ACTIVATED_STATUS_ACTIVATED);
            updateByPrimaryKey(userDO);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void userRemove(List<Long> ids) {
        ids.forEach(this::deleteByPrimaryKey);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createUser(UserSaveRequest userSaveRequest) {
        // 登录名称不能重复
        UserDTO userDTO = selectByLoginName(userSaveRequest.getLoginName());
        if (Objects.nonNull(userDTO)) {
            throw new ErrorMessageException("登录名称重复");
        }

        UserDO userDO = BeanMapper.map(userSaveRequest, UserDO.class);
        // 初始化 创建用户时的必填项 手动创建
        createInitUser(userDO, UserDO.SOURCE_TYPE_MANUAL_CONTROL);

        insert(userDO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateUser(UserUpdateRequest userUpdateRequest) {
        UserDO userDO = selectByPrimaryKey(userUpdateRequest.getId());

        userDO.setName(userUpdateRequest.getName());
        userDO.setEmail(userUpdateRequest.getEmail());
        userDO.setDepartmentId(userUpdateRequest.getDepartmentId());
        userDO.setMobile(userUpdateRequest.getMobile());
        userDO.setLoginName(userUpdateRequest.getLoginName());
        userDO.setPositionName(userUpdateRequest.getPositionName());
        userDO.setUpdateTime(new Date());

        updateByPrimaryKey(userDO);
    }

    @Override
    public UserDTO selectByLoginName(String loginName) {
        UserDO userDO = userMapper.selectByLoginName(loginName);
        if (Objects.isNull(userDO)) {
            return null;
        }
        return BeanMapper.map(userDO, UserDTO.class);
    }

    @Override
    public Integer countByDepartmentId(Long id) {
        return userMapper.countByDepartmentId(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveImportUserList(List<ImportUserDataDTO> importUserList) {
        if (CollUtil.isNotEmpty(importUserList)) {
            List<UserDO> userList = new ArrayList<>(importUserList.size());
            for (ImportUserDataDTO importUserDataDTO : importUserList) {
                UserDO initUser = createInitUser(null, UserDO.SOURCE_TYPE_BATCH_IMPORT);
                initUser.setName(importUserDataDTO.getUserName());
                initUser.setDepartmentId(importUserDataDTO.getId());
                initUser.setEmail(importUserDataDTO.getUserEmail());
                initUser.setMobile(importUserDataDTO.getUserMobile());
                initUser.setLoginName(importUserDataDTO.getLoginName());
                if (StrUtil.isNotEmpty(importUserDataDTO.getPositionName())) {
                    initUser.setPositionName(importUserDataDTO.getPositionName());
                }

                userList.add(initUser);

                // 满足批次就提交
                if (userList.size() >= BATCH_COUNT) {
                    this.batchInsert(userList);
                    userList.clear();
                }
            }

            // 提交最后一批
            if (CollUtil.isNotEmpty(userList)) {
                this.batchInsert(userList);
                userList.clear();
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updatePassword(Long id, String decrypt) {
        UserDO userDO = this.selectByPrimaryKey(id);
        // 使用明文md5后的hash串作为密码
        String digestHex = MD5.create().digestHex(decrypt);
        userDO.setPassword(digestHex);
        userDO.setUpdateTime(new Date());
        this.updateByPrimaryKey(userDO);
    }

    /**
     * 初始化 创建用户时的必填项
     *
     * @param userDO
     * @param source
     */
    private UserDO createInitUser(UserDO userDO, int source) {
        if (Objects.isNull(userDO)) {
            userDO = new UserDO();
        }

        // 创建初始化用户  初始化必填项
        userDO.setCreateTime(new Date());
        userDO.setUpdateTime(new Date());
        userDO.setModifyPasswordWrongCount(UserDO.MODIFY_PASSWORD_WRONG_COUNT_DEFAULT);
        userDO.setSource(source);
        userDO.setValidStatus(UserDO.VALID_STATUS_VALID);
        userDO.setActivatedStatus(UserDO.ACTIVATED_STATUS_INACTIVATED);
        userDO.setDisabledStatus(UserDO.DISABLED_STATUS_NOT_DISABLED);
        userDO.setFailedLoginTimes(UserDO.FAILED_LOGIN_TIMES_DEFAULT);
        userDO.setDeviceNum(UserDO.USER_DEVICE_NUM_3);
        userDO.setDeviceSwitchStatus(UserDO.DEVICE_SWITCH_STATUS_ON);
        userDO.setTempDeviceNum(UserDO.TEMP_DEVICE_NUM_DEFAULT);
        // 初始化密码 使用md5("123456") hash串
        // 加入是前端修改密码时 逻辑为, 前端使用RSA(公钥加密)加密算法加密后, 后端获取到, 使用RSA(私钥解密)后,
        // 再使用md5("明文密码")后的hash串,作为入库密码
        userDO.setPassword(MD5.create().digestHex(CommonConstants.SUER_INIT_PASSWORD));

        return userDO;
    }
}
