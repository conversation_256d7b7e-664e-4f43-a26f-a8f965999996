package com.pttl.mobile.manager.service;

import com.github.pagehelper.PageInfo;
import com.pttl.mobile.manager.domain.dto.ManagerOperationLogDTO;
import com.pttl.mobile.manager.domain.dto.ManagerOperationLogDetailsDTO;
import com.pttl.mobile.manager.domain.dto.ManagerOperationLogPageResultDTO;
import com.pttl.mobile.manager.domain.entity.ManagerOperationLogDO;
import com.pttl.mobile.manager.domain.request.ManagerOperationLogPageRequest;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ManagerOperationLogService {


    int deleteByPrimaryKey(Long id);

    int insert(ManagerOperationLogDO record);

    int insertSelective(ManagerOperationLogDO record);

    ManagerOperationLogDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ManagerOperationLogDO record);

    int updateByPrimaryKey(ManagerOperationLogDO record);

    List<ManagerOperationLogDO> selectAllOrderByIdDesc();

    /**
     * 保存管理端操作日志
     *
     * @param managerOperationLogDTO 保存管理端操作日志
     */
    void saveManagerOperationLog(ManagerOperationLogDTO managerOperationLogDTO);

    /**
     * 分页获取管理端操作日志
     *
     * @param pageRequest 参数
     * @return 结果
     */
    PageInfo<ManagerOperationLogPageResultDTO> pageInfo(ManagerOperationLogPageRequest pageRequest);

    /**
     * 根据id获取日志详情
     *
     * @param id id
     * @return 详情
     */
    ManagerOperationLogDetailsDTO getDetailsById(Long id);
}
