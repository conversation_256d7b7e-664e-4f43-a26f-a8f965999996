package com.pttl.mobile.manager.service.impl;

import com.pttl.mobile.manager.dao.StrategyUser2Mapper;
import com.pttl.mobile.manager.domain.dto.UserStrategyInfoDTO;
import com.pttl.mobile.manager.domain.entity.StrategyUser2DO;
import com.pttl.mobile.manager.domain.entity.UserDO;
import com.pttl.mobile.manager.service.StrategyUser2Service;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class StrategyUser2ServiceImpl implements StrategyUser2Service {

    @Resource
    private StrategyUser2Mapper strategyUser2Mapper;

    @Override
    public int deleteByPrimaryKey(Integer id) {
        return strategyUser2Mapper.deleteByPrimaryKey(id);
    }

    @Override
    public int insert(StrategyUser2DO record) {
        return strategyUser2Mapper.insert(record);
    }

    @Override
    public int insertSelective(StrategyUser2DO record) {
        return strategyUser2Mapper.insertSelective(record);
    }

    @Override
    public StrategyUser2DO selectByPrimaryKey(Integer id) {
        return strategyUser2Mapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(StrategyUser2DO record) {
        return strategyUser2Mapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(StrategyUser2DO record) {
        return strategyUser2Mapper.updateByPrimaryKey(record);
    }

    @Override
    public int updateBatch(List<StrategyUser2DO> list) {
        return strategyUser2Mapper.updateBatch(list);
    }

    @Override
    public int updateBatchSelective(List<StrategyUser2DO> list) {
        return strategyUser2Mapper.updateBatchSelective(list);
    }

    @Override
    public int batchInsert(List<StrategyUser2DO> list) {
        return strategyUser2Mapper.batchInsert(list);
    }

    @Override
    public Integer countByStrategyId(Long id) {
        return strategyUser2Mapper.countByStrategyId(id);
    }

    @Override
    public List<UserDO> listByStrategyId(Long id) {
        return strategyUser2Mapper.listByStrategyId(id);
    }

    @Override
    public void deleteByStrategyId(Long id) {
        strategyUser2Mapper.deleteByStrategyId(id);
    }

    @Override
    public List<UserStrategyInfoDTO> userStrategyByUserId(Long userId) {
        return strategyUser2Mapper.userStrategyByUserId(userId);
    }

}
