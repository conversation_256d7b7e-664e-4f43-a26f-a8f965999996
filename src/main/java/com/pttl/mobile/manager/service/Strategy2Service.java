package com.pttl.mobile.manager.service;

import com.github.pagehelper.PageInfo;
import com.pttl.mobile.manager.domain.dto.*;
import com.pttl.mobile.manager.domain.entity.Strategy2DO;
import com.pttl.mobile.manager.domain.request.*;

import java.util.List;

public interface Strategy2Service {


    int deleteByPrimaryKey(Long id);

    int insert(Strategy2DO strategy2DO);

    int insertSelective(Strategy2DO strategy2DO);

    Strategy2DO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(Strategy2DO strategy2DO);

    int updateByPrimaryKey(Strategy2DO strategy2DO);

    int updateBatch(List<Strategy2DO> list);

    int updateBatchSelective(List<Strategy2DO> list);

    int batchInsert(List<Strategy2DO> list);

    /**
     * 分页获取策略信息
     *
     * @param pageRequest
     * @return
     */
    PageInfo<StrategyPageDTO> pageInfo(StrategyPageRequest pageRequest);

    /**
     * 策略详情
     *
     * @param id
     * @return
     */
    StrategyDetailDTO getStrategyDetails(Long id);

    /**
     * 只做状态修改  不做物理删除
     *
     * @param ids
     */
    void removeByIds(List<Long> ids);

    /**
     * 克隆策略
     *
     * @param id
     */
    void cloneById(Long id);

    /**
     * 更新策略信息
     *
     * @param dataRequest
     */
    void updateStrategy(StrategyUpdateRequest dataRequest);

    /**
     * 根据策略id获取关联应用
     *
     * @param id
     * @return
     */
    List<ApplicationDTO> getApplicationByStrategyId(Long id);

    /**
     * 根据策略id获取关联用户和组织部门
     *
     * @param id
     * @return
     */
    List<PolicyUserAndDepartmentConfigurationDTO> getUserByStrategyId(Long id);

    /**
     * 创建策略及配置
     *
     * @param dataRequest
     */
    void createStrategy(StrategyCreateRequest dataRequest);

    /**
     * 更新策略application配置
     *
     * @param dataRequest
     */
    void updateStrategyAppConfiguration(StrategyUpdateAppConfigurationRequest dataRequest);

    /**
     * 更新策略用户及部门配置
     *
     * @param dataRequest
     */
    void updateStrategyUserConfiguration(StrategyUpdateUserConfigurationRequest dataRequest);

    /**
     * 根据用户id 获取已关联策略
     *
     * @param userId 用户id
     * @return 用户关联策略
     */
    List<UserStrategyInfoDTO> userStrategyByUserId(Long userId);
}
