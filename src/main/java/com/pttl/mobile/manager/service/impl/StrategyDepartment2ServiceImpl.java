package com.pttl.mobile.manager.service.impl;

import com.pttl.mobile.manager.dao.StrategyDepartment2Mapper;
import com.pttl.mobile.manager.domain.entity.Department2DO;
import com.pttl.mobile.manager.domain.entity.StrategyDepartment2DO;
import com.pttl.mobile.manager.service.StrategyDepartment2Service;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class StrategyDepartment2ServiceImpl implements StrategyDepartment2Service {

    @Resource
    private StrategyDepartment2Mapper strategyDepartment2Mapper;

    @Override
    public int deleteByPrimaryKey(Integer id) {
        return strategyDepartment2Mapper.deleteByPrimaryKey(id);
    }

    @Override
    public int insert(StrategyDepartment2DO record) {
        return strategyDepartment2Mapper.insert(record);
    }

    @Override
    public int insertSelective(StrategyDepartment2DO record) {
        return strategyDepartment2Mapper.insertSelective(record);
    }

    @Override
    public StrategyDepartment2DO selectByPrimaryKey(Integer id) {
        return strategyDepartment2Mapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(StrategyDepartment2DO record) {
        return strategyDepartment2Mapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(StrategyDepartment2DO record) {
        return strategyDepartment2Mapper.updateByPrimaryKey(record);
    }

    @Override
    public int updateBatch(List<StrategyDepartment2DO> list) {
        return strategyDepartment2Mapper.updateBatch(list);
    }

    @Override
    public int updateBatchSelective(List<StrategyDepartment2DO> list) {
        return strategyDepartment2Mapper.updateBatchSelective(list);
    }

    @Override
    public int batchInsert(List<StrategyDepartment2DO> list) {
        return strategyDepartment2Mapper.batchInsert(list);
    }

    @Override
    public List<Department2DO> listByStrategyId(Long strategyId) {
        return strategyDepartment2Mapper.listByStrategyId(strategyId);
    }

    @Override
    public void deleteByStrategyId(Long strategyId) {
        strategyDepartment2Mapper.deleteByStrategyId(strategyId);
    }

}
