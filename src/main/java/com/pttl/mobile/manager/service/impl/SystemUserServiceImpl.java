package com.pttl.mobile.manager.service.impl;

import com.pttl.mobile.manager.dao.SystemUserMapper;
import com.pttl.mobile.manager.domain.entity.SystemUserDO;
import com.pttl.mobile.manager.service.SystemUserService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class SystemUserServiceImpl implements SystemUserService {

    @Resource
    private SystemUserMapper systemUserMapper;

    @Override
    public int deleteByPrimaryKey(Long id) {
        return systemUserMapper.deleteByPrimaryKey(id);
    }

    @Override
    public int insert(SystemUserDO record) {
        return systemUserMapper.insert(record);
    }

    @Override
    public int insertSelective(SystemUserDO record) {
        return systemUserMapper.insertSelective(record);
    }

    @Override
    public SystemUserDO selectByPrimaryKey(Long id) {
        return systemUserMapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(SystemUserDO record) {
        return systemUserMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateByPrimaryKey(SystemUserDO record) {
        return systemUserMapper.updateByPrimaryKey(record);
    }

    @Override
    public int updateBatch(List<SystemUserDO> list) {
        return systemUserMapper.updateBatch(list);
    }

    @Override
    public int batchInsert(List<SystemUserDO> list) {
        return systemUserMapper.batchInsert(list);
    }

    @Override
    public SystemUserDO getByLoginName(String loginName) {
        return systemUserMapper.getByLoginName(loginName);
    }

}
