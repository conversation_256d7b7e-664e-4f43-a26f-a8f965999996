package com.pttl.mobile.manager.service;

import com.github.pagehelper.PageInfo;
import com.pttl.mobile.manager.domain.dto.DepartmentQueryDTO;
import com.pttl.mobile.manager.domain.dto.DepartmentTreeDTO;
import com.pttl.mobile.manager.domain.dto.PolicyDepartmentConfigurationDTO;
import com.pttl.mobile.manager.domain.dto.SameLevelDepartmentDTO;
import com.pttl.mobile.manager.domain.entity.Department2DO;
import com.pttl.mobile.manager.domain.request.DepartmentPageRequest;
import com.pttl.mobile.manager.domain.request.DepartmentSaveRequest;
import com.pttl.mobile.manager.domain.request.DepartmentUpdateRequest;
import com.pttl.mobile.manager.excel.dto.ExportDepartmentDataDTO;

import java.util.List;

public interface Department2Service {


    int deleteByPrimaryKey(Long id);

    int insert(Department2DO record);

    int insertSelective(Department2DO record);

    Department2DO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(Department2DO record);

    int updateByPrimaryKey(Department2DO record);

    int updateBatch(List<Department2DO> list);

    int updateBatchSelective(List<Department2DO> list);

    int batchInsert(List<Department2DO> list);

    /**
     * 获取部门树形数据
     *
     * @return
     */
    List<DepartmentTreeDTO> listTree();

    /**
     * 保存创建的部门
     *
     * @param request
     */
    void saveDepartment(DepartmentSaveRequest request);

    /**
     * 修改部门信息 名称和描述
     *
     * @param request
     */
    void updateDepartment(DepartmentUpdateRequest request);

    /**
     * 根据ID批量删除
     *
     * @param ids
     */
    void batchRemoveDepartment(List<Long> ids);

    /**
     * 根据部门id获取部门名称全路径
     *
     * @param departmentId 当前部门di
     * @return
     */
    String getFullDepartmentName(Long departmentId);

    /**
     * 根据父级部门获取子级部门列表
     *
     * @param request 参数
     * @return
     */
    PageInfo<PolicyDepartmentConfigurationDTO> pageListByParentDepartmentId(DepartmentPageRequest request);

    /**
     * 获取全部部门数据
     *
     * @return
     */
    List<Department2DO> listAll();


    /**
     * 获取导出 全部部门数据
     *
     * @return
     */
    List<ExportDepartmentDataDTO> exportDepartmentListAll();

    /**
     * 根据id获取部门
     *
     * @param departmentId
     * @return
     */
    DepartmentQueryDTO getByDepartmentId(Long departmentId);

    /**
     * 获取所有同级部门列表
     *
     * @param departmentId id
     * @return
     */
    List<SameLevelDepartmentDTO> listSameLevelDepartment(Long departmentId);
}
