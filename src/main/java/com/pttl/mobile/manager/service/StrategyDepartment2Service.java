package com.pttl.mobile.manager.service;

import com.pttl.mobile.manager.domain.entity.Department2DO;
import com.pttl.mobile.manager.domain.entity.StrategyDepartment2DO;

import java.util.List;

public interface StrategyDepartment2Service {


    int deleteByPrimaryKey(Integer id);

    int insert(StrategyDepartment2DO record);

    int insertSelective(StrategyDepartment2DO record);

    StrategyDepartment2DO selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(StrategyDepartment2DO record);

    int updateByPrimaryKey(StrategyDepartment2DO record);

    int updateBatch(List<StrategyDepartment2DO> list);

    int updateBatchSelective(List<StrategyDepartment2DO> list);

    int batchInsert(List<StrategyDepartment2DO> list);

    /**
     * 根据策略id获取关联部门信息列表
     *
     * @param strategyId
     * @return
     */
    List<Department2DO> listByStrategyId(Long strategyId);

    /**
     * 根据策略id删除
     *
     * @param id
     */
    void deleteByStrategyId(Long id);
}
