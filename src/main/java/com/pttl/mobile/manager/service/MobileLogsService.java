package com.pttl.mobile.manager.service;

import com.github.pagehelper.PageInfo;
import com.pttl.mobile.manager.domain.dto.FrontLogsQueryDTO;
import com.pttl.mobile.manager.domain.dto.FrontLogsResultDTO;
import com.pttl.mobile.manager.domain.entity.MobileLogs;

import java.util.List;
public interface MobileLogsService{

    int deleteByPrimaryKey(Long id);

    Integer insert(MobileLogs record);

    int insertSelective(MobileLogs record);

    MobileLogs selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(MobileLogs record);

    int updateByPrimaryKey(MobileLogs record);

    int updateBatch(List<MobileLogs> list);

    int updateBatchSelective(List<MobileLogs> list);

    int batchInsert(List<MobileLogs> list);

    PageInfo<FrontLogsResultDTO> pageInfo(FrontLogsQueryDTO frontLogsQuery);

    void saveInBulk(List<MobileLogs> mobileLogs);
}
