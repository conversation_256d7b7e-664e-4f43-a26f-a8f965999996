package com.pttl.mobile.manager.service;

import com.github.pagehelper.PageInfo;
import com.pttl.mobile.manager.domain.dto.FrontLogsQueryDTO;
import com.pttl.mobile.manager.domain.dto.FrontLogsResultDTO;
import com.pttl.mobile.manager.domain.dto.LogDataDTO;
import com.pttl.mobile.manager.domain.entity.FrontLogs;

import java.util.List;
public interface FrontLogsService{

    int deleteByPrimaryKey(Long id);

    int insert(FrontLogs record);

    int insertSelective(FrontLogs record);

    FrontLogs selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(FrontLogs record);

    int updateByPrimaryKey(FrontLogs record);

    int updateBatch(List<FrontLogs> list);

    int updateBatchSelective(List<FrontLogs> list);

    int batchInsert(List<FrontLogs> list);

    void savefrontLog(List<LogDataDTO> mobileLogs);

    PageInfo<FrontLogsResultDTO> pageInfo(FrontLogsQueryDTO frontLogsQuery);

    PageInfo<FrontLogs> exportPageInfoFrontLogs(FrontLogsQueryDTO frontLogsQuery);
}
