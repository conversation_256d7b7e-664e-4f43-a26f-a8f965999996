package com.pttl.mobile.manager.service.impl;

import com.pttl.mobile.manager.dao.RuntimeMapper;
import com.pttl.mobile.manager.lib.ResponseMessage;
import com.pttl.mobile.manager.domain.po.Runtime;
import com.pttl.mobile.manager.service.RuntimeService;
import com.pttl.mobile.manager.util.CollectionUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.UUID;

@Service
@Slf4j
public class RuntimeServiceImpl implements RuntimeService {
    @Autowired
    private RuntimeMapper runtimeMapper;

    @Override
    public ResponseMessage<Boolean> createRuntime(Runtime runtime) {
        // 检查要添加的Version数据是否存在
        int versionExistRows = runtimeMapper.isVersionExists(runtime.getVersion());

        // 如果存在的话 抛出异常 否则 添加到数据库
        if (versionExistRows > 0) {
            return ResponseMessage.error("版本已存在");
        }

        runtime.setId(UUID.randomUUID().toString());
        runtime.setCreateDate(new Date());
        runtimeMapper.insert(runtime);

        return ResponseMessage.ok(true);
    }

    @Override
    public ResponseMessage<List<Runtime>> getRuntime() {
        return ResponseMessage.ok(runtimeMapper.getRuntime());
    }

    @Override
    public ResponseMessage<Boolean> deleteRuntimeByIds(List<String> ids) {
        if (!CollectionUtil.isEmpty(ids)) {
            for (String id : ids) {
                runtimeMapper.deleteByPrimaryKey(id);
            }

            return ResponseMessage.ok(true);
        }

        return ResponseMessage.error("参数不能为空");
    }
}
