package com.pttl.mobile.manager.service;

import com.pttl.mobile.manager.domain.entity.SystemUserDO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface SystemUserService {


    int deleteByPrimaryKey(Long id);

    int insert(SystemUserDO record);

    int insertSelective(SystemUserDO record);

    SystemUserDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(SystemUserDO record);

    int updateByPrimaryKey(SystemUserDO record);

    int updateBatch(List<SystemUserDO> list);

    int batchInsert(List<SystemUserDO> list);

    /**
     * 通过用户登陆名称获取
     *
     * @param loginName 登陆名称
     * @return 结果
     */
    SystemUserDO getByLoginName(String loginName);
}
