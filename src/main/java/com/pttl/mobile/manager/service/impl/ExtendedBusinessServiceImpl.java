package com.pttl.mobile.manager.service.impl;

import com.pttl.mobile.manager.constant.ExtendedBusinessEnum;
import com.pttl.mobile.manager.dao.ExtendedBusinessMapper;
import com.pttl.mobile.manager.domain.dto.VrSwitchDTO;
import com.pttl.mobile.manager.domain.entity.ExtendedBusiness;
import com.pttl.mobile.manager.service.ExtendedBusinessService;
import com.pttl.mobile.manager.util.BeanMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;

@Service
public class ExtendedBusinessServiceImpl implements ExtendedBusinessService{

    @Autowired
    private ExtendedBusinessMapper extendedBusinessMapper;

    @Override
    public int deleteByPrimaryKey(Long id) {
        return extendedBusinessMapper.deleteByPrimaryKey(id);
    }

    @Override
    public int insert(ExtendedBusiness record) {
        return extendedBusinessMapper.insert(record);
    }

    @Override
    public int insertSelective(ExtendedBusiness record) {
        return extendedBusinessMapper.insertSelective(record);
    }

    @Override
    public ExtendedBusiness selectByPrimaryKey(Long id) {
        return extendedBusinessMapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(ExtendedBusiness record) {
        return extendedBusinessMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(ExtendedBusiness record) {
        return extendedBusinessMapper.updateByPrimaryKey(record);
    }

    @Override
    public int updateBatch(List<ExtendedBusiness> list) {
        return extendedBusinessMapper.updateBatch(list);
    }

    @Override
    public int updateBatchSelective(List<ExtendedBusiness> list) {
        return extendedBusinessMapper.updateBatchSelective(list);
    }

    @Override
    public int batchInsert(List<ExtendedBusiness> list) {
        return extendedBusinessMapper.batchInsert(list);
    }

    @Override
    public void updateVrSwitch() {
        String vrSwitchKey = ExtendedBusinessEnum.IOS_VR_SWITCH_OFF.getKey();
        ExtendedBusiness record = extendedBusinessMapper.getByKey(vrSwitchKey);
        if (Objects.isNull(record)) {
            record = new ExtendedBusiness();
            record.setDataKey(vrSwitchKey);
            record.setRemark(ExtendedBusinessEnum.IOS_VR_SWITCH_ON.getDesc());
            record.setDataStr(ExtendedBusinessEnum.IOS_VR_SWITCH_ON.getData());
            record.setCreateTime(new Date());
            extendedBusinessMapper.insertSelective(record);
        }

        // 模拟开关
        record.setUpdateTime(new Date());
        if (Objects.equals(record.getDataStr(), ExtendedBusinessEnum.IOS_VR_SWITCH_OFF.getData())) {
            record.setDataStr(ExtendedBusinessEnum.IOS_VR_SWITCH_ON.getData());
            record.setRemark(ExtendedBusinessEnum.IOS_VR_SWITCH_ON.getDesc());
            extendedBusinessMapper.updateByPrimaryKeySelective(record);
        } else {
            record.setDataStr(ExtendedBusinessEnum.IOS_VR_SWITCH_OFF.getData());
            record.setRemark(ExtendedBusinessEnum.IOS_VR_SWITCH_OFF.getDesc());
            extendedBusinessMapper.updateByPrimaryKeySelective(record);
        }
    }

    @Override
    public String getByKey(ExtendedBusinessEnum extendedBusinessEnum) {
        ExtendedBusiness byKey = extendedBusinessMapper.getByKey(extendedBusinessEnum.getKey());
        if (Objects.isNull(byKey)) {
            return null;
        }

        return byKey.getDataStr();
    }

    @Override
    public VrSwitchDTO getSwitchStatusByKey(ExtendedBusinessEnum extendedBusinessEnum) {
        ExtendedBusiness byKey = extendedBusinessMapper.getByKey(extendedBusinessEnum.getKey());
        if (Objects.isNull(byKey)) {
            return null;
        }

        return BeanMapper.map(byKey, VrSwitchDTO.class);
    }

}
