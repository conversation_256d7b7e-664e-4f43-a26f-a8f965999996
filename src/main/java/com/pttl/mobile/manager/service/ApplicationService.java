package com.pttl.mobile.manager.service;

import com.pttl.mobile.manager.domain.dto.ApplicationInfoForUserDTO;
import com.pttl.mobile.manager.lib.ResponseMessage;
import com.pttl.mobile.manager.domain.request.ApplicationCreateRequest;
import com.pttl.mobile.manager.domain.dto.ApplicationExtendDTO;
import com.pttl.mobile.manager.mobile.dto.ApplicationInfoDTO;

import java.util.List;

public interface ApplicationService {
    ResponseMessage<Boolean> createApplication(ApplicationCreateRequest ac);

    ResponseMessage<List<ApplicationExtendDTO>> getApplication();

    ResponseMessage<ApplicationExtendDTO> getApplicationById(String id);

    ResponseMessage<Boolean> deleteApplicationByIds(List<String> ids);

    ResponseMessage<Boolean> deleteByGroupIds(List<String> groupIds);

    ResponseMessage<Boolean> updateApplication(ApplicationCreateRequest ac);


    /**
     * 获取全部应用信息 条件可选
     *
     * @param name
     * @param applicationGroupId
     * @return
     */
    List<ApplicationExtendDTO> listAllApplication(String name, String applicationGroupId);

    /**
     * 移动获取应用列表
     *
     * @return 列表
     */
    List<ApplicationInfoDTO> infoListForMobile();

    /**
     * 获取用户已关联所有应用信息
     *
     * @param userId 用户id
     * @return 用户已关联所有应用信息
     */
    List<ApplicationInfoForUserDTO> userApplicationByUserId(Long userId);

    /**
     * 更新应用权重
     *
     * @param id                 应用id
     * @param applicationWeight  应用权重
     */
    void updateApplicationWeightById(String id, Integer applicationWeight);
}
