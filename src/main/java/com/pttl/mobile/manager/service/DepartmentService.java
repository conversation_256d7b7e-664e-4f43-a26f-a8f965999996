package com.pttl.mobile.manager.service;

import com.pttl.mobile.manager.domain.po.Department;
import com.pttl.mobile.manager.domain.request.DepartmentSaveRequest;
import com.pttl.mobile.manager.lib.ResponseMessage;

import java.util.List;

public interface DepartmentService {
    ResponseMessage<List<Department>> getDepartment();

    ResponseMessage<Boolean> createDepartment(DepartmentSaveRequest request);

    ResponseMessage<Boolean> updateDepartment(DepartmentSaveRequest request);

    ResponseMessage<Boolean> deleteDepartmentByIds(List<String> ids);
}