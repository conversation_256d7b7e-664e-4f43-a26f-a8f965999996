package com.pttl.mobile.manager.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.pttl.mobile.manager.config.ApplicationDefinedConfiguration;
import com.pttl.mobile.manager.constant.ApplicationTypeEnum;
import com.pttl.mobile.manager.constant.CommonConstants;
import com.pttl.mobile.manager.dao.ApplicationAdapterMapper;
import com.pttl.mobile.manager.dao.ApplicationMapper;
import com.pttl.mobile.manager.domain.dto.ApplicationExtendDTO;
import com.pttl.mobile.manager.domain.dto.ApplicationInfoForUserDTO;
import com.pttl.mobile.manager.domain.dto.ManifestDTO;
import com.pttl.mobile.manager.domain.po.Application;
import com.pttl.mobile.manager.domain.po.ApplicationAdapter;
import com.pttl.mobile.manager.domain.request.ApplicationCreateRequest;
import com.pttl.mobile.manager.lib.ContextUtil;
import com.pttl.mobile.manager.lib.ResponseMessage;
import com.pttl.mobile.manager.mobile.dto.ApplicationInfoDTO;
import com.pttl.mobile.manager.mobile.util.RequestContextUtils;
import com.pttl.mobile.manager.service.ApplicationService;
import com.pttl.mobile.manager.util.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import javax.annotation.Resource;
import java.io.*;
import java.net.URL;
import java.util.Date;
import java.util.List;
import java.util.UUID;

@Slf4j
@Service
public class ApplicationServiceImpl implements ApplicationService {

    /**
     * 系统参数配置
     */
    @Resource
    private ApplicationDefinedConfiguration applicationDefinedConfiguration;

    @Autowired
    private ApplicationMapper applicationMapper;

    @Autowired
    private ApplicationAdapterMapper applicationAdapterMapper;

    @Value("${file.path.shareRootPath}")

    /**
     * 文件地址 配置的地址为Web端的URL
     */
    private String fileServer;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResponseMessage<Boolean> createApplication(ApplicationCreateRequest ac) {
        if (!StringUtil.isEmpty(ac.getPackageUrl()) && !analyticAdapter(ac)) {
            return ResponseMessage.error(CommonConstants.ERROR_MESSAGE_PARSING_FAILED);
        }

        // 如果存在的话 抛出异常 否则 添加到数据库
        if (applicationMapper.isApplicationExists(ac.getName()) > 0) {
            return ResponseMessage.error("应用已存在");
        }

        String id = UUID.randomUUID().toString();
        Application application = new Application();
        BeanUtil.copy(ac, application);
        application.setId(id);
        application.setCreateDate(new Date());
        application.setVersion(UUID.randomUUID().toString());
        application.setLastUpdate(new Date());
        ac.setId(id);

        int insertRows = applicationMapper.insert(application);

        if (insertRows <= 0) {
            return ResponseMessage.error("创建应用失败");
        }

        // 如果当前插入的类型是适配包的话 需要将数据插入到适配包表
        if (ac.getType() == ApplicationTypeEnum.ADAPTER.getType()) {
            // 检查适配包是否存在 包内容是否重复等等.
            ResponseMessage<ApplicationAdapter> rmaa = verifyApplicationAdapter(ac);
            if (rmaa != null && rmaa.isOk()) {
                // 插入适配表
                int applicationInsertRows = applicationAdapterMapper.insert(rmaa.getBody());
                if (applicationInsertRows <= 0) {
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    return ResponseMessage.error("创建适配失败");
                }
            } else {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return ResponseMessage.error(rmaa != null ? rmaa.getMessage() : "适配包内容错误");
            }
        }

        return ResponseMessage.ok(true);
    }

    @Override
    public ResponseMessage<List<ApplicationExtendDTO>> getApplication() {
        List<ApplicationExtendDTO> dtoList = applicationMapper.getApplication();
        if (CollUtil.isNotEmpty(dtoList)) {
            dtoList.forEach(applicationExtendDTO -> {
                applicationExtendDTO.setVisitLogoUrl(StringUtil.getApplicationIconUr(applicationExtendDTO.getLogoUrl(),
                        applicationDefinedConfiguration.getSystemManager().getApplicationIconUrlPrefix()));
            });
        }

        return ResponseMessage.ok(dtoList);
    }

    @Override
    public ResponseMessage<ApplicationExtendDTO> getApplicationById(String id) {
        if (!StringUtil.isEmpty(id)) {
            ApplicationExtendDTO applicationExtend = applicationMapper.getApplicationById(id);
            applicationExtend.setVisitLogoUrl(StringUtil.getApplicationIconUr(applicationExtend.getLogoUrl(),
                    applicationDefinedConfiguration.getSystemManager().getApplicationIconUrlPrefix()));

            return ResponseMessage.ok(applicationExtend);
        }

        return ResponseMessage.error("参数错误");
    }

    @Override
    @Transactional
    public ResponseMessage<Boolean> deleteApplicationByIds(List<String> ids) {
        if (!CollectionUtil.isEmpty(ids)) {
            for (String id : ids) {
                applicationMapper.deleteByPrimaryKey(id);
                applicationAdapterMapper.deleteByPrimaryKey(id);
            }


            return ResponseMessage.ok(true);
        }

        return ResponseMessage.error("参数不能为空");
    }

    @Override
    public ResponseMessage<Boolean> deleteByGroupIds(List<String> groupIds) {
        if (!CollectionUtil.isEmpty(groupIds)) {
            applicationMapper.deleteByGroupIds(groupIds);

            return ResponseMessage.ok(true);
        }

        return ResponseMessage.error("参数不能为空");
    }

    @Transactional
    @Override
    public ResponseMessage<Boolean> updateApplication(ApplicationCreateRequest ac) {
        // 如果修改的应用名称以及存在的话 抛出异常
        if (applicationMapper.isApplicationExistsForUpdate(ac.getName(), ac.getId())) {
            return ResponseMessage.error("应用已存在");
        }

        Application application = new Application();
        BeanUtil.copy(ac, application);
        application.setLastUpdate(new Date());
        application.setVersion(UUID.randomUUID().toString());
        applicationMapper.updateByPrimaryKeySelective(application);

        // 如果是适配应用 并且Manifest不为空说明重新上传或者由在线应用改为适配应用了 需要修改适配包数据
        if (ac.getType() == ApplicationTypeEnum.ADAPTER.getType()) {
            if (ac.getIsManifestChange() && !StringUtil.isEmpty(ac.getPackageUrl())) {
                if (!analyticAdapter(ac))
                    return ResponseMessage.error("适配包解析失败");

                // 重新检查适配包是否重名等.
                ResponseMessage<ApplicationAdapter> rmaa = verifyApplicationAdapter(ac);
                if (rmaa != null && rmaa.isOk()) {
                    applicationAdapterMapper.deleteByPrimaryKey(rmaa.getBody().getApplicationId());
                    applicationAdapterMapper.insert(rmaa.getBody());
                } else {
                    return ResponseMessage.error(rmaa != null ? rmaa.getMessage() : "适配包内容错误");
                }
            }
        } else {
            // 在线应用需要删除适配包
            applicationAdapterMapper.deleteByPrimaryKey(application.getId());
        }

        return ResponseMessage.ok(true);
    }

    @Override
    public List<ApplicationExtendDTO> listAllApplication(String name, String applicationGroupId) {
        List<ApplicationExtendDTO> dtoList = applicationMapper.listAllApplication(name, applicationGroupId);

        if (CollUtil.isNotEmpty(dtoList)) {
            // 设置图标访问地址
            dtoList.forEach(applicationExtendDTO -> {
                applicationExtendDTO.setVisitLogoUrl(StringUtil.getApplicationIconUr(applicationExtendDTO.getLogoUrl(),
                        applicationDefinedConfiguration.getSystemManager().getApplicationIconUrlPrefix()));
            });
        }

        return dtoList;
    }

    @Override
    public List<ApplicationInfoDTO> infoListForMobile() {
        Long userId = RequestContextUtils.getUserId();
        return applicationMapper.infoListForMobile(userId);
    }

    @Override
    public List<ApplicationInfoForUserDTO> userApplicationByUserId(Long userId) {
        return applicationMapper.userApplicationByUserId(userId);
    }

    @Override
    public void updateApplicationWeightById(String id, Integer applicationWeight) {
        applicationMapper.updateApplicationWeightById(id, applicationWeight);
    }

    // 检查适配包格式以及数据处理
    private ResponseMessage<ApplicationAdapter> verifyApplicationAdapter(ApplicationCreateRequest ac) {
        try {
            int applicationAdapterExistRows = applicationAdapterMapper.isApplicationAdapterExists(
                    ac.getManifest().getScope(), ac.getId());

            if (applicationAdapterExistRows > 0) {
                return ResponseMessage.error("Scope相同 请检查");
            }

            ManifestDTO manifest = ac.getManifest();
            String runtimePrimaryVersion = null;
            boolean isSpecificRuntimeUsed = false;
            String runtimeVersion = manifest.getRuntime_version().trim();
            if (!StringUtil.isEmpty(runtimeVersion)) {
                runtimePrimaryVersion = runtimeVersion.split("\\.")[0] + ".+";
                isSpecificRuntimeUsed = !StringUtil.isNumeric(runtimeVersion.substring(0, 1));
            }

            ApplicationAdapter applicationAdapter = new ApplicationAdapter();
            applicationAdapter.setApplicationId(ac.getId());
            applicationAdapter.setScope(manifest.getScope());
            applicationAdapter.setManifest(JsonUtil.toJson(ac.getManifest()));
            applicationAdapter.setRuntimePrimaryVersion(runtimePrimaryVersion);
            applicationAdapter.setRuntimeVersion(runtimeVersion);
            applicationAdapter.setIsSpecificRuntimeUsed(isSpecificRuntimeUsed);
            applicationAdapter.setLastUpdate(new Date());
            applicationAdapter.setSystemPlatform(ContextUtil.systemPlatform());
            applicationAdapter.setEnv(ContextUtil.currentEnv());

            return ResponseMessage.ok(applicationAdapter);
        } catch (Exception ex) {
            log.error("检验适配包失败,Ex={}", ex);
            return null;
        }
    }

    private boolean analyticAdapter(ApplicationCreateRequest ac) {
        try {
            log.info("适配包解析开始");

            // 临时存储文件位置
            String filePath = ac.getPackageUrl();

            // 远程文件地址
            String remoteFilePath = fileServer + filePath;

            // 当前是否是本地开发环境
            boolean isLocal = !remoteFilePath.startsWith("http");

            if (!isLocal) {
                downloadFile(remoteFilePath, filePath);
            }

            // 读取压缩包的manifest.json文件
            String manifest = ZipUtil.readFile(filePath, "manifest.json");
            if (!StringUtil.isEmpty(manifest)) {
                ac.setManifest(JsonUtil.fromJson(manifest, ManifestDTO.class));
            }

            // 处理之后删除文件
            if (!isLocal) {
                FileUtil.deleteFile(filePath);
            }

            if (ac.getManifest() != null) {
                return true;
            } else {
                log.error("适配包解析失败");
            }
        } catch (Exception ex) {
            log.error("解析适配包失败,Ex={}", ex);
        }

        return false;
    }

    /**
     * 直接获取远程zip包会报错 所以先下载到本地 处理之后删除 之后会使用直接读的方式
     *
     * @param remoteFilePath
     * @param localFilePath
     * @throws Exception
     */
    private void downloadFile(String remoteFilePath, String localFilePath) throws IOException {
        URL url = new URL(remoteFilePath);
        InputStream inputStream = url.openStream();
        File file = new File(localFilePath);
        try (BufferedInputStream bis = new BufferedInputStream(inputStream);
             BufferedOutputStream bos = new BufferedOutputStream(new FileOutputStream(file));) {
            File fileParent = file.getParentFile();
            if (!fileParent.exists()) {
                boolean mkdirs = fileParent.mkdirs();
                if (!mkdirs) {
                    return;
                }
            }

            boolean newFile = file.createNewFile();
            if (!newFile) {
                return;
            }

            int len = 2048;
            byte[] b = new byte[len];
            while ((len = bis.read(b)) != -1) {
                bos.write(b, 0, len);
            }
            bos.flush();
            inputStream.close();
        } catch (Exception exception) {
            exception.printStackTrace();
        }
    }
}
