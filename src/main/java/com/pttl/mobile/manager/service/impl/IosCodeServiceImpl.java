package com.pttl.mobile.manager.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.pttl.mobile.manager.config.IosCodeTypeMapConfiguration;
import com.pttl.mobile.manager.constant.SmsWarnTypeEnum;
import com.pttl.mobile.manager.dao.IosCodeMapper;
import com.pttl.mobile.manager.domain.dto.*;
import com.pttl.mobile.manager.domain.entity.IosCodeDO;
import com.pttl.mobile.manager.domain.entity.SmsLogs;
import com.pttl.mobile.manager.domain.request.IosCodePageRequest;
import com.pttl.mobile.manager.excel.dto.ImportIosCodeDataDTO;
import com.pttl.mobile.manager.service.IosCodeService;
import com.pttl.mobile.manager.service.SmsLogsService;
import com.pttl.mobile.manager.service.SmsPersonnelService;
import com.pttl.mobile.manager.util.BeanMapper;
import com.pttl.mobile.manager.util.HttpClientUtil;
import com.pttl.mobile.manager.util.PageUtil;
import com.pttl.mobile.manager.util.StatisticalTimeAcquisitionUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 */
@Service
public class IosCodeServiceImpl implements IosCodeService {

    @Resource
    private IosCodeMapper iosCodeMapper;

    /**
     * ios code码 分类标识map配置
     */
    @Resource
    private IosCodeTypeMapConfiguration iosCodeTypeMapConfiguration;

    /**
     * 服务对象
     */
    @Autowired
    private SmsPersonnelService smsPersonnelService;

    /**
     * 短信日志服务对象
     */
    @Resource
    private SmsLogsService smsLogsService;

    @Override
    public int deleteByPrimaryKey(Long id) {
        return iosCodeMapper.deleteByPrimaryKey(id);
    }

    @Override
    public int insert(IosCodeDO record) {
        return iosCodeMapper.insert(record);
    }

    @Override
    public int insertSelective(IosCodeDO record) {
        return iosCodeMapper.insertSelective(record);
    }

    @Override
    public IosCodeDO selectByPrimaryKey(Long id) {
        return iosCodeMapper.selectByPrimaryKey(id);
    }

    @Override
    public int updateByPrimaryKeySelective(IosCodeDO record) {
        return iosCodeMapper.updateByPrimaryKeySelective(record);
    }

    @Override
    public int updateByPrimaryKey(IosCodeDO record) {
        return iosCodeMapper.updateByPrimaryKey(record);
    }

    @Override
    public int updateBatch(List<IosCodeDO> list) {
        return iosCodeMapper.updateBatch(list);
    }

    @Override
    public int batchInsert(List<IosCodeDO> list) {
        return iosCodeMapper.batchInsert(list);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveImportList(List<ImportIosCodeDataDTO> importIosCodeList) {
        if (CollUtil.isNotEmpty(importIosCodeList)) {
            List<IosCodeDO> iosCodeList = BeanMapper.mapList(importIosCodeList, IosCodeDO.class);
            // 默认值 有效及导入时间
            iosCodeList.forEach(iosCodeDO -> {
                iosCodeDO.setValidStatus(IosCodeDO.VALID_STATUS_VALID);
                iosCodeDO.setImportTime(new Date());
            });
            batchInsert(iosCodeList);
        }
    }

    @Override
    public PageInfo<IosCodePageDTO> pageInfo(IosCodePageRequest pageRequest) {
        PageMethod.startPage(pageRequest.getPageNum(), pageRequest.getPageSize());

        List<IosCodeDO> dataList = iosCodeMapper.listByPageParameter(pageRequest);

        return PageUtil.page2PageVo(new PageInfo<>(dataList), IosCodePageDTO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void iosCodeRemove(List<Long> ids) {
        ids.forEach(this::deleteByPrimaryKey);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public String getIosCodeQrUrl(Integer appType) {
        // 获取 最新导入有效的 一条数据
        IosCodeDO latestImportCode = iosCodeMapper.getLatestImportCode(appType);
        if (Objects.isNull(latestImportCode)) {
            return null;
        }

        // 跟新状态为无效  只要获取过就置为无效
        latestImportCode.setFailureTime(new Date());
        latestImportCode.setValidStatus(IosCodeDO.VALID_STATUS_DEL);
        iosCodeMapper.updateByPrimaryKey(latestImportCode);

        return latestImportCode.getCodeLink();
    }

    @Override
    public List<UserUsageStatisticsDTO> getCount() {

        List<UserUsageStatisticsDTO> list = iosCodeMapper.getCount();

        if (CollUtil.isEmpty(list)) {
            return CollUtil.newArrayList();
        }

        return list;
    }

    @Override
    public List<UserUsageStatisticsDTO> unusedQuantity() {
        List<UserUsageStatisticsDTO> list = iosCodeMapper.unusedQuantity();

        if (CollUtil.isEmpty(list)) {
            return CollUtil.newArrayList();
        }
        return list;
    }

    @Override
    public ExchangeStatisticsDTO getExchangeStatistics(Integer appType) {
        return iosCodeMapper.getExchangeStatistics(appType);
    }

    @Override
    public List<ExchangeDetailsDTO> getExchangeDetails(Integer dataType, Integer appType) {
        List<ExchangeDetailsDTO> resultList = CollUtil.newArrayList();
        // 今天每小时
        if (Objects.isNull(dataType) || dataType == ExchangeDetailsDTO.DATA_TYPE_DAY_1) {
            String dateStr = DateUtil.beginOfDay(new Date()).toString();
            // 查询库
            List<ExchangeDetailsDTO> hourList = iosCodeMapper.hourList(dateStr, appType);
            resultList = processDataListComplement(hourList, StatisticalTimeAcquisitionUtil.hourList());
        }

        // 近七天的
        if (dataType == ExchangeDetailsDTO.DATA_TYPE_DAY_7) {
            List<String> days7List = StatisticalTimeAcquisitionUtil.days7List();
            // 查询库
            List<ExchangeDetailsDTO> dataList = iosCodeMapper.daysList(days7List.get(0), appType);
            resultList = processDataListComplement(dataList, days7List);
        }

        // 近30天的
        if (dataType == ExchangeDetailsDTO.DATA_TYPE_DAY_30) {
            List<String> days30List = StatisticalTimeAcquisitionUtil.days30List();
            // 查询库
            List<ExchangeDetailsDTO> dataList = iosCodeMapper.daysList(days30List.get(0), appType);
            resultList = processDataListComplement(dataList, days30List);
        }

        // 近一年
        if (dataType == ExchangeDetailsDTO.DATA_TYPE_MONTH_12) {
            List<String> month12List = StatisticalTimeAcquisitionUtil.month12List();
            // 查询库
            List<ExchangeDetailsDTO> dataList = iosCodeMapper.mouthsList(month12List.get(0), appType);
            resultList = processDataListComplement(dataList, month12List);
        }

        // 所有
        if (dataType == ExchangeDetailsDTO.DATA_TYPE_ALL) {
            // 查询库
            resultList = iosCodeMapper.allList(appType);
        }
        return resultList;
    }

    @Override
    public void checkIosCodeQuantitySendWarningSMSMessages(String smsGateway) {
        List<StatisticsUnusedIosCodeQuantityDTO> statisticsUnusedLists =  statisticsOfUnusedQuantityOfEachCategory();

        if(CollUtil.isNotEmpty(statisticsUnusedLists)) {
            List<SmsPersonnelResponseDTO> smsPhones = smsPersonnelService.getNeedSendWarningSMSMessagesPhoneBySmsType(SmsWarnTypeEnum.IOS_CODE);
            sendWarningMessage(smsGateway, statisticsUnusedLists, smsPhones);
        }
    }

    /**
     * 发送预警短信
     * @param smsGateway 网关地址
     * @param statisticsUnusedLists
     * @param smsPhones
     */
    private void sendWarningMessage(String smsGateway, List<StatisticsUnusedIosCodeQuantityDTO> statisticsUnusedLists
            , List<SmsPersonnelResponseDTO> smsPhones) {

        if (CollUtil.isNotEmpty(smsPhones)) {
            for (SmsPersonnelResponseDTO smsPhone : smsPhones) {
                boolean flag =  false;
                for (StatisticsUnusedIosCodeQuantityDTO statisticsUnusedList : statisticsUnusedLists) {
                    if (Integer.parseInt(smsPhone.getSmsCondition()) >= statisticsUnusedList.getQuantity()) {
                        flag =  true;
                        break;
                    }
                }

                if (flag) {
                    String modelContent = smsPhone.getModelContent();
                    for (int i = 0; i < statisticsUnusedLists.size(); i++) {
                        modelContent = modelContent.replace("$app" + (i + 1)+ "$"
                                        , getIosCodeTypeNameByAppType(statisticsUnusedLists.get(i).getAppType()))
                                .replace("$threshold" + (i + 1)+ "$", String.valueOf(statisticsUnusedLists.get(i).getQuantity()));
                    }

                    String message = String.format("{\"phoneNumbers\":\"%s\",\"content\":\"%s\"}"
                            , smsPhone.getPhoneNum(), modelContent);
                    String result = HttpClientUtil.doPostJson(smsGateway, message);

                    // 保存记录
                    smsLogsService.insert(SmsLogs.builder().smsType(SmsWarnTypeEnum.IOS_CODE.getType()).name(smsPhone.getName())
                            .modelName(smsPhone.getModelName()).modelContent(modelContent).smsCondition(smsPhone.getSmsCondition())
                            .phoneNum(smsPhone.getPhoneNum()).reservedField1(JSON.toJSONString(JSON.parseObject(result).get("Message")))
                            .reservedField2(result).createTime(new Date()).build());
                }
            }
        }
    }

    /**
     * 根据appType获取iosCodeTypeName
     *
     * @param appType appType
     * @return iosCodeTypeName
     */
    private String getIosCodeTypeNameByAppType(Integer appType) {
        LinkedHashMap<String, Integer> iosCodeType = iosCodeTypeMapConfiguration.getIosCodeType();
        for (Map.Entry<String, Integer> s : iosCodeType.entrySet()) {
            if (s.getValue().equals(appType)) {
                return s.getKey();
            }
        }

        throw new RuntimeException("未找到对应的iosCodeTypeName");
    }

    /**
     * 统计未使用的数量
     *
     * @return 统计结果
     */
    private List<StatisticsUnusedIosCodeQuantityDTO> statisticsOfUnusedQuantityOfEachCategory() {
        List<StatisticsUnusedIosCodeQuantityDTO> dataList = iosCodeMapper.statisticsGroupByAppType();
        if (CollUtil.isEmpty(dataList)) {
            return CollUtil.newArrayList();
        }
        return dataList;
    }

    /**
     * 处理数据补全
     *
     * @param dataList 待处理数据
     * @return 处理完成数据
     */
    private List<ExchangeDetailsDTO> processDataListComplement(List<ExchangeDetailsDTO> dataList, List<String> dateTimes) {
        // 没有数据 补全0
        ArrayList<ExchangeDetailsDTO> objects = CollUtil.newArrayList();
        if (CollUtil.isEmpty(dataList)) {
            dateTimes.forEach(dateTime -> {
                objects.add(new ExchangeDetailsDTO(0, dateTime));
            });
            return objects;
        }

        // 有数据补全为连续时间
        for (String dateTime : dateTimes) {
            boolean flag = false;
            for (ExchangeDetailsDTO exchangeDetailsDTO : dataList) {
                if (dateTime.equals(exchangeDetailsDTO.getFailureTime())) {
                    flag = true;
                    objects.add(new ExchangeDetailsDTO(exchangeDetailsDTO.getTotal(), exchangeDetailsDTO.getFailureTime()));
                }
            }

            // 不存在 则需要不出为0
            if (!flag) {
                objects.add(new ExchangeDetailsDTO(0, dateTime));
            }
        }

        return objects;
    }
}
