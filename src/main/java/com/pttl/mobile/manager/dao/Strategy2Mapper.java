package com.pttl.mobile.manager.dao;

import com.pttl.mobile.manager.domain.entity.Strategy2DO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface Strategy2Mapper {
    /**
     * delete by primary key
     *
     * @param id primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long id);

    /**
     * insert record to table
     *
     * @param strategy2DO the record
     * @return insert count
     */
    int insert(Strategy2DO strategy2DO);

    /**
     * insert record to table selective
     *
     * @param strategy2DO the record
     * @return insert count
     */
    int insertSelective(Strategy2DO strategy2DO);

    /**
     * select by primary key
     *
     * @param id primary key
     * @return object by primary key
     */
    Strategy2DO selectByPrimaryKey(Long id);

    /**
     * update record selective
     *
     * @param strategy2DO the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(Strategy2DO strategy2DO);

    /**
     * update record
     *
     * @param strategy2DO the updated record
     * @return update count
     */
    int updateByPrimaryKey(Strategy2DO strategy2DO);

    int updateBatch(List<Strategy2DO> list);

    int updateBatchSelective(List<Strategy2DO> list);

    int batchInsert(@Param("list") List<Strategy2DO> list);

    /**
     * 获取策略数据列表
     *
     * @param name
     * @return
     */
    List<Strategy2DO> listData(@Param("name") String name);

    /**
     * 根据权重获取 策略列表
     *
     * @param weight
     * @return
     */
    List<Strategy2DO> listAllByWeight(@Param("weight") Integer weight);
}