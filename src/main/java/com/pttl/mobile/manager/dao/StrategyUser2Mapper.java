package com.pttl.mobile.manager.dao;

import com.pttl.mobile.manager.domain.dto.UserStrategyInfoDTO;
import com.pttl.mobile.manager.domain.entity.StrategyUser2DO;
import com.pttl.mobile.manager.domain.entity.UserDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface StrategyUser2Mapper {
    /**
     * delete by primary key
     *
     * @param id primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * insert record to table
     *
     * @param strategyUser2DO the record
     * @return insert count
     */
    int insert(StrategyUser2DO strategyUser2DO);

    /**
     * insert record to table selective
     *
     * @param strategyUser2DO the record
     * @return insert count
     */
    int insertSelective(StrategyUser2DO strategyUser2DO);

    /**
     * select by primary key
     *
     * @param id primary key
     * @return object by primary key
     */
    StrategyUser2DO selectByPrimaryKey(Integer id);

    /**
     * update record selective
     *
     * @param strategyUser2DO the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(StrategyUser2DO strategyUser2DO);

    /**
     * update record
     *
     * @param strategyUser2DO the updated record
     * @return update count
     */
    int updateByPrimaryKey(StrategyUser2DO strategyUser2DO);

    int updateBatch(List<StrategyUser2DO> list);

    int updateBatchSelective(List<StrategyUser2DO> list);

    int batchInsert(@Param("list") List<StrategyUser2DO> list);

    /**
     * 统计策略人数
     *
     * @param strategyId
     * @return
     */
    Integer countByStrategyId(@Param("strategyId") Long strategyId);

    /**
     * 根据策略id 获取关联用户信息
     *
     * @param strategyId
     * @return
     */
    List<UserDO> listByStrategyId(@Param("strategyId") Long strategyId);

    /**
     * 根据策略id删除
     *
     * @param strategyId
     */
    void deleteByStrategyId(@Param("strategyId") Long strategyId);

    /**
     * 根据用户id 获取已关联策略
     *
     * @param userId 用户id
     * @return 用户关联策略
     */
    List<UserStrategyInfoDTO> userStrategyByUserId(@Param("userId") Long userId);
}