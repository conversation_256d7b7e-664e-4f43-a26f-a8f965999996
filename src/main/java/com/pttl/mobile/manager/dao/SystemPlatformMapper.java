package com.pttl.mobile.manager.dao;

import com.pttl.mobile.manager.domain.po.SystemPlatform;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SystemPlatformMapper {
    int deleteByPrimaryKey(Boolean id);

    int insert(SystemPlatform record);

    int insertSelective(SystemPlatform record);

    SystemPlatform selectByPrimaryKey(Boolean id);

    int updateByPrimaryKeySelective(SystemPlatform record);

    int updateByPrimaryKey(SystemPlatform record);

    List<SystemPlatform> getSystemPlatform();
}