package com.pttl.mobile.manager.dao;

import com.pttl.mobile.manager.domain.entity.FunctionalAttributesDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface FunctionalAttributesMapper {
    /**
     * delete by primary key
     *
     * @param id primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long id);

    /**
     * insert record to table
     *
     * @param functionalAttributesDO the record
     * @return insert count
     */
    int insert(FunctionalAttributesDO functionalAttributesDO);

    /**
     * insert record to table selective
     *
     * @param functionalAttributesDO the record
     * @return insert count
     */
    int insertSelective(FunctionalAttributesDO functionalAttributesDO);

    /**
     * select by primary key
     *
     * @param id primary key
     * @return object by primary key
     */
    FunctionalAttributesDO selectByPrimaryKey(Long id);

    /**
     * update record selective
     *
     * @param functionalAttributesDO the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(FunctionalAttributesDO functionalAttributesDO);

    /**
     * update record
     *
     * @param functionalAttributesDO the updated record
     * @return update count
     */
    int updateByPrimaryKey(FunctionalAttributesDO functionalAttributesDO);

    /**
     * 查询所有
     *
     * @return 返回结合
     */
    List<FunctionalAttributesDO> selectAllOrderByIdDesc();

    int updateBatch(List<FunctionalAttributesDO> list);

    int updateBatchSelective(List<FunctionalAttributesDO> list);

    int batchInsert(@Param("list") List<FunctionalAttributesDO> list);

    /**
     * 根据属性名称获取分页
     *
     * @param functionalAttributes 属性名称
     * @param dataType             数据类型
     * @param operatingSystem      操作系统
     * @return 数据集
     */
    List<FunctionalAttributesDO> listByFunctionalAttributes(@Param("functionalAttributes") String functionalAttributes,
                                                            @Param("dataType") Integer dataType,
                                                            @Param("operatingSystem") Integer operatingSystem);

    /**
     * 根据属性名称获取分页
     *
     * @param functionalAttributes 属性名称
     * @param dataType             数据类型
     * @param operatingSystem      操作系统
     * @return 数据集
     */
    List<FunctionalAttributesDO> listByFunctionalAttributesForMobile(@Param("functionalAttributes") String functionalAttributes,
                                                                     @Param("dataType") Integer dataType,
                                                                     @Param("operatingSystem") Integer operatingSystem);

    /**
     * 根据属性id 获取数据
     *
     * @param functionalAttributes 属性id
     * @return 结果
     */
    FunctionalAttributesDO getByFunctionalAttributes(String functionalAttributes);
}