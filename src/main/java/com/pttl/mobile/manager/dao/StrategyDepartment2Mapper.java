package com.pttl.mobile.manager.dao;

import com.pttl.mobile.manager.domain.entity.Department2DO;
import com.pttl.mobile.manager.domain.entity.StrategyDepartment2DO;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface StrategyDepartment2Mapper {
    /**
     * delete by primary key
     *
     * @param id primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(StrategyDepartment2DO record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(StrategyDepartment2DO record);

    /**
     * select by primary key
     *
     * @param id primary key
     * @return object by primary key
     */
    StrategyDepartment2DO selectByPrimaryKey(Integer id);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(StrategyDepartment2DO record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(StrategyDepartment2DO record);

    int updateBatch(List<StrategyDepartment2DO> list);

    int updateBatchSelective(List<StrategyDepartment2DO> list);

    int batchInsert(@Param("list") List<StrategyDepartment2DO> list);

    /**
     * 根据策略id获取关联部门信息列表
     *
     * @param strategyId
     * @return
     */
    List<Department2DO> listByStrategyId(@Param("strategyId") Long strategyId);

    /**
     * 根据策略id删除
     *
     * @param strategyId
     */
    void deleteByStrategyId(@Param("strategyId") Long strategyId);
}