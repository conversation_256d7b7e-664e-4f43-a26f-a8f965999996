package com.pttl.mobile.manager.dao;

import com.pttl.mobile.manager.domain.po.StrategyDepartment;

public interface StrategyDepartmentMapper {
    int deleteByPrimaryKey(String id);

    int insert(StrategyDepartment record);

    int insertSelective(StrategyDepartment record);

    StrategyDepartment selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(StrategyDepartment record);

    int updateByPrimaryKey(StrategyDepartment record);
}