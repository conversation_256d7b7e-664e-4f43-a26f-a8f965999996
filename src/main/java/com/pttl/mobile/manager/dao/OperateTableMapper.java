package com.pttl.mobile.manager.dao;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
@Mapper
public interface OperateTableMapper {

    /**
     * 检查表是否存在
     *
     * @param tableName 表名
     * @return 0不存在
     */
    int existTable(@Param("tableName") String tableName);

    /**
     * 删除表及数据
     *
     * @param tableName 表名
     * @return 0 失败
     */
    int dropTable(@Param("tableName") String tableName);

    /**
     * 创建表移动端操作日志表  规则   mm_mobile_record_log_2022_12   mm_mobile_record_log_{年份_月份}
     *
     * @param tableName 表名
     * @return 0 失败
     */
    int createTableOfMobileRecordLog(@Param("tableName") String tableName);
}