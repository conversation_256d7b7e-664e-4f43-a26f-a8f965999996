package com.pttl.mobile.manager.dao;

import com.pttl.mobile.manager.domain.po.OperatingSystem;
import org.springframework.stereotype.Repository;

@Repository
public interface OperatingSystemMapper {
    int deleteByPrimaryKey(Boolean id);

    int insert(OperatingSystem record);

    int insertSelective(OperatingSystem record);

    OperatingSystem selectByPrimaryKey(Boolean id);

    int updateByPrimaryKeySelective(OperatingSystem record);

    int updateByPrimaryKey(OperatingSystem record);
}