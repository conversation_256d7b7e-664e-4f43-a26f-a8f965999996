package com.pttl.mobile.manager.dao;

import com.pttl.mobile.manager.domain.entity.ExtendedBusiness;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ExtendedBusinessMapper {
    /**
     * delete by primary key
     *
     * @param id primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long id);

    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(ExtendedBusiness record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(ExtendedBusiness record);

    /**
     * select by primary key
     *
     * @param id primary key
     * @return object by primary key
     */
    ExtendedBusiness selectByPrimaryKey(Long id);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(ExtendedBusiness record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(ExtendedBusiness record);

    int updateBatch(List<ExtendedBusiness> list);

    int updateBatchSelective(List<ExtendedBusiness> list);

    int batchInsert(@Param("list") List<ExtendedBusiness> list);

    ExtendedBusiness getByKey(@Param("vrSwitchKey") String vrSwitchKey);
}