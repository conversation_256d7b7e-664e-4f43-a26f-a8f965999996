package com.pttl.mobile.manager.dao;

import com.pttl.mobile.manager.domain.dto.SameLevelDepartmentDTO;
import com.pttl.mobile.manager.domain.entity.Department2DO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface Department2Mapper {
    int deleteByPrimaryKey(Long id);

    int insert(Department2DO record);

    int insertSelective(Department2DO record);

    Department2DO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(Department2DO record);

    int updateByPrimaryKey(Department2DO record);

    int updateBatch(List<Department2DO> list);

    int updateBatchSelective(List<Department2DO>
                                     list);

    int batchInsert(@Param("list") List<Department2DO> list);


    /**
     * 根据部门名称和部门上级id 查询
     *
     * @param name
     * @param parentDepartmentId
     * @return
     */
    Department2DO getOneByNameAndParentDepartmentId(@Param("name") String name,
                                                    @Param("parentDepartmentId") Long parentDepartmentId);

    /**
     * 获取所有部门
     *
     * @return
     */
    List<Department2DO> listAll();

    /**
     * 根据父部门id获取子级部门列表
     *
     * @param parentDepartmentId
     * @return
     */
    List<Department2DO> listByParentDepartmentId(@Param("parentDepartmentId") Long parentDepartmentId);

    int countAll();

    /**
     * 获取所有同级部门列表
     *
     * @param departmentId id
     * @return 列表
     */
    List<SameLevelDepartmentDTO> listSameLevelDepartment(Long departmentId);
}