package com.pttl.mobile.manager.dao;

import com.pttl.mobile.manager.domain.dto.FrontLogsQueryDTO;
import com.pttl.mobile.manager.domain.dto.FrontLogsResultDTO;
import com.pttl.mobile.manager.domain.entity.FrontLogs;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface FrontLogsMapper {
    /**
     * delete by primary key
     * @param id primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long id);

    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(FrontLogs record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(FrontLogs record);

    /**
     * select by primary key
     * @param id primary key
     * @return object by primary key
     */
    FrontLogs selectByPrimaryKey(Long id);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(FrontLogs record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(FrontLogs record);

    int updateBatch(List<FrontLogs> list);

    int updateBatchSelective(List<FrontLogs> list);

    int batchInsert(@Param("list") List<FrontLogs> list);

    List<FrontLogsResultDTO> pageInfo(FrontLogsQueryDTO frontLogsQuery);

    List<FrontLogs> exportPageInfo(FrontLogsQueryDTO frontLogsQuery);
}