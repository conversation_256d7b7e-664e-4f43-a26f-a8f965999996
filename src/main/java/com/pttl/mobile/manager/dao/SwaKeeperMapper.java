package com.pttl.mobile.manager.dao;

import com.pttl.mobile.manager.domain.po.SwaKeeper;
import org.springframework.stereotype.Repository;

@Repository
public interface SwaKeeperMapper {
    int deleteByPrimaryKey(String swaId);

    int insert(Swa<PERSON>eeper record);

    int insertSelective(Swa<PERSON>eeper record);

    SwaKeeper selectByPrimaryKey(String swaId);

    int updateByPrimaryKeySelective(SwaKeeper record);

    int updateByPrimaryKey(SwaKeeper record);
}