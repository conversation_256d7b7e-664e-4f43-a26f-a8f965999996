package com.pttl.mobile.manager.dao;

import com.pttl.mobile.manager.domain.po.Release;
import com.pttl.mobile.manager.domain.dto.ReleaseExtendDTO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface ReleaseMapper {
    int deleteByPrimaryKey(String id);

    int insert(Release record);

    int insertSelective(Release record);

    Release selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(Release record);

    int updateByPrimaryKey(Release record);

    List<ReleaseExtendDTO> getReleaseHistory(@Param("type") Integer type, @Param("version") String version, @Param("keyword") String keyword, @Param("start_date") Date start_date, @Param("end_date") Date end_date);

    int isVersionExists(@Param("type") Integer type, @Param("operating_system") Integer operating_system, @Param("version") String version);

    Release getReleaseLastVersion(@Param("type") Integer type, @Param("operating_system") Integer operating_system);
}