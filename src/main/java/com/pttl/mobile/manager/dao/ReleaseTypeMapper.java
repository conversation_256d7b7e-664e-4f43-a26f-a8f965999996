package com.pttl.mobile.manager.dao;

import com.pttl.mobile.manager.domain.po.ReleaseType;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ReleaseTypeMapper {
    int deleteByPrimaryKey(Boolean id);

    int insert(ReleaseType record);

    int insertSelective(ReleaseType record);

    ReleaseType selectByPrimaryKey(Boolean id);

    int updateByPrimaryKeySelective(ReleaseType record);

    int updateByPrimaryKey(ReleaseType record);

    List<ReleaseType> getType();
}