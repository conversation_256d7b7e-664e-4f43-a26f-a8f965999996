package com.pttl.mobile.manager.dao;

import com.pttl.mobile.manager.domain.entity.ManagerOperationLogDO;
import com.pttl.mobile.manager.domain.request.ManagerOperationLogPageRequest;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface ManagerOperationLogMapper {
    /**
     * delete by primary key
     *
     * @param id primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long id);

    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(ManagerOperationLogDO record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(ManagerOperationLogDO record);

    /**
     * select by primary key
     *
     * @param id primary key
     * @return object by primary key
     */
    ManagerOperationLogDO selectByPrimaryKey(Long id);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(ManagerOperationLogDO record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(ManagerOperationLogDO record);

    /**
     * 查询所有
     *
     * @return 结果
     */
    List<ManagerOperationLogDO> selectAllOrderByIdDesc();

    /**
     * 根据请求参数 获取分页数据
     *
     * @param pageRequest 参数
     * @return 结果
     */
    List<ManagerOperationLogDO> listByPageParameter(ManagerOperationLogPageRequest pageRequest);
}