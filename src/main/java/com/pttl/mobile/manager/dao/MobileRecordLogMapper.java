package com.pttl.mobile.manager.dao;

import com.pttl.mobile.manager.domain.entity.MobileRecordLogDO;
import com.pttl.mobile.manager.domain.request.MobileRecordLogPageRequest;
import com.pttl.mobile.manager.mobile.dto.DateRange;
import com.pttl.mobile.manager.mobile.dto.LoginStatisticsResultDTO;
import com.pttl.mobile.manager.mobile.dto.MobileRecordLogQueryDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface MobileRecordLogMapper {
    /**
     * delete by primary key
     *
     * @param id primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long id);

    /**
     * insert record to table
     *
     * @param mobileRecordLogDO  the record
     * @param tableNameDateMonth the record
     * @return insert count
     */
    int insert(@Param("mobileRecordLogDO") MobileRecordLogDO mobileRecordLogDO, @Param("tableNameDateMonth") String tableNameDateMonth);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(MobileRecordLogDO record);

    /**
     * select by primary key
     *
     * @param id primary key
     * @return object by primary key
     */
    MobileRecordLogDO selectByPrimaryKey(@Param("id") Long id, @Param("yearMonth") String yearMonth);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(MobileRecordLogDO record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(MobileRecordLogDO record);

    /**
     * 查询全部
     *
     * @return 结果集
     */
    List<MobileRecordLogDO> selectAllOrderByIdDesc();

    /**
     * 分页获取
     *
     * @param pageRequest 入参
     * @return 结果集
     */
    List<MobileRecordLogDO> listByPageParameter(MobileRecordLogPageRequest pageRequest);

    /**
     * 根据时间范围和表月份查询
     *
     * @param range 时间范围表月份
     * @return 查询结果
     */
    List<LoginStatisticsResultDTO> listLoginStatisticsByDateRange(DateRange range);

    /**
     * 移动端分页查询
     *
     * @param mouthDate               表月份
     * @param mobileRecordLogQueryDTO 参数
     * @return 查询结果
     */
    List<MobileRecordLogDO> listByPageParameterForMobile(@Param("mouthDate") String mouthDate, @Param("queryDTO") MobileRecordLogQueryDTO mobileRecordLogQueryDTO);
}