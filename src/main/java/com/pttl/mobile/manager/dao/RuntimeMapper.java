package com.pttl.mobile.manager.dao;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import com.pttl.mobile.manager.domain.po.Runtime;

import java.util.List;

@Repository
public interface RuntimeMapper {
    int deleteByPrimaryKey(String id);

    int insert(Runtime record);

    int insertSelective(Runtime record);

    Runtime selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(Runtime record);

    int updateByPrimaryKey(Runtime record);

    int isVersionExists(@Param("version") String version);

    List<Runtime> getRuntime();
}