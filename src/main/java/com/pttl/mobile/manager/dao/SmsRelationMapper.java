package com.pttl.mobile.manager.dao;

import com.pttl.mobile.manager.domain.entity.SmsRelationDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SmsRelationMapper {
    /**
     * delete by primary key
     * @param id primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long id);

    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(SmsRelationDO record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(SmsRelationDO record);

    /**
     * select by primary key
     * @param id primary key
     * @return object by primary key
     */
    SmsRelationDO selectByPrimaryKey(Long id);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(SmsRelationDO record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(SmsRelationDO record);

    int updateBatch(List<SmsRelationDO> list);

    int updateBatchSelective(List<SmsRelationDO> list);

    int batchInsert(@Param("list") List<SmsRelationDO> list);

    void deleteByPersonId(@Param("id") Long id);
}