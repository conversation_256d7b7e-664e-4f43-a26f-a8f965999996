package com.pttl.mobile.manager.dao;

import com.pttl.mobile.manager.domain.entity.SystemUserDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface SystemUserMapper {
    /**
     * delete by primary key
     *
     * @param id primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long id);

    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(SystemUserDO record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(SystemUserDO record);

    /**
     * select by primary key
     *
     * @param id primary key
     * @return object by primary key
     */
    SystemUserDO selectByPrimaryKey(Long id);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(SystemUserDO record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(SystemUserDO record);

    int updateBatch(List<SystemUserDO> list);

    int batchInsert(@Param("list") List<SystemUserDO> list);

    /**
     * 通过用户登陆名称获取
     *
     * @param loginName 登陆名称
     * @return 结果
     */
    SystemUserDO getByLoginName(@Param("loginName") String loginName);
}