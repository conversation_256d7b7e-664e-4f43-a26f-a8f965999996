package com.pttl.mobile.manager.dao;

import com.pttl.mobile.manager.domain.dto.ApplicationInfoForUserDTO;
import com.pttl.mobile.manager.domain.po.Application;
import com.pttl.mobile.manager.domain.dto.ApplicationExtendDTO;
import com.pttl.mobile.manager.mobile.dto.ApplicationInfoDTO;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ApplicationMapper {
    int deleteByPrimaryKey(String id);

    int insert(Application application);

    int insertSelective(Application application);

    Application selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(Application application);

    int updateByPrimaryKey(Application application);

    int isApplicationExists(@Param("idOrName") String idOrName);

    boolean isApplicationExistsForUpdate(@Param("name") String name, @Param("id") String id);

    List<ApplicationExtendDTO> getApplication();

    ApplicationExtendDTO getApplicationById(@Param("id") String id);

    int deleteByGroupIds(@Param("groupIds") List<String> groupIds);

    /**
     * 获取全部应用信息 条件可选
     *
     * @param name
     * @param applicationGroupId
     * @return
     */
    List<ApplicationExtendDTO> listAllApplication(@Param("name") String name,
                                                  @Param("applicationGroupId") String applicationGroupId);

    /**
     * 获取当前登录用户有权限应用列表
     *
     * @param userId 当前登录用户id
     * @return 登录用户有权限应用列表
     */
    List<ApplicationInfoDTO> infoListForMobile(@Param("userId") Long userId);

    /**
     * 获取用户已关联所有应用信息
     *
     * @param userId 用户id
     * @return 用户已关联所有应用信息
     */
    List<ApplicationInfoForUserDTO> userApplicationByUserId(Long userId);

    /**
     * 更新应用权重
     *
     * @param id                 应用id
     * @param applicationWeight  应用权重
     */
    void updateApplicationWeightById(@Param("id") String id, @Param("applicationWeight") Integer applicationWeight);
}