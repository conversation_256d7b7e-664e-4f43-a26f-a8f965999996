package com.pttl.mobile.manager.dao;

import com.pttl.mobile.manager.domain.dto.ExchangeDetailsDTO;
import com.pttl.mobile.manager.domain.dto.ExchangeStatisticsDTO;
import com.pttl.mobile.manager.domain.dto.StatisticsUnusedIosCodeQuantityDTO;
import com.pttl.mobile.manager.domain.dto.UserUsageStatisticsDTO;
import com.pttl.mobile.manager.domain.entity.IosCodeDO;
import com.pttl.mobile.manager.domain.request.IosCodePageRequest;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface IosCodeMapper {
    /**
     * delete by primary key
     *
     * @param id primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long id);

    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(IosCodeDO record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(IosCodeDO record);

    /**
     * select by primary key
     *
     * @param id primary key
     * @return object by primary key
     */
    IosCodeDO selectByPrimaryKey(Long id);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(IosCodeDO record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(IosCodeDO record);

    /**
     * 批量更新
     *
     * @param list record
     * @return int
     */
    int updateBatch(List<IosCodeDO> list);

    /**
     * 批量插入
     *
     * @param list record
     * @return int
     */
    int batchInsert(@Param("list") List<IosCodeDO> list);

    /**
     * 分页获取
     *
     * @param pageRequest 分页参数
     * @return 结果
     */
    List<IosCodeDO> listByPageParameter(IosCodePageRequest pageRequest);

    /**
     * 获取最近导入有效的一条数据
     *
     * @return 结果
     */
    IosCodeDO getLatestImportCode(@Param("appType") Integer appType);

    /**
     * 统计用户使用情况
     *
     * @return 结果列表
     */
    List<UserUsageStatisticsDTO> getCount();

    /**
     * 统计未使用情况
     *
     * @return 结果列表
     */
    List<UserUsageStatisticsDTO> unusedQuantity();

    /**
     * 兑换统计
     *
     * @return 结果
     */
    ExchangeStatisticsDTO getExchangeStatistics(@Param("appType") Integer appType);

    /**
     * 获取今天每小时的兑换量
     *
     * @param dateStr 当天时间开始时间
     * @return 结果
     */
    List<ExchangeDetailsDTO> hourList(@Param("dateStr") String dateStr, @Param("appType") Integer appType);

    /**
     * 近七天或者30天数据查询
     *
     * @param dateStr 当天日期时间
     * @return 结果
     */
    List<ExchangeDetailsDTO> daysList(@Param("dateStr") String dateStr, @Param("appType") Integer appType);

    /**
     * 近一年数据查询
     *
     * @param dateStr 上一年的本月份
     * @return 结果
     */
    List<ExchangeDetailsDTO> mouthsList(@Param("dateStr") String dateStr, @Param("appType") Integer appType);

    /**
     * 查询所有年份的
     *
     * @return 结果
     */
    List<ExchangeDetailsDTO> allList(@Param("appType") Integer appType);

    /**
     * 统计未使用数量
     *
     * @return 结果
     */
    List<StatisticsUnusedIosCodeQuantityDTO> statisticsGroupByAppType();
}