package com.pttl.mobile.manager.dao;

import com.pttl.mobile.manager.domain.dto.*;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ClientMapper {
    ReleaseClientDTO getReleaseLastVersion(@Param("type") Integer type, @Param("operating_system") Integer operating_system);

    ReleaseClientDTO getReleaseMallLastVersion(@Param("type") Integer type, @Param("operating_system") Integer operating_system);

    ReleaseClientDTO getReleaseHongmengLastVersion(@Param("type") Integer type, @Param("operating_system") Integer operating_system);

    List<RuntimeClientDTO> getRuntime();

    String getConfigurationValueByName(@Param("name") String name);

    List<ApplicationClientItemDTO> getApplication();

    List<ApplicationAdapterClientDTO> getApplicationAdapterByIds(@Param("ids") List<String> ids);

    List<SwaClientDTO> getSwa();
}