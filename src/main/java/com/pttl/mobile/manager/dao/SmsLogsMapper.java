package com.pttl.mobile.manager.dao;

import com.pttl.mobile.manager.domain.entity.SmsLogs;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface SmsLogsMapper {
    /**
     * delete by primary key
     *
     * @param id primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long id);

    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(SmsLogs record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(SmsLogs record);

    /**
     * select by primary key
     *
     * @param id primary key
     * @return object by primary key
     */
    SmsLogs selectByPrimaryKey(Long id);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(SmsLogs record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(SmsLogs record);

    int updateBatch(List<SmsLogs> list);

    int updateBatchSelective(List<SmsLogs> list);

    int batchInsert(@Param("list") List<SmsLogs> list);
}