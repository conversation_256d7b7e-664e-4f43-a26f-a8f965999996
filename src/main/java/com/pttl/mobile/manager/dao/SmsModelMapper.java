package com.pttl.mobile.manager.dao;

import com.pttl.mobile.manager.domain.entity.SmsModelDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SmsModelMapper {
    /**
     * delete by primary key
     * @param id primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long id);

    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(SmsModelDO record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(SmsModelDO record);

    /**
     * select by primary key
     * @param id primary key
     * @return object by primary key
     */
    SmsModelDO selectByPrimaryKey(Long id);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(SmsModelDO record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(SmsModelDO record);

    int updateBatch(List<SmsModelDO> list);

    int updateBatchSelective(List<SmsModelDO> list);

    int batchInsert(@Param("list") List<SmsModelDO> list);

    List<SmsModelDO> list();
}