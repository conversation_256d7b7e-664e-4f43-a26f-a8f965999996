package com.pttl.mobile.manager.dao;

import com.pttl.mobile.manager.domain.po.Configuration;
import com.pttl.mobile.manager.domain.po.ConfigurationKey;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ConfigurationMapper {
    int deleteByPrimaryKey(ConfigurationKey key);

    int insert(Configuration record);

    int insertSelective(Configuration record);

    Configuration selectByPrimaryKey(ConfigurationKey key);

    int updateByPrimaryKeySelective(Configuration record);

    int updateByPrimaryKey(Configuration record);

    List<Configuration> getConfiguration();

    Configuration getConfigurationByName(@Param("name") String name);

    int insertOrUpdate(Configuration setting);
}