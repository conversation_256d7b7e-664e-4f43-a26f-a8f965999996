package com.pttl.mobile.manager.dao;

import com.pttl.mobile.manager.domain.po.ApplicationAdapter;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ApplicationAdapterMapper {
    int deleteByPrimaryKey(String applicationId);

    int insert(ApplicationAdapter record);

    int insertSelective(ApplicationAdapter record);

    ApplicationAdapter selectByPrimaryKey(String applicationId);

    int updateByPrimaryKeySelective(ApplicationAdapter record);

    int updateByPrimaryKey(ApplicationAdapter record);

    int isApplicationAdapterExists(@Param("scope") String scope, @Param("applicationId") String applicationId);

    List<ApplicationAdapter> getByIds(@Param("ids") List<String> ids);
}