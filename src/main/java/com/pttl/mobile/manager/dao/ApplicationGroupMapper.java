package com.pttl.mobile.manager.dao;

import com.pttl.mobile.manager.domain.po.ApplicationGroup;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ApplicationGroupMapper {
    int deleteByPrimaryKey(String id);

    int insert(ApplicationGroup record);

    int insertSelective(ApplicationGroup record);

    ApplicationGroup selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(ApplicationGroup record);

    int updateByPrimaryKey(ApplicationGroup record);

    int isGroupExists(@Param("name") String name);

    List<ApplicationGroup> getApplicationGroup();
}