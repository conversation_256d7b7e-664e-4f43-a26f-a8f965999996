package com.pttl.mobile.manager.dao;

import com.pttl.mobile.manager.domain.dto.SwaExtendDTO;
import com.pttl.mobile.manager.domain.po.Swa;
import com.pttl.mobile.manager.domain.po.SwaKeeper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface SwaMapper {
    int deleteByPrimaryKey(String id);

    int insert(Swa record);

    int insertSelective(Swa record);

    Swa selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(Swa record);

    int updateByPrimaryKey(Swa record);

    int isSwaExists(@Param("applicationId") String applicationId);

    List<SwaExtendDTO> getSwa();

    Boolean update(@Param("swa") Swa swa, @Param("swaKeeper") SwaKeeper swaKeeper);

    Boolean delete(@Param("ids") List<String> ids);
}