package com.pttl.mobile.manager.dao;

import com.pttl.mobile.manager.domain.po.StrategyUser;

public interface StrategyUserMapper {
    int deleteByPrimaryKey(String id);

    int insert(StrategyUser record);

    int insertSelective(StrategyUser record);

    StrategyUser selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(StrategyUser record);

    int updateByPrimaryKey(StrategyUser record);
}