package com.pttl.mobile.manager.dao;

import com.pttl.mobile.manager.domain.dto.SmsPersonnelDetailsResponseDTO;
import com.pttl.mobile.manager.domain.dto.SmsPersonnelResponseDTO;
import com.pttl.mobile.manager.domain.entity.SmsPersonnelDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SmsPersonnelMapper {
    /**
     * delete by primary key
     * @param id primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long id);

    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(SmsPersonnelDO record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(SmsPersonnelDO record);

    /**
     * select by primary key
     * @param id primary key
     * @return object by primary key
     */
    SmsPersonnelDO selectByPrimaryKey(Long id);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(SmsPersonnelDO record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(SmsPersonnelDO record);

    int updateBatch(List<SmsPersonnelDO> list);

    int updateBatchSelective(List<SmsPersonnelDO> list);

    int batchInsert(@Param("list") List<SmsPersonnelDO> list);

    List<SmsPersonnelResponseDTO> listByCondition(@Param("name") String name, @Param("phoneNum") String phoneNum);

    List<SmsPersonnelResponseDTO> getNeedSendWarningSMSMessagesPhoneBySmsType(@Param("type") String type);

    SmsPersonnelDetailsResponseDTO detailsById(@Param("id") Long id);
}