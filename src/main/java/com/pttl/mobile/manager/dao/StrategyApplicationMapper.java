package com.pttl.mobile.manager.dao;

import com.pttl.mobile.manager.domain.po.StrategyApplication;

public interface StrategyApplicationMapper {
    int deleteByPrimaryKey(String id);

    int insert(StrategyApplication record);

    int insertSelective(StrategyApplication record);

    StrategyApplication selectByPrimaryKey(String id);

    int updateByPrimaryKeySelective(StrategyApplication record);

    int updateByPrimaryKey(StrategyApplication record);
}