package com.pttl.mobile.manager.dao;

import com.pttl.mobile.manager.domain.dto.ReleaseExtendDTO;
import com.pttl.mobile.manager.domain.entity.ReleaseHongmeng;
import com.pttl.mobile.manager.domain.po.Release;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@Mapper
public interface ReleaseHongmengMapper {
    /**
     * delete by primary key
     * @param id primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(String id);

    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(Release record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(ReleaseHongmeng record);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(ReleaseHongmeng record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(ReleaseHongmeng record);

    int updateBatch(List<ReleaseHongmeng> list);

    int updateBatchSelective(List<ReleaseHongmeng> list);

    int batchInsert(@Param("list") List<ReleaseHongmeng> list);

    List<ReleaseExtendDTO> getReleaseHistory(@Param("type") Integer type, @Param("version") String version, @Param("keyword") String keyword, @Param("start_date") Date start_date, @Param("end_date") Date end_date);

    int isVersionExists(@Param("type") Integer type, @Param("operating_system") Integer operating_system, @Param("version") String version);

    Release getReleaseLastVersion(@Param("type") Integer type, @Param("operating_system") Integer operating_system);
}