package com.pttl.mobile.manager.dao;

import com.pttl.mobile.manager.domain.dto.FrontLogsQueryDTO;
import com.pttl.mobile.manager.domain.dto.FrontLogsResultDTO;
import com.pttl.mobile.manager.domain.entity.MobileLogs;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface MobileLogsMapper {
    /**
     * delete by primary key
     * @param id primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Long id);

    /**
     * insert record to table
     * @param record the record
     * @return insert count
     */
    int insert(MobileLogs record);

    /**
     * insert record to table selective
     * @param record the record
     * @return insert count
     */
    int insertSelective(MobileLogs record);

    /**
     * select by primary key
     * @param id primary key
     * @return object by primary key
     */
    MobileLogs selectByPrimaryKey(Long id);

    /**
     * update record selective
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(MobileLogs record);

    /**
     * update record
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(MobileLogs record);

    int updateBatch(List<MobileLogs> list);

    int updateBatchSelective(List<MobileLogs> list);

    int batchInsert(@Param("list") List<MobileLogs> list);

    List<FrontLogsResultDTO> pageInfo(FrontLogsQueryDTO frontLogsQuery);
}