package com.pttl.mobile.manager.dao;

import com.pttl.mobile.manager.domain.dto.ApplicationDTO;
import com.pttl.mobile.manager.domain.entity.StrategyApplication2DO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface StrategyApplication2Mapper {
    /**
     * delete by primary key
     *
     * @param id primaryKey
     * @return deleteCount
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * insert record to table
     *
     * @param record the record
     * @return insert count
     */
    int insert(StrategyApplication2DO record);

    /**
     * insert record to table selective
     *
     * @param record the record
     * @return insert count
     */
    int insertSelective(StrategyApplication2DO record);

    /**
     * select by primary key
     *
     * @param id primary key
     * @return object by primary key
     */
    StrategyApplication2DO selectByPrimaryKey(Integer id);

    /**
     * update record selective
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKeySelective(StrategyApplication2DO record);

    /**
     * update record
     *
     * @param record the updated record
     * @return update count
     */
    int updateByPrimaryKey(StrategyApplication2DO record);

    int updateBatch(List<StrategyApplication2DO> list);

    int updateBatchSelective(List<StrategyApplication2DO> list);

    int batchInsert(@Param("list") List<StrategyApplication2DO> list);

    /**
     * 根据策略id获取关联应用
     *
     * @param strategyId
     * @return
     */
    List<ApplicationDTO> listByStrategyId(@Param("strategyId") Long strategyId);

    /**
     * 根据策略id删除
     *
     * @param strategyId
     */
    void deleteByStrategyId(@Param("strategyId") Long strategyId);
}