package com.pttl.mobile.manager.dao;

import com.pttl.mobile.manager.domain.entity.UserDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface UserMapper {
    int deleteByPrimaryKey(Long id);

    int insert(UserDO userDO);

    int insertSelective(UserDO userDO);

    UserDO selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(UserDO userDO);

    int updateByPrimaryKey(UserDO userDO);

    int updateBatch(List<UserDO> list);

    int updateBatchSelective(List<UserDO> list);

    int batchInsert(@Param("list") List<UserDO> list);

    /**
     * 分页获取用户信息
     *
     * @param name
     * @param departmentId
     * @return
     */
    List<UserDO> pageInfo(@Param("name") String name, @Param("departmentId") Long departmentId);

    /**
     * 根据用户登录名称来获取用户信息
     *
     * @param loginName
     * @return
     */
    UserDO selectByLoginName(@Param("loginName") String loginName);

    /**
     * 根据部门id 统计当前部门下人数
     *
     * @param departmentId 部门id
     * @return 数量
     */
    Integer countByDepartmentId(@Param("departmentId") Long departmentId);
}