<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="false" scan="false">
    <springProperty scop="context" name="spring.application.name" source="spring.application.name" defaultValue=""/>
    <property name="log.path" value="logs"/>

    <!-- Log file debug output -->
    <appender name="debug" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/debug.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/%d{yyyy-MM, aux}/debug.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>7</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%date [%thread] %-5level [%logger{50}] %file:%line - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- Log file error output -->
    <appender name="error" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/error.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/%d{yyyy-MM}/error.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>7</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%date [%thread] %-5level [%logger{50}] %file:%line - %msg%n</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
    </appender>

    <!-- 自定义日志文件 -->
    <appender name="mobile-log" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 可以是相对路径，可以是绝对路径 -->
        <file>${log.path}/mobile-log/mobile-record.log</file>
        <append>true</append>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/mobile-log/mobile-record.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>15</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%date [%thread] %-5level [%logger{50}] %file:%line - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- 自定义日志 多文件日志打印 -->
    <logger name="com.pttl.mobile.manager.mobile.controller.MobileRecordLogController" level="INFO" additivity="false">
        <appender-ref ref="mobile-log"/>
        <!-- 添加控制台打印 -->
        <appender-ref ref="debug"/>
    </logger>

    <!--<logger name="org.apache.commons" level="info" additivity="false">
        <appender-ref ref="debug" />
    </logger>

    <logger name="org.apache.commons.beanutils.converters" level="info" additivity="false">
        <appender-ref ref="debug" />
    </logger>

    <logger name="org.dozer" level="info" additivity="false">
        <appender-ref ref="debug" />
    </logger>

    <logger name="org.eclipse.jetty" level="info" additivity="false">
        <appender-ref ref="debug" />
    </logger>

    <logger name="com.ulisesbocchio" level="info" additivity="false">
        <appender-ref ref="debug" />
    </logger>-->
    <logger name="com.pttl.gather.thread" level="debug" additivity="false">
        <appender-ref ref="debug"/>
    </logger>
    <!-- Level: FATAL 0  ERROR 3  WARN 4  INFO 6  DEBUG 7 -->
    <root level="INFO">
        <appender-ref ref="debug"/>
        <appender-ref ref="error"/>
    </root>
</configuration>
