server:
  port: 3000
  session:
    # 设置session超时时间/秒
    timeout: 7200
    # 解决 加密会话（SSL）<PERSON><PERSON> 中缺少 Secure 属性
  # 解决 加密会话（SSL）<PERSON><PERSON> 中缺少 Secure 属性
  servlet:
    session:
      cookie:
        secure: true
        http-only: true

admin:
  userName: admin
  #  password: User@123
  password: TT%!AORhVcI9

# 跨域允许源
cors-filter:
  allowedOrigins: http://192.168.8.70,https://192.168.8.70,http://onlinemanager.cetctaili.com,http://onlinemanager.pttl.com

spring:
  session:
    store-type: redis
  datasource:
    connectionProperties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
    driverClassName: com.mysql.jdbc.Driver
    filters: stat,wall,slf4j
    initialSize: 10
    maxOpenPreparedStatements: 20
    maxPoolPreparedStatementPerConnectionSize: 20
    maxWait: 60000
    minEvictableIdleTimeMillis: 300000
    minIdle: 5
    maxActive: 30
    poolPreparedStatements: true
    testOnBorrow: false
    testOnReturn: false
    testWhileIdle: true
    timeBetweenEvictionRunsMillis: 60000
    type: com.alibaba.druid.pool.DruidDataSource
    url: *************************************************************************************************************************************************************************************************
    username: tl_office_wr
    password: uLAw#c#!VKWio6TI
    validationQuery: SELECT 1;
  redis:
    host: 127.0.0.1
    port: 6379
    timeout: 20000
    pool:
      max-active: 500
      min-idle: 0
      max-idle: 100
      max-wait: 60000
    database: 0
    password: v#7mbwejQQ

logging:
  level:
    root: info
    com.pttl.mobile.manager.dao: debug

file:
  path:
    shareRootPath: /home/<USER>/xiBel/manager/file
    uploadPath: upload
    fileSize: 10
    validFileSuffixes: jpg,swf,pdf,docx,doc,png,bmp,jpeg,gif,xlsx,zip,pma

deployUrl: https://b2bmobiletest.putiantaili.com:8005

swagger:
  enable: false

# excel模板 本地路径
excel-file:
  # 用户导入模板
  user-template-path: /home/<USER>/xiBel/manager/manager-console-1.0.0.SNAPSHOT/template/userTemplate.xlsx
  # ios二维码导入模板
  ios-code-template-path: /home/<USER>/xiBel/manager/manager-console-1.0.0.SNAPSHOT/template/iosCodeTemplate.xlsx

# 系统自定义参数
application-defined:
  # 系统配置
  system-manager:
    # 登陆开关 是否需要登陆
    login-switch: true
    # 登陆开关 是否需要验证码
    login-sms-switch: true
    # 应用图标访问地址前缀
    application_icon_url_prefix: http://onlinemanager.cetctaili.com/static

  # jwt相关配置 移动端token
  mobile-token:
    # 移动端 token过期时间/分钟
    token-expiration-time: 30
    # token key(密钥)
    jwt-key: e10adc3949ba59abbe56e057f20f883e

  # ios-qrcode 二维码有效时间
  ios-code:
    # 单位/分钟
    valid-time: 30

  # ihr web-service 通讯录接口参数
  ihr-address-list:
    # 地址前缀  http://ip:port
    url-prefix: https://tlihr.pttl.com
    # HPS_INF_ID
    info-id: 1111
    # HPS_INF_APP
    info-app: Portal
    # HPS_INF_NAME 查询数据的类型
    #info-name: HPS_INT_PER_QRY
    # HPS_INF_ACTION  操作类型 SELECT
    info-action: SELECT

  # 通讯录 组织架构
  mobile-address-book:
    # 组织架构(为了提高效率) 检索索引结果进行缓存,
    # 如果为空则表示当前时间到当天凌晨,单位/分钟
    # 需要参考定时任务: organizational-structure
    search-index-expire-time:
    # 头像公网访问地址前缀
    head-picture-visit-pre: http://im.cetctaili.com/hrService/images/
    # 移动端-我的 头像公网访问地址前缀
    head-picture-visit-pre-self: http://im.cetctaili.com/hrService/tl/api/query?id=

  # 移动端 第三方bpm登陆 cookie获取 相关配置参数
  bpm-cookie-client:
    # 密码aes加密密钥 iv
    aes-key: BPM_AES88AES_BPM
    aes-iv: BPM_AES88AES_BPM
    # 登陆地址
    url-address: http://bpm.pttl.com:8000/ptsoa/login/org.gocom.components.coframe.auth.login.login.flow
    # 登陆地址(鸿蒙)
    url-address-hm: http://**************:8080/login/org.gocom.components.coframe.auth.login.login.flow
    # 请求头参数 map
    header-map:
      content-type: application/x-www-form-urlencoded; charset=utf-8
    # 成功时状态码 默认为200
    success-status-code: 302
    # 需要获取的响应头 cookie名称
    cookie-name: Set-Cookie
    # rpa权限检查请求地址 测试环境
    rpa-url-address: http://apim.pttl.com/gateway/services/APIWS?WSDL
    # 人员项目管理 权限检查请求地址
    menu-name-url-address: http://bpm.pttl.com:8000/skins/default/org.gocom.components.coframe.auth.LoginManager.getMenuData.biz.ext

  # 移动端 第三方vcrm登陆 cookie获取 相关配置参数
  vcrm-cookie-client:
    # 登陆地址
    url-address: http://vcrm.pttl.com/crmapi/api-crm-facade/internalUser/login
    # 登陆地址(鸿蒙)
    url-address-hm: http://***********:30101/crmapi/api-crm-facade/internalUser/login
    # 需要获取的响应头 cookie名称
    cookie-name: Set-Cookie
    # vcrm账密加密公钥获取
    vcrm-public-key-url: http://apim.pttl.com/gateway/api-crm-facade/internalUser/queryVcrmPublickey

  # 移动端 第三方 通知相关配置参数
  inform-cookie-client:
    # 通知登陆地址
    inform-url-address: http://vcrm.pttl.com/crmapi/api-crm-facade/internalUser/login
    # 绑定极光推送地址
    aurora-url-address: http://vcrm.pttl.com/crmapi/api-crm-facade/internalUser/updateUserVirtualDevice
    # 获取通知类型地址
    type-url-address: http://vcrm.pttl.com/crmapi/api-crm-facade/notice/noticeTypeList
    # 获取通知列表地址
    list-url-address: http://vcrm.pttl.com/crmapi/api-crm-facade/notice/noticeList
    # 一键已读 地址
    once-read-url-address: http://vcrm.pttl.com/crmapi/api-crm-facade/notice/setAllNoticeReadState
    # 已读 地址
    read-url-address: http://vcrm.pttl.com/crmapi/api-crm-facade/notice/setNoticeReadState
    # 搜索 地址
    search-url-address: http://vcrm.pttl.com/crmapi/api-crm-facade/notice/searchNotice
    # BPM红点显示 地址
    red-dot-url-address: http://apim.pttl.com/gateway/CountNewsNotification
    # BPM红点显示请求头 X-TLSI-APPKEY属性值
    red-dot-header-value: MWU4YjI4OTdiYWY4NjkzYzZhODJjZGY1YzIyZTI4MGE=

  # 移动端 在途订单 相关配置参数
  in-transit-order:
    # 获取VCRM子账号地址
    vcrm-sub-account-url-address: http://vcrm.pttl.com/crmapi/api-crm-facade/internalUser/queryLoginVO
    # 获取中台key地址
    middle-ground-key-url-address: https://report.pttl.com/pttlbdrcApis/pttlbdrc-core/common/getKey
    # 获取UUID 地址
    uuid-url-address: https://report.pttl.com/pttlbdrcApis/pttlbdrc-core/common/thirdPartLogin
    # 获取登录token 地址(与上面uuid接口需要间隔两秒)
    login-token-url-address: https://report.pttl.com/pttlbdrcApis/pttlbdrc-core/common/thirdPartGetLoginToken


# elasticsearch配置参数
elasticsearch:
  # 如需要账密验证时配置, 可缺省
  es-user-name:
  # 如需要账密验证时配置, 可缺省
  es-password:
  # es 集群时配置多个
  host-list:
    - ***************:9200
  # 连接超时时间/毫秒
  connect-timeout: 6000
  # 连接超时时间/毫秒
  socket-timeout: 6000
  # 响应超时时间/毫秒
  connection-request-timeout: 15000

# 短信网关配置
sms:
  open: true
  smswsdl: http://sms.mobset.com:8080/Api?wsdl
  operID: 111048
  operLogin: pttlb2b
  operPass: 112412
  maxSize: 3
  smsValidHour: 8

# 定时任务配置
schedule:
  # 按照每月创建移动端日志表
  create:
    # 开关
    enabled: true
    # cron表达式 每月一号凌晨3分0秒执行
    cron: 5 0 0 1 * ?
    # 每月一号凌晨3分0秒执行
    # 0 3 0 1 * ?

  # 按照每月删除移动端日志表
  delete:
    # 开关
    enabled: true
    # 保留近几个月的 1为仅保留当月 2为2个月,3为3个月...
    quantity: 3
    # cron表达式 每月一号凌晨15分0秒执行
    cron: 0 15 0 1 * ?

  # 通讯录 组织架构 自动同步入库
  organizational-structure:
    # 开关
    enabled: true
    # cron表达式
    cron: 0 3 0 * * ?
  # 通讯录 部门信息 自动同步入库
  dept-info:
    # 开关
    enabled: true
    # cron表达式
    cron: 0 1 0 * * ?

  # 通讯录 人员信息 自动同步入库
  person:
    # 开关
    enabled: true
    # cron表达式
    cron: 0 5 0 * * ?
  # ios端 code码短信告警
  ios-code:
    # 开关
    enabled: true
    # cron表达式
    cron: 0 0 9 * * ?
    sms-gateway: http://apim-uat.pttl.com/gateway/sms/send

# 配置ios端 code码 分类配置 数字标识/文字标识
map-data:
  ios-code-type:
    电科太力: 1
    优服助手: 2