package com.pttl.mobile.manager.mobile.controller;

import com.alibaba.fastjson.JSONObject;
import com.pttl.mobile.manager.config.ApplicationDefinedConfiguration;
import com.pttl.mobile.manager.mobile.encrypt.DesUtils;
import com.pttl.mobile.manager.mobile.vo.IhrLoginVO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import static org.junit.jupiter.api.Assertions.*;

/**
 * IHR登录功能测试
 *
 * <AUTHOR>
 * @date 2025/01/08
 */
@SpringBootTest
public class LoginThirdPartyControllerTest {

    @Autowired
    private ApplicationDefinedConfiguration applicationDefinedConfiguration;

    @Test
    public void testDesEncryption() throws Exception {
        String desKey = "pptt-ys-ps";
        String originalUserId = "testUser";
        String originalPassword = "testPassword";
        
        // 测试DES加密
        String encryptedUserId = DesUtils.desEncrypt(originalUserId, desKey);
        String encryptedPassword = DesUtils.desEncrypt(originalPassword, desKey);
        
        assertNotNull(encryptedUserId);
        assertNotNull(encryptedPassword);
        assertNotEquals(originalUserId, encryptedUserId);
        assertNotEquals(originalPassword, encryptedPassword);
        
        System.out.println("Original UserId: " + originalUserId);
        System.out.println("Encrypted UserId: " + encryptedUserId);
        System.out.println("Original Password: " + originalPassword);
        System.out.println("Encrypted Password: " + encryptedPassword);
    }

    @Test
    public void testIhrLoginVOCreation() {
        IhrLoginVO ihrLoginVO = new IhrLoginVO();
        ihrLoginVO.setUserId("testUser");
        ihrLoginVO.setPassword("testPassword");
        
        assertEquals("testUser", ihrLoginVO.getUserId());
        assertEquals("testPassword", ihrLoginVO.getPassword());
    }

    @Test
    public void testRequestBodyConstruction() throws Exception {
        String desKey = "pptt-ys-ps";
        String userId = "testUser";
        String password = "testPassword";
        
        String encryptedUserId = DesUtils.desEncrypt(userId, desKey);
        String encryptedPassword = DesUtils.desEncrypt(password, desKey);
        
        // 构建请求参数
        JSONObject bodyJsonObject = new JSONObject();
        bodyJsonObject.put("Flag", "mobile");
        bodyJsonObject.put("Password", encryptedPassword);
        bodyJsonObject.put("UserId", encryptedUserId);

        JSONObject requestJsonObject = new JSONObject();
        requestJsonObject.put("Body", bodyJsonObject);
        
        String requestBody = requestJsonObject.toJSONString();
        
        assertNotNull(requestBody);
        assertTrue(requestBody.contains("Body"));
        assertTrue(requestBody.contains("Flag"));
        assertTrue(requestBody.contains("mobile"));
        
        System.out.println("Request Body: " + requestBody);
    }

    @Test
    public void testIhrLoginConfiguration() {
        // 测试配置是否正确加载
        ApplicationDefinedConfiguration.IhrLoginClient ihrLoginClient =
                applicationDefinedConfiguration.getIhrLoginClient();

        assertNotNull(ihrLoginClient, "IHR登录配置不能为空");
        assertNotNull(ihrLoginClient.getUrlAddress(), "IHR登录URL不能为空");
        assertNotNull(ihrLoginClient.getDesKey(), "DES密钥不能为空");
        assertNotNull(ihrLoginClient.getAppKey(), "APP KEY不能为空");
        assertNotNull(ihrLoginClient.getTokenExpirationTime(), "Token过期时间不能为空");

        System.out.println("IHR Login URL: " + ihrLoginClient.getUrlAddress());
        System.out.println("DES Key: " + ihrLoginClient.getDesKey());
        System.out.println("App Key: " + ihrLoginClient.getAppKey());
        System.out.println("Token Expiration Time: " + ihrLoginClient.getTokenExpirationTime() + "s");
    }

    @Test
    public void testDesEncryptionWithConfig() throws Exception {
        ApplicationDefinedConfiguration.IhrLoginClient ihrLoginClient =
                applicationDefinedConfiguration.getIhrLoginClient();

        String originalUserId = "testUser";
        String originalPassword = "testPassword";

        // 使用配置文件中的密钥进行加密
        String encryptedUserId = DesUtils.desEncrypt(originalUserId, ihrLoginClient.getDesKey());
        String encryptedPassword = DesUtils.desEncrypt(originalPassword, ihrLoginClient.getDesKey());

        assertNotNull(encryptedUserId);
        assertNotNull(encryptedPassword);
        assertNotEquals(originalUserId, encryptedUserId);
        assertNotEquals(originalPassword, encryptedPassword);

        System.out.println("Using config DES key: " + ihrLoginClient.getDesKey());
        System.out.println("Encrypted UserId: " + encryptedUserId);
        System.out.println("Encrypted Password: " + encryptedPassword);
    }
}
