package com.pttl.mobile.manager;

import com.pttl.mobile.manager.util.RSAUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.Date;

/**
 * <AUTHOR>
 * @description: 生成客户端Token
 * @date 2022/1/27 13:40
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class GenerateToken {
    @Autowired
    private RSAUtil rsaUtil;

    @Test
    public void encrypt() throws Exception {
        String token = rsaUtil.encrypt(new Date().getTime() + "|太力商城");
        log.info(token);
    }

    /**
     * 移动端测试用户名称/密码明文 加密
     *
     * @throws Exception
     */
    @Test
    public void mobilUser() throws Exception {
        String name = "test11";
        // 用户名base64编码
        String encodeName = Base64.getEncoder().encodeToString(name.getBytes(StandardCharsets.UTF_8));
        System.out.println(encodeName);
        String userName = new String(Base64.getDecoder().decode(encodeName), StandardCharsets.UTF_8);
        System.out.println(userName);

        // 密码使用rsa公钥加密  服务端使用私钥解密
        String pwd = "123456";
        String encrypt = new RSAUtil().encrypt(pwd);
        System.out.println(encrypt);
    }
}
