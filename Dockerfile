FROM 192.168.2.118:8082/openjdk:0.0.1
ENV LANG C.UTF-8

# manager-console-1.0.0.SNAPSHOT.jar
ENV APP_NAME=manager-console
ENV VERSION=1.0.0.SNAPSHOT
ENV PORT=9000
ENV APP_SOURCE=/application

RUN ln -snf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && echo Asia/Shanghai > /etc/timezone
RUN mkdir $APP_SOURCE

COPY target/$APP_NAME-$VERSION.jar $APP_SOURCE/$APP_NAME.jar
COPY target/classes/config/ $APP_SOURCE/

WORKDIR ${APP_SOURCE}
CMD java  -Xmx2G -Xms2G -Dfile.encoding=UTF-8 -jar $APP_SOURCE/$APP_NAME.jar --spring.config.location=$APP_SOURCE/
EXPOSE $PORT
