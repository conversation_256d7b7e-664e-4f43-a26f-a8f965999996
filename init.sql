/*
 Navicat Premium Data Transfer

 Source Server         : 220.176-test
 Source Server Type    : MySQL
 Source Server Version : 5.7.25
 Source Host           : ***************:3306
 Source Schema         : mobile_manager

 Target Server Type    : MySQL
 Target Server Version : 50725
 File Encoding         : 65001

 Date: 21/04/2023 10:07:11
*/
/*!40101 SET NAMES utf8 */;

/*!40101 SET SQL_MODE = ''*/;

/*!40014 SET @OLD_UNIQUE_CHECKS = @@UNIQUE_CHECKS, UNIQUE_CHECKS = 0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS = @@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS = 0 */;
/*!40101 SET @OLD_SQL_MODE = @@SQL_MODE, SQL_MODE = 'NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES = @@SQL_NOTES, SQL_NOTES = 0 */;
CREATE DATABASE /*!32312 IF NOT EXISTS */`sys_mobile_manager` /*!40100 DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci */;

USE `sys_mobile_manager`;

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for application
-- ----------------------------
DROP TABLE IF EXISTS `application`;
CREATE TABLE `application`
(
    `id`                   varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  NOT NULL COMMENT '应用id',
    `application_group_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  NOT NULL COMMENT '应用组id',
    `name`                 varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  NOT NULL COMMENT '应用名称',
    `description`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL     DEFAULT NULL COMMENT '应用描述',
    `logo_url`             varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '应用图标地址',
    `package_url`          varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL     DEFAULT NULL COMMENT '应用包上传地址',
    `package_name`         varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  NULL     DEFAULT NULL COMMENT '应用包的名称(包括扩展名)',
    `address`              varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '在线应用,适配应用地址',
    `inner_address`        varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL     DEFAULT NULL COMMENT '应用内网IP地址',
    `type`                 tinyint(3)                                                    NOT NULL DEFAULT 1 COMMENT '应用类型(1:在线应用; 2: 适配应用;), 不同的类型有可能存在子表',
    `is_visible`           tinyint(1)                                                    NULL     DEFAULT 1 COMMENT '应用状态(0:隐藏 1:显示)',
    `create_date`          datetime                                                      NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `last_update`          timestamp                                                     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `version`              varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  NULL     DEFAULT NULL COMMENT '唯一id 每次更新和新建都会变',
    `system_platform`      tinyint(3) UNSIGNED                                           NOT NULL COMMENT '外键 当前数据生效的系统平台',
    `env`                  tinyint(3)                                                    NOT NULL DEFAULT 1 COMMENT '环境标识 1:正式环境  2:预生产环境 用户发版测试',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '应用表'
  ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for application_adapter
-- ----------------------------
DROP TABLE IF EXISTS `application_adapter`;
CREATE TABLE `application_adapter`
(
    `application_id`           varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '应用id',
    `scope`                    varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'studio中为适配包设置的唯一名称',
    `manifest`                 json                                                         NOT NULL COMMENT 'manifest文件内容',
    `runtime_primary_version`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '适配包对应的运行时主版本号',
    `runtime_version`          varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '适配包对应的运行时具体版本',
    `is_specific_runtime_used` tinyint(1) UNSIGNED                                          NOT NULL DEFAULT 0 COMMENT '是否使用指定的运行时（0：不是，如果有新的运行时，客户端需要更新；1：是，不进行更新）',
    `last_update`              timestamp                                                    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间戳',
    `system_platform`          tinyint(3) UNSIGNED                                          NOT NULL COMMENT '外键 当前数据生效的系统平台',
    `env`                      tinyint(3)                                                   NOT NULL DEFAULT 1 COMMENT '环境标识 1:正式环境  2:预生产环境 用户发版测试',
    PRIMARY KEY (`application_id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT = '适配应用信息表'
  ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for application_group
-- ----------------------------
DROP TABLE IF EXISTS `application_group`;
CREATE TABLE `application_group`
(
    `id`              varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '应用组id',
    `name`            varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '应用组名称',
    `create_date`     datetime                                                     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `last_update`     timestamp                                                    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `is_default`      tinyint(1)                                                   NOT NULL DEFAULT 0 COMMENT '分组类型 1.默认  0标准',
    `weight`          tinyint(3)                                                   NULL     DEFAULT NULL COMMENT '排序级别 越小排在越前面',
    `system_platform` tinyint(3) UNSIGNED                                          NOT NULL COMMENT '外键 当前数据生效的系统平台',
    `env`             tinyint(3)                                                   NOT NULL DEFAULT 1 COMMENT '环境标识 1:正式环境  2:预生产环境 用户发版测试',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT = '应用组'
  ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for configuration
-- ----------------------------
DROP TABLE IF EXISTS `configuration`;
CREATE TABLE `configuration`
(
    `name`            varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '配置名称 Key',
    `description`     varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '配置说明',
    `value`           json                                                          NULL COMMENT '配置值',
    `system_platform` tinyint(3) UNSIGNED                                           NOT NULL COMMENT '外键 当前数据生效的系统平台',
    `env`             tinyint(3)                                                    NOT NULL DEFAULT 1 COMMENT '环境标识 1:正式环境  2:预生产环境 用户发版测试',
    PRIMARY KEY (`name`, `system_platform`, `env`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '配置表'
  ROW_FORMAT = DYNAMIC;
-- 初始化默认值
INSERT INTO `configuration` (`name`, `description`, `value`, `system_platform`, `env`)
VALUES ('onlinePreview',
        '安全配置-在线预览(false不开启，true开启,setting: {type//0saas,1private,url:地址,format:格式,previewMode:0普通1高速};)', '{
    \"value\": true,
    \"setting\": {
      \"url\": \"http://yz.tlmall.com:8080\",
      \"format\": [
        \"doc\",
        \"eio\"
      ]
    }
  }', 1, 1);
INSERT INTO `configuration` (`name`, `description`, `value`, `system_platform`, `env`)
VALUES ('onlinePreview',
        '安全配置-在线预览(false不开启，true开启,setting: {type//0saas,1private,url:地址,format:格式,previewMode:0普通1高速};)', '{
    \"value\": false,
    \"setting\": {
      \"url\": \"http://yz.tlmall.com:8080\",
      \"format\": []
    }
  }', 1, 2);
INSERT INTO `configuration` (`name`, `description`, `value`, `system_platform`, `env`)
VALUES ('onlinePreview',
        '安全配置-在线预览(false不开启，true开启,setting: {type//0saas,1private,url:地址,format:格式,previewMode:0普通1高速};)', '{
    \"value\": false,
    \"setting\": {
      \"url\": \"http://yz.tlmall.com:8080\",
      \"format\": []
    }
  }', 2, 1);
INSERT INTO `configuration` (`name`, `description`, `value`, `system_platform`, `env`)
VALUES ('onlinePreview',
        '安全配置-在线预览(false不开启，true开启,setting: {type//0saas,1private,url:地址,format:格式,previewMode:0普通1高速};)', '{
    \"value\": false,
    \"setting\": {
      \"url\": \"http://yz.tlmall.com:8080\",
      \"format\": []
    }
  }', 2, 2);


-- ----------------------------
-- Table structure for mm_department
-- ----------------------------
DROP TABLE IF EXISTS `mm_department`;
CREATE TABLE `mm_department`
(
    `id`                   bigint(11)                                                    NOT NULL AUTO_INCREMENT COMMENT '部门ID',
    `name`                 varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '部门名称',
    `description`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '部门描述',
    `parent_department_id` bigint(11)                                                    NOT NULL DEFAULT 0 COMMENT '父部门ID, 0为根',
    `source`               tinyint(2) UNSIGNED                                           NOT NULL DEFAULT 1 COMMENT '数据来源(1: 手动创建; 2: 批量导入; 3: 定制导入; 4: AD导入)',
    `full_department_id`   varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '部门全路径(记录当前目录的所有父级ID)',
    `user_device_num`      tinyint(2)                                                    NOT NULL DEFAULT 3 COMMENT '部门内用户最大设备数, (暂且保留)',
    `device_switch_status` tinyint(3)                                                    NOT NULL DEFAULT 1 COMMENT '是否开启最大设备限制数,暂且保留',
    `valid_status`         tinyint(2) UNSIGNED                                           NOT NULL DEFAULT 0 COMMENT '是否删除(0:未删除; 1: 已删除, 默认0:未删除)',
    `create_time`          datetime                                                      NULL     DEFAULT NULL COMMENT '创建时间',
    `update_time`          datetime                                                      NULL     DEFAULT NULL COMMENT '更新时间',
    `remark`               varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL     DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `idx_department_full_department_id` (`full_department_id`) USING BTREE COMMENT '部门全路径的索引',
    INDEX `idx_department_name` (`name`) USING BTREE COMMENT '部门名称索引',
    INDEX `idx_parent_department_id` (`parent_department_id`) USING BTREE COMMENT '部门父id索引'
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '部门表'
  ROW_FORMAT = DYNAMIC;
-- 初始化默认值
INSERT INTO `mm_department` (`id`, `name`, `description`, `parent_department_id`, `source`, `full_department_id`,
                             `user_device_num`, `device_switch_status`, `valid_status`, `create_time`, `update_time`,
                             `remark`)
VALUES (56, '根策略组织', '默认的组织部门根, 默认组织部门', 0, 1, NULL, 3, 1, 0, '2023-04-23 10:45:35', '2023-04-23 10:45:39', NULL);


-- ----------------------------
-- Table structure for mm_functional_attributes
-- ----------------------------
DROP TABLE IF EXISTS `mm_functional_attributes`;
CREATE TABLE `mm_functional_attributes`
(
    `id`                    bigint(11)                                                    NOT NULL AUTO_INCREMENT COMMENT '主键',
    `operating_system`      tinyint(2)                                                    NULL DEFAULT NULL COMMENT '操作系统 1-Android  2-IOS 3-Android&IOS',
    `env`                   tinyint(2)                                                    NULL DEFAULT NULL COMMENT '(暂不使用该字段)环境标识 1:正式环境  2:测试环境 用户发版测试',
    `data_type`             tinyint(3)                                                    NOT NULL COMMENT '属于哪种数据类型: 1:环境参数 2:Webview拦截系统加载地址 3:系统应用描述（/）区分 ; 后续可自己定义',
    `functional_attributes` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL DEFAULT NULL COMMENT '功能属性id',
    `attribute_value`       varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '属性值',
    `create_time`           datetime                                                      NULL DEFAULT NULL COMMENT '创建时间',
    `update_time`           datetime                                                      NULL DEFAULT NULL COMMENT '更新时间',
    `remark`                varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  NULL DEFAULT NULL COMMENT '功能属性描述, 例:消息推送：http://mpttl.com',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '系统参数(属性配置)表'
  ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for mm_ios_code
-- ----------------------------
DROP TABLE IF EXISTS `mm_ios_code`;
CREATE TABLE `mm_ios_code`
(
    `id`           bigint(11)                                                    NOT NULL AUTO_INCREMENT COMMENT '主键',
    `code`         varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  NULL DEFAULT NULL COMMENT '代码',
    `code_link`    varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '链接',
    `valid_status` tinyint(2)                                                    NULL DEFAULT 0 COMMENT '是否有效: 0 : 有效, 1 : 无效',
    `import_time`  datetime                                                      NULL DEFAULT NULL COMMENT '导入时间',
    `failure_time` datetime                                                      NULL DEFAULT NULL COMMENT '失效时间, 当状态为无效时, 这里才会有值',
    `remark`       varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  NULL DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT = 'ios QRCode (ios二维码数据导入)'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for mm_manager_operation_log
-- ----------------------------
DROP TABLE IF EXISTS `mm_manager_operation_log`;
CREATE TABLE `mm_manager_operation_log`
(
    `id`                       bigint(11)                                                    NOT NULL AUTO_INCREMENT COMMENT '主键',
    `operation_module`         varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  NULL     DEFAULT NULL COMMENT '功能模块',
    `operation_status`         tinyint(2)                                                    NOT NULL DEFAULT 1 COMMENT '当前操作成功与否状态, 0: 异常, 1:成功',
    `operation_type`           varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  NULL     DEFAULT NULL COMMENT '操作类型',
    `operation_description`    varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL     DEFAULT NULL COMMENT '操作描述',
    `operation_request_param`  text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci         NULL COMMENT '请求参数',
    `operation_response_param` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci         NULL COMMENT '返回参数',
    `exception_name`           varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL     DEFAULT NULL COMMENT '异常名称, 当操作成功状态为异常时存在值',
    `exception_message`        text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci         NULL COMMENT '异常信息, 当操作成功状态为异常时存在值',
    `operation_user_id`        bigint(11)                                                    NULL     DEFAULT NULL COMMENT '操作员id',
    `operation_user_name`      varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  NULL     DEFAULT NULL COMMENT '操作员名称',
    `operation_method`         varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL     DEFAULT NULL COMMENT '操作方法',
    `operation_uri`            varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL     DEFAULT NULL COMMENT '请求URI',
    `operation_ip`             varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  NULL     DEFAULT NULL COMMENT '请求ip',
    `create_time`              datetime                                                      NULL     DEFAULT NULL COMMENT '创建时间',
    `remark`                   varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  NULL     DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 10000
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT = '管理端 操作日志表'
  ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for mm_mobil_address_book_extend
-- ----------------------------
DROP TABLE IF EXISTS `mm_mobil_address_book_extend`;
CREATE TABLE `mm_mobil_address_book_extend`
(
    `id`              bigint(11)                                                    NOT NULL AUTO_INCREMENT,
    `person_info_id`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  NULL DEFAULT NULL COMMENT '关联通讯录员工id(employee_id)',
    `user_remark`     varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  NULL DEFAULT NULL COMMENT '人员备注',
    `custom_avatar`   varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '自定义头像',
    `reserved_field1` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  NULL DEFAULT NULL COMMENT '预留字段',
    `reserved_field2` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  NULL DEFAULT NULL COMMENT '预留字段2',
    `reserved_field3` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  NULL DEFAULT NULL COMMENT '预留字段3',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `person_info_id_index` (`person_info_id`) USING BTREE COMMENT '员工id'
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT = '移动端-通讯录扩展字段 (我方服务端单独保留-非同步)'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for mm_mobile_record_log
-- ----------------------------
DROP TABLE IF EXISTS `mm_mobile_record_log`;
CREATE TABLE `mm_mobile_record_log`
(
    `id`                bigint(11)                                                   NOT NULL AUTO_INCREMENT COMMENT '主键',
    `app_version`       varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'app版本',
    `description`       varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '描述',
    `login_name`        varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '登陆名称',
    `login_time`        datetime                                                     NULL DEFAULT NULL COMMENT '登陆时间',
    `model`             varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '系统版本',
    `module`            varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '模块',
    `occur_time`        datetime                                                     NULL DEFAULT NULL COMMENT '发生时间',
    `platform`          varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '平台 在线商城/在线办公',
    `source`            varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '手机系统 Android/IOS',
    `sub_module`        varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '子模块',
    `system_login_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '系统登陆名称',
    `time_used`         int(8)                                                       NULL DEFAULT NULL COMMENT '使用次数',
    `type`              varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '类型 Info/Error/success',
    `remark`            varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备注',
    `extension`         varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '扩展字段',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT = '移动端业务操作记录日志表'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for mm_mobile_record_log_2023_2
-- ----------------------------
DROP TABLE IF EXISTS `mm_mobile_record_log_2023_2`;
CREATE TABLE `mm_mobile_record_log_2023_2`
(
    `id`                bigint(11)                                                   NOT NULL AUTO_INCREMENT COMMENT '主键',
    `app_version`       varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'app版本',
    `description`       varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '描述',
    `login_name`        varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '登陆名称',
    `login_time`        datetime                                                     NULL DEFAULT NULL COMMENT '登陆时间',
    `model`             varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '系统版本',
    `module`            varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '模块',
    `occur_time`        datetime                                                     NULL DEFAULT NULL COMMENT '发生时间',
    `platform`          varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '平台 在线商城/在线办公',
    `source`            varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '手机系统 Android/IOS',
    `sub_module`        varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '子模块',
    `system_login_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '系统登陆名称',
    `time_used`         int(8)                                                       NULL DEFAULT NULL COMMENT '使用次数',
    `type`              varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '类型 Info/Error/success',
    `remark`            varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备注',
    `extension`         varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '扩展字段',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT = '移动端业务操作记录日志表'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for mm_mobile_record_log_2023_3
-- ----------------------------
DROP TABLE IF EXISTS `mm_mobile_record_log_2023_3`;
CREATE TABLE `mm_mobile_record_log_2023_3`
(
    `id`                bigint(11)                                                   NOT NULL AUTO_INCREMENT COMMENT '主键',
    `app_version`       varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'app版本',
    `description`       varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '描述',
    `login_name`        varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '登陆名称',
    `login_time`        datetime                                                     NULL DEFAULT NULL COMMENT '登陆时间',
    `model`             varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '系统版本',
    `module`            varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '模块',
    `occur_time`        datetime                                                     NULL DEFAULT NULL COMMENT '发生时间',
    `platform`          varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '平台 在线商城/在线办公',
    `source`            varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '手机系统 Android/IOS',
    `sub_module`        varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '子模块',
    `system_login_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '系统登陆名称',
    `time_used`         int(8)                                                       NULL DEFAULT NULL COMMENT '使用次数',
    `type`              varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '类型 Info/Error/success',
    `remark`            varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备注',
    `extension`         varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '扩展字段',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 100
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT = '移动端业务操作记录日志表'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for mm_mobile_record_log_2023_4
-- ----------------------------
DROP TABLE IF EXISTS `mm_mobile_record_log_2023_4`;
CREATE TABLE `mm_mobile_record_log_2023_4`
(
    `id`                bigint(11)                                                   NOT NULL AUTO_INCREMENT COMMENT '主键',
    `app_version`       varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'app版本',
    `description`       varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '描述',
    `login_name`        varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '登陆名称',
    `login_time`        datetime                                                     NULL DEFAULT NULL COMMENT '登陆时间',
    `model`             varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '系统版本',
    `module`            varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '模块',
    `occur_time`        datetime                                                     NULL DEFAULT NULL COMMENT '发生时间',
    `platform`          varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '平台 在线商城/在线办公',
    `source`            varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '手机系统 Android/IOS',
    `sub_module`        varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '子模块',
    `system_login_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '系统登陆名称',
    `time_used`         int(8)                                                       NULL DEFAULT NULL COMMENT '使用次数',
    `type`              varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '类型 Info/Error/success',
    `remark`            varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备注',
    `extension`         varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '扩展字段',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT = '移动端业务操作记录日志表'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for mm_strategy
-- ----------------------------
DROP TABLE IF EXISTS `mm_strategy`;
CREATE TABLE `mm_strategy`
(
    `id`           bigint(11)                                                    NOT NULL AUTO_INCREMENT COMMENT '策略id',
    `name`         varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '策略名称',
    `description`  varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '描述',
    `weight`       tinyint(5) UNSIGNED                                           NOT NULL COMMENT '权重',
    `platform`     tinyint(3) UNSIGNED                                           NOT NULL DEFAULT 0 COMMENT '平台(1:PC  2:移动 3:pad)',
    `current_step` tinyint(3) UNSIGNED                                           NOT NULL DEFAULT 1 COMMENT '1: 基本信息; 2: 功能配置; 3: 应用配置; 4: 用户配置',
    `is_deleted`   tinyint(3) UNSIGNED                                           NOT NULL DEFAULT 0 COMMENT '是否删除',
    `create_date`  datetime                                                      NULL     DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `last_update`  datetime                                                      NULL     DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `remark`       varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL     DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `fk_strategy_platform_idx` (`platform`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '策略组'
  ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for mm_strategy_application
-- ----------------------------
DROP TABLE IF EXISTS `mm_strategy_application`;
CREATE TABLE `mm_strategy_application`
(
    `id`             int(10) UNSIGNED                                             NOT NULL AUTO_INCREMENT,
    `strategy_id`    bigint(11)                                                   NOT NULL COMMENT '策略组id',
    `application_id` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '应用id(如果选了部分二级应用需要包括父应用id)',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `fk_strategy_application_strategy_idx` (`strategy_id`) USING BTREE,
    INDEX `fk_strategy_application_application_idx` (`application_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 1000
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '策略组应用关联表'
  ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for mm_strategy_department
-- ----------------------------
DROP TABLE IF EXISTS `mm_strategy_department`;
CREATE TABLE `mm_strategy_department`
(
    `id`            int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
    `strategy_id`   bigint(11)       NOT NULL COMMENT '策略id',
    `department_id` bigint(11)       NOT NULL COMMENT '部门id',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `fk_strategy_department_strategy_idx` (`strategy_id`) USING BTREE,
    INDEX `fk_strategy_department_department_idx` (`department_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 100
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '策略与部门关联表'
  ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for mm_strategy_user
-- ----------------------------
DROP TABLE IF EXISTS `mm_strategy_user`;
CREATE TABLE `mm_strategy_user`
(
    `id`          int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键',
    `strategy_id` bigint(11)       NOT NULL COMMENT '策略id',
    `user_id`     bigint(11)       NOT NULL COMMENT '用户id',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `fk_strategy_user_strategy_idx` (`strategy_id`) USING BTREE,
    INDEX `fk_strategy_user_user_idx` (`user_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 100
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '策略组与用户关联表'
  ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for mm_system_user
-- ----------------------------
DROP TABLE IF EXISTS `mm_system_user`;
CREATE TABLE `mm_system_user`
(
    `id`           bigint(11)                                                    NOT NULL AUTO_INCREMENT COMMENT '用户ID',
    `name`         varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '用户姓名',
    `password`     varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL     DEFAULT NULL COMMENT '用户密码',
    `email`        varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '用户email',
    `mobile`       varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL     DEFAULT NULL COMMENT '手机',
    `login_name`   varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户登录账户',
    `avatar_path`  varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '头像路径',
    `employee_no`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL     DEFAULT NULL COMMENT '员工工号',
    `person_level` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL     DEFAULT NULL COMMENT '人员级别',
    `locality`     varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '工作地点',
    `telephone`    varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL     DEFAULT NULL COMMENT '办公电话',
    `source`       tinyint(3) UNSIGNED                                           NOT NULL DEFAULT 1 COMMENT '数据来源(1: 手动创建; 2: 批量导入; 3: 定制导入; 4: AD导入)',
    `valid_status` tinyint(2) UNSIGNED                                           NOT NULL DEFAULT 0 COMMENT '是否删除(0:未删除; 1: 已删除, 默认0:未删除)',
    `create_time`  datetime                                                      NULL     DEFAULT NULL COMMENT '创建时间',
    `update_time`  datetime                                                      NULL     DEFAULT NULL COMMENT '更新时间',
    `remark`       varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL     DEFAULT NULL COMMENT '备注',
    `extension1`   varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL     DEFAULT NULL COMMENT '扩展字段',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '管理端系统用户表'
  ROW_FORMAT = Dynamic;

-- 插入必要数据
INSERT INTO `mm_system_user` (`id`, `name`, `password`, `email`, `mobile`, `login_name`, `avatar_path`, `employee_no`,
                              `person_level`, `locality`, `telephone`, `source`, `valid_status`, `create_time`,
                              `update_time`, `remark`, `extension1`)
VALUES (1, '管理员', 'User@123', '<EMAIL>', '13552827981', 'admin', NULL, NULL, NULL, NULL, NULL, 1, 0, NULL, NULL,
        NULL, NULL);


-- ----------------------------
-- Table structure for mm_user
-- ----------------------------
DROP TABLE IF EXISTS `mm_user`;
CREATE TABLE `mm_user`
(
    `id`                          bigint(11)                                                    NOT NULL AUTO_INCREMENT COMMENT '用户ID',
    `department_id`               bigint(11)                                                    NOT NULL COMMENT '用户所属部门id',
    `name`                        varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NOT NULL COMMENT '用户姓名',
    `password`                    varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL     DEFAULT NULL COMMENT '用户密码',
    `email`                       varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '用户email',
    `mobile`                      varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL     DEFAULT NULL COMMENT '手机',
    `login_name`                  varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户登录账户',
    `position_name`               varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '岗位名称',
    `modify_password_wrong_count` tinyint(2) UNSIGNED                                           NOT NULL DEFAULT 0 COMMENT '密码修改错误次数',
    `modify_password_wrong_time`  datetime                                                      NULL     DEFAULT NULL COMMENT '密码修改错误时间',
    `avatar_path`                 varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '头像路径',
    `employee_no`                 varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL     DEFAULT NULL COMMENT '员工工号',
    `person_level`                varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL     DEFAULT NULL COMMENT '人员级别',
    `locality`                    varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT '工作地点',
    `telephone`                   varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL     DEFAULT NULL COMMENT '办公电话',
    `source`                      tinyint(3) UNSIGNED                                           NOT NULL DEFAULT 1 COMMENT '数据来源(1: 手动创建; 2: 批量导入; 3: 定制导入; 4: AD导入)',
    `valid_status`                tinyint(2) UNSIGNED                                           NOT NULL DEFAULT 0 COMMENT '是否删除(0:未删除; 1: 已删除, 默认0:未删除)',
    `activated_status`            tinyint(2) UNSIGNED                                           NOT NULL DEFAULT 0 COMMENT '是否已经激活(0:未激活, 1:已激活)',
    `activated_time`              datetime                                                      NULL     DEFAULT NULL COMMENT '激活时间',
    `disabled_status`             tinyint(2) UNSIGNED                                           NOT NULL DEFAULT 0 COMMENT '是否被禁用(0:未禁用,1:已禁用)',
    `last_login_time`             datetime                                                      NULL     DEFAULT NULL COMMENT '最近登录时间',
    `failed_login_times`          tinyint(4)                                                    NOT NULL DEFAULT 0 COMMENT '失败认证次数',
    `device_num`                  tinyint(2)                                                    NOT NULL DEFAULT 3 COMMENT '部门内用户最大设备数, (暂且保留)',
    `device_switch_status`        tinyint(3)                                                    NOT NULL DEFAULT 1 COMMENT '是否开启最大设备限制数, (暂且保留)',
    `temp_device_num`             tinyint(3)                                                    NOT NULL DEFAULT 0 COMMENT '用户临时设备数',
    `temp_device_num_expire_time` datetime                                                      NULL     DEFAULT NULL COMMENT '用户临时设备数失效时间',
    `create_time`                 datetime                                                      NULL     DEFAULT NULL COMMENT '创建时间',
    `update_time`                 datetime                                                      NULL     DEFAULT NULL COMMENT '更新时间',
    `ad_dn`                       varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL     DEFAULT NULL COMMENT 'ad域中的distinguishName属性',
    `remark`                      varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL     DEFAULT NULL COMMENT '备注',
    `extension1`                  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL     DEFAULT NULL COMMENT '扩展字段1',
    `extension2`                  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL     DEFAULT NULL COMMENT '扩展字段2',
    `extension3`                  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci  NULL     DEFAULT NULL COMMENT '扩展字段3',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `idx_user_department_idx` (`department_id`) USING BTREE,
    INDEX `idx_user_email` (`email`) USING BTREE COMMENT 'email索引',
    INDEX `idx_user_login_name` (`login_name`) USING BTREE COMMENT 'login_name索引',
    INDEX `idx_user_mobile` (`mobile`) USING BTREE COMMENT 'mobile索引'
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_general_ci COMMENT = '用户表'
  ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for mm_ws_dept_info
-- ----------------------------
DROP TABLE IF EXISTS `mm_ws_dept_info`;
CREATE TABLE `mm_ws_dept_info`
(
    `id`                      bigint(11)                                                    NOT NULL AUTO_INCREMENT,
    `dept_abbreviation_short` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  NULL DEFAULT NULL COMMENT '部门简称',
    `manager_id`              varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  NULL DEFAULT NULL COMMENT '部门经理ID',
    `serv_att_abbreviation`   varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  NULL DEFAULT NULL COMMENT '业务属性',
    `eff_status`              varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  NULL DEFAULT NULL,
    `dept_full_name`          varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '部门全称',
    `sequence`                int(8)                                                        NULL DEFAULT NULL,
    `dept_cost`               varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  NULL DEFAULT NULL COMMENT '成本承担部门ID',
    `dept_sort_abbreviation`  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  NULL DEFAULT NULL COMMENT '部门类别',
    `dept_count`              varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  NULL DEFAULT NULL,
    `row_number`              int(8)                                                        NULL DEFAULT NULL,
    `dept_hie_abbreviation`   varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  NULL DEFAULT NULL COMMENT '部门层级',
    `dept_id`                 varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  NULL DEFAULT NULL COMMENT '部门ID',
    `company_abbreviation`    varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  NULL DEFAULT NULL COMMENT '部门所属公司',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT = '通讯录-部门信息- 暂时没用'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for mm_ws_organizational_structure_info
-- ----------------------------
DROP TABLE IF EXISTS `mm_ws_organizational_structure_info`;
CREATE TABLE `mm_ws_organizational_structure_info`
(
    `id`                         bigint(11)                                                   NOT NULL AUTO_INCREMENT,
    `superior_dept_abbreviation` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '上级部门简称',
    `part_deptId_chn`            int(11)                                                      NULL DEFAULT NULL COMMENT '上级部门ID',
    `dept_id`                    int(11)                                                      NULL DEFAULT NULL COMMENT '部门ID',
    `row_number`                 int(8)                                                       NULL DEFAULT NULL,
    `dept_abbreviation`          varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '部门简称',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `index_part_deptId_chn` (`part_deptId_chn`) USING BTREE,
    INDEX `index_dept_id` (`dept_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 100
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT = '通讯录-组织架构信息'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for mm_ws_person_info
-- ----------------------------
DROP TABLE IF EXISTS `mm_ws_person_info`;
CREATE TABLE `mm_ws_person_info`
(
    `id`                  bigint(11)                                                    NOT NULL AUTO_INCREMENT,
    `company`             varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  NULL DEFAULT NULL COMMENT '机构ID',
    `email_address`       varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  NULL DEFAULT NULL COMMENT '邮箱地址',
    `name`                varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  NULL DEFAULT NULL COMMENT '用户姓名',
    `birthdate`           varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  NULL DEFAULT NULL COMMENT '出生日期',
    `dept_abbreviation`   varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  NULL DEFAULT NULL COMMENT '所属部门',
    `personnel_dept`      varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  NULL DEFAULT NULL COMMENT '用户岗位',
    `person_count`        int(8)                                                        NULL DEFAULT NULL,
    `position_nbr`        varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  NULL DEFAULT NULL COMMENT '岗位ID',
    `phone1`              varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  NULL DEFAULT NULL COMMENT '座机号码',
    `phone`               varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  NULL DEFAULT NULL COMMENT '联系电话',
    `employee_rcd`        int(8)                                                        NULL DEFAULT NULL COMMENT '员工记录号',
    `hr_status`           varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  NULL DEFAULT NULL COMMENT '用户状态',
    `job_level`           varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  NULL DEFAULT NULL COMMENT '员工职级ID',
    `punch_type`          varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  NULL DEFAULT NULL COMMENT '考勤类型',
    `address`             varchar(256) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
    `sex`                 varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  NULL DEFAULT NULL COMMENT '性别 M男  F女',
    `employee_id`         varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  NULL DEFAULT NULL COMMENT '员工id',
    `row_number`          int(8)                                                        NULL DEFAULT NULL COMMENT 'rowNumber',
    `dept_id`             int(11)                                                       NULL DEFAULT NULL COMMENT '所属部门id',
    `company_description` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  NULL DEFAULT NULL COMMENT '用户所属机构',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `index_dept_id` (`dept_id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 1000
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT = '通讯录-人员信息'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for mm_ws_person_sort_info
-- ----------------------------
DROP TABLE IF EXISTS `mm_ws_person_sort_info`;
CREATE TABLE `mm_ws_person_sort_info`
(
    `id`          int(11)                                                      NOT NULL AUTO_INCREMENT,
    `dept_id`     int(11)                                                      NULL DEFAULT NULL COMMENT '部门id',
    `employee_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '员工id',
    `sort_id`     int(5)                                                       NULL DEFAULT NULL COMMENT '排序 顺序数字, 数字越小越靠前; 0开始',
    `update_time` datetime                                                     NULL DEFAULT NULL COMMENT '更新时间',
    `remark`      varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 10
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT = '自定义用户排序表'
  ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for operating_system
-- ----------------------------
DROP TABLE IF EXISTS `operating_system`;
CREATE TABLE `operating_system`
(
    `id`   tinyint(3) UNSIGNED                                          NOT NULL AUTO_INCREMENT COMMENT 'IOS：1； Android：2',
    `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'IOS或者Android或者其他...',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT = '操作系统表'
  ROW_FORMAT = DYNAMIC;

INSERT INTO `operating_system` (`id`, `name`) VALUES (1, 'Android');
INSERT INTO `operating_system` (`id`, `name`) VALUES (2, 'IOS');

-- ----------------------------
-- Table structure for release
-- ----------------------------
DROP TABLE IF EXISTS `release`;
CREATE TABLE `release`
(
    `id`               varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  NOT NULL COMMENT '发布版本id',
    `version`          varchar(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  NULL     DEFAULT NULL COMMENT '发布的版本',
    `download_url`     varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL     DEFAULT NULL COMMENT '下载地址',
    `system_platform`  tinyint(3) UNSIGNED                                           NOT NULL COMMENT '外键 发版的系统平台',
    `type`             tinyint(3) UNSIGNED                                           NOT NULL COMMENT '外键 更新的类型 客户端或者适配包',
    `operating_system` tinyint(3) UNSIGNED                                           NULL     DEFAULT NULL COMMENT '外键 操作系统 Android或者IOS',
    `is_forced`        tinyint(1)                                                    NULL     DEFAULT 0 COMMENT '是否强制更新',
    `release_date`     datetime                                                      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '发布版本的日期',
    `description`      varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL     DEFAULT NULL COMMENT '更新内容',
    `env`              tinyint(3)                                                    NOT NULL DEFAULT 1 COMMENT '环境标识 1:正式环境  2:预生产环境 用户发版测试',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `idx_version` (`version`) USING BTREE COMMENT '发布版本索引'
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT = '发布版本表'
  ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for release_test
-- ----------------------------
DROP TABLE IF EXISTS `release_test`;
CREATE TABLE `release_test`
(
    `id`               varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  NOT NULL COMMENT '发布版本id',
    `version`          varchar(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  NULL     DEFAULT NULL COMMENT '发布的版本',
    `download_url`     varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL     DEFAULT NULL COMMENT '下载地址',
    `system_platform`  tinyint(3) UNSIGNED                                           NOT NULL COMMENT '外键 发版的系统平台',
    `type`             tinyint(3) UNSIGNED                                           NOT NULL COMMENT '外键 更新的类型 客户端或者适配包',
    `operating_system` tinyint(3) UNSIGNED                                           NULL     DEFAULT NULL COMMENT '外键 操作系统 Android或者IOS',
    `is_forced`        tinyint(1)                                                    NULL     DEFAULT 0 COMMENT '是否强制更新',
    `release_date`     datetime                                                      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '发布版本的日期',
    `description`      varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL     DEFAULT NULL COMMENT '更新内容',
    `env`              tinyint(3)                                                    NOT NULL DEFAULT 1 COMMENT '环境标识 1:正式环境  2:预生产环境 用户发版测试',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `idx_version` (`version`) USING BTREE COMMENT '发布版本索引'
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT = '发布版本表'
  ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for release_type
-- ----------------------------
DROP TABLE IF EXISTS `release_type`;
CREATE TABLE `release_type`
(
    `id`   tinyint(3) UNSIGNED                                          NOT NULL AUTO_INCREMENT COMMENT '客户端更新：1； 适配包更新：2',
    `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '客户端更新或者适配包更新或者其他...',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT = '发版类型表'
  ROW_FORMAT = DYNAMIC;

INSERT INTO `release_type` (`id`, `name`)
VALUES (1, '客户端更新');
INSERT INTO `release_type` (`id`, `name`)
VALUES (2, '适配包内容更新');


-- ----------------------------
-- Table structure for runtime
-- ----------------------------
DROP TABLE IF EXISTS `runtime`;
CREATE TABLE `runtime`
(
    `id`              varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  NOT NULL,
    `version`         varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  NOT NULL COMMENT '运行时版本',
    `description`     varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL     DEFAULT NULL COMMENT '描述信息',
    `package_name`    varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  NOT NULL COMMENT '运行时包的名称',
    `package_url`     varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '运行时上传地址',
    `create_date`     datetime                                                      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `system_platform` tinyint(3) UNSIGNED                                           NOT NULL COMMENT '外键 当前数据生效的系统平台',
    `env`             tinyint(3)                                                    NOT NULL DEFAULT 1 COMMENT '环境标识 1:正式环境  2:预生产环境 用户发版测试',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT = '运行时表'
  ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for swa
-- ----------------------------
DROP TABLE IF EXISTS `swa`;
CREATE TABLE `swa`
(
    `id`                      varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  NOT NULL COMMENT 'id',
    `application_id`          varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  NOT NULL COMMENT '应用id',
    `type`                    tinyint(3) UNSIGNED                                           NOT NULL DEFAULT 1 COMMENT 'swa类型(1: 密码管家; 2: AD代理认证)',
    `login_type`              tinyint(3) UNSIGNED                                           NOT NULL DEFAULT 1 COMMENT '用户登录方式(1: 统一认证; 2: 用户自定义)',
    `login_field`             tinyint(3) UNSIGNED                                           NOT NULL COMMENT '登录字段(1: 用户登录名; 2: 邮箱; 3: 邮箱前缀; 4: 手机号)',
    `url`                     varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL     DEFAULT NULL COMMENT '登录地址或者host地址',
    `is_customized_char_used` tinyint(1)                                                    NOT NULL DEFAULT 0 COMMENT '是否存在自定义前缀或者后缀(0: 否; 1: 是)',
    `prefix`                  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  NULL     DEFAULT NULL COMMENT '前缀',
    `suffix`                  varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  NULL     DEFAULT NULL COMMENT '后缀',
    `create_date`             datetime                                                      NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `last_update`             timestamp                                                     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间戳',
    `system_platform`         tinyint(3) UNSIGNED                                           NOT NULL COMMENT '外键 当前数据生效的系统平台',
    `env`                     tinyint(3)                                                    NOT NULL DEFAULT 1 COMMENT '环境标识 1:正式环境  2:预生产环境 用户发版测试',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT = '密码代填'
  ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for swa_keeper
-- ----------------------------
DROP TABLE IF EXISTS `swa_keeper`;
CREATE TABLE `swa_keeper`
(
    `swa_id`             varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci  NOT NULL COMMENT 'swa外键',
    `username_xpath`     varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户名的xpath',
    `password_xpath`     varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '密码的xpath',
    `login_button_xpath` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '登录按钮的xpath',
    `login_page_type`    tinyint(4)                                                    NOT NULL DEFAULT 1 COMMENT '页面登录类型（1：常规登录；2：iframe嵌套登录）',
    `iframe_xpath`       varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL     DEFAULT NULL COMMENT 'iframe的xpath',
    PRIMARY KEY (`swa_id`) USING BTREE
) ENGINE = InnoDB
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT = 'SWA - 密码管家'
  ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for system_platform
-- ----------------------------
DROP TABLE IF EXISTS `system_platform`;
CREATE TABLE `system_platform`
(
    `id`   tinyint(3) UNSIGNED                                          NOT NULL AUTO_INCREMENT COMMENT '在线办公平台：1； 太力商城：2',
    `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '在线办公平台或者太力商城或者其他...',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB
  AUTO_INCREMENT = 1
  CHARACTER SET = utf8mb4
  COLLATE = utf8mb4_unicode_ci COMMENT = '发版平台表'
  ROW_FORMAT = DYNAMIC;

INSERT INTO `system_platform` (`id`, `name`)
VALUES (1, '在线办公平台');
INSERT INTO `system_platform` (`id`, `name`)
VALUES (2, '太力商城');

-- ----------------------------
-- Function structure for queryChildrenDeptInfo
-- ----------------------------
DROP FUNCTION IF EXISTS `queryChildrenDeptInfo`;
delimiter ;;
CREATE FUNCTION `queryChildrenDeptInfo`(deptId INT)
    RETURNS varchar(6000) CHARSET utf8mb4 COLLATE utf8mb4_unicode_ci
BEGIN
    DECLARE sTemp VARCHAR(6000);
    DECLARE sTempChd VARCHAR(6000);

    SET sTemp = '$';
    SET sTempChd = CAST(deptId AS CHAR);

    WHILE sTempChd IS NOT NULL
        DO
            SET sTemp = CONCAT(sTemp, ',', sTempChd);
            SELECT GROUP_CONCAT(dept_id)
            INTO sTempChd
            FROM mm_ws_organizational_structure_info
            WHERE FIND_IN_SET(part_deptId_chn, sTempChd) > 0;
        END WHILE;
    RETURN sTemp;
END
;;
delimiter ;

SET FOREIGN_KEY_CHECKS = 1;
