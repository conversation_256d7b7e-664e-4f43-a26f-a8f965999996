# IHR登录接口文档

## 接口概述
新增的IHR登录接口，用于调用第三方IHR系统进行用户认证，成功后生成token并存储到Redis缓存中。

## 接口信息
- **接口路径**: `/mobile/tp/ihrLogin`
- **请求方法**: POST
- **Content-Type**: application/json

## 请求参数

### 请求体 (IhrLoginVO)
```json
{
    "userId": "用户ID",
    "password": "用户密码"
}
```

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| userId | String | 是 | IHR系统用户ID |
| password | String | 是 | IHR系统用户密码 |

## 响应格式

### 成功响应
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "token": "生成的token ID",
        "ihrLoginResult": {
            "PunchType": "100",
            "Email": "<EMAIL>",
            "UserId": "加密后的用户ID",
            "ResultMsg": "",
            "ResultCode": "0"
        }
    }
}
```

### 失败响应
```json
{
    "code": 500,
    "message": "IHR登录失败: 错误信息",
    "data": null
}
```

## 实现细节

### 1. DES加密
- 使用DES算法对用户ID和密码进行加密
- 加密密钥: 从配置文件动态获取 (`application-defined.ihr-login-client.des-key`)
- 加密模式: `DES/ECB/PKCS7Padding`
- 编码方式: Base64

### 2. 第三方接口调用
- **目标URL**: 从配置文件动态获取 (`application-defined.ihr-login-client.url-address`)
- **请求方法**: POST
- **请求头**:
  - `X-TLSI-APPKEY`: 从配置文件动态获取 (`application-defined.ihr-login-client.app-key`)
  - `Content-Type`: `application/json`

### 3. 请求体格式
```json
{
    "Body": {
        "Flag": "mobile",
        "Password": "DES加密后的密码",
        "UserId": "DES加密后的用户ID"
    }
}
```

### 4. Token生成和存储
- 生成唯一的token ID (UUID)
- 将token信息存储到Redis
- Redis Key格式: `ihr:login:token:{tokenId}`
- 过期时间: 从配置文件动态获取 (`application-defined.ihr-login-client.token-expiration-time`)
- 存储内容包括:
  - tokenId: token唯一标识
  - userId: 用户ID
  - email: 用户邮箱
  - punchType: 打卡类型
  - loginTime: 登录时间戳
  - source: 来源标识 ("IHR")

## 配置文件示例

在 `application-test45.yml` 中添加以下配置：

```yaml
application-defined:
  # IHR登录 相关配置参数
  ihr-login-client:
    # IHR登录接口地址
    url-address: https://easybus-uat.tlmall.com:8083/HRLoginCheck
    # DES加密密钥
    des-key: pptt-ys-ps
    # 请求头 X-TLSI-APPKEY 值
    app-key: OTIzNWVmYzhmMWJiZWMxNGZmMDI1NzI1YzAzYzEwMTc=
    # token过期时间（秒）默认30分钟
    token-expiration-time: 1800
```

## 使用示例

### cURL请求示例
```bash
curl -X POST http://localhost:8080/mobile/tp/ihrLogin \
  -H "Content-Type: application/json" \
  -d '{
    "userId": "testUser",
    "password": "testPassword"
  }'
```

### JavaScript请求示例
```javascript
fetch('/mobile/tp/ihrLogin', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({
        userId: 'testUser',
        password: 'testPassword'
    })
})
.then(response => response.json())
.then(data => {
    if (data.code === 200) {
        console.log('登录成功，token:', data.data.token);
        // 可以将token存储到localStorage或sessionStorage
        localStorage.setItem('ihrToken', data.data.token);
    } else {
        console.error('登录失败:', data.message);
    }
});
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 400 | 参数错误 (用户ID或密码为空) |
| 500 | IHR登录失败或系统异常 |

## 注意事项

1. 用户ID和密码会在服务端进行DES加密，客户端无需预先加密
2. 生成的token会自动存储到Redis缓存中，有效期可通过配置文件调整
3. 接口会记录详细的日志信息，便于问题排查
4. 第三方IHR接口返回的ResultCode为"0"表示成功，其他值表示失败
5. 所有配置参数都可以通过配置文件动态调整，无需修改代码
6. 不同环境可以使用不同的配置文件，便于环境管理
